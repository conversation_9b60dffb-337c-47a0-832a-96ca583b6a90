# Flyway 数据库迁移脚本目录结构与命名规范

本规范用于指导项目中 Flyway 数据库迁移脚本的目录结构组织及命名方式，包括基座、插件和插件实例等不同层级的脚本管理。

---

## 一、目录结构与示例

### 1. 基座脚本（由主程序统一管理）

```plaintext
src/
└── main/
    └── resources/
        └── db/
            └── core/  # 由基座的 Flyway 实例扫描
                └── migration/  # 由基座的 Flyway 实例扫描
                    ├── V[大版本].[小版本].[脚本编号]__core_[脚本用途].sql
```

### 示例
```plaintext
src/
└── main/
    └── resources/
        └── db/
            └── core/  # 由基座的 Flyway 实例扫描
                └── migration/
                    ├── V1.1.0__core_table_init.sql          # 初始化核心表结构
                    ├── V1.1.1__core_table_init.sql          # 表结构补充
                    ├── V1.1.2__core_init_data.sql           # 初始化基础数据
                    ├── V1.1.3__core_init_user_data.sql      # 初始化默认用户数据
```

### 2. 业务插件（插件各自维护）
```plaintext
[插件名称]/
└── src/
    └── main/
        └── resources/
            └── db/
                └── 插件名称/  # 插件实例的 Flyway 扫描根目录
                    └── migration/  # 插件实例的 Flyway 扫描根目录
                        ├── V[大版本].[小版本].[脚本编号]__[实例插件名称]_[脚本用途].sql
```

### 示例
```plaintext
tcs-south-cmcc/
└── src/
    └── main/
        └── resources/
            └── db/
                └── 插件名称/  # 插件实例的 Flyway 扫描根目录
                    └── migration/
                        ├── V1.1.0__tcs-south-cmcc_table_init.sql       # 实例表结构初始化
                        ├── V1.1.1__tcs-south-cmcc_table_init.sql       # 表结构补充
                        ├── V1.1.2__tcs-south-cmcc_init_data.sql        # 初始化数据
                        ├── V1.1.3__tcs-south-cmcc_init_role_data.sql   # 初始化角色权限
```
### 3. 插件 Flyway 配置

每个需要独立管理数据库迁移的插件，都需要配置自己的 Flyway 实例。这通常在插件的配置类中完成，例如 `FlywayConfig.java`。

关键配置项包括：

*   **数据源 (DataSource):** 明确指定插件使用的数据源。如果系统中存在多个数据源，需要使用 `@Qualifier` 注解指定正确的 Bean 名称。
*   **迁移脚本位置 (locations):** 设置 Flyway 扫描 SQL 迁移脚本的路径。规范要求插件将脚本放置在 `classpath:db/migration/[插件名称]` 目录下，例如 `classpath:db/migration/seed` 或 `classpath:db/migration/cmcc`。
*   **历史记录表 (table):** 为避免与基座或其他插件冲突，插件应配置独立的 Flyway 历史记录表。表名建议使用 `[插件名称]_schema_history` 格式，例如 `seed_schema_history`。
*   **依赖关系 (DependsOn):** 确保 Flyway Bean 在其依赖的数据源 Bean 初始化之后再创建。

**示例 (`tcs-south-seed` 插件的 `FlywayConfig.java`):**

```java
package com.siteweb.tcs.south.seed.config;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

@Configuration(value = "seedFlywayConfig")
public class FlywayConfig {

    @Bean(name = "seedFlyway")
    @DependsOn("seedDataSource") // 确保在数据源之后初始化
    public Flyway flyway(@Qualifier("seedDataSource") DataSource dataSource) { // 指定数据源
        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/seed/migration") // 指定脚本位置
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("seed_schema_history") // 指定历史表名
            .load();

        flyway.migrate();

        return flyway;
    }
}
```

### 4. 版本号使用
```plaintext
大版本：主程序版本号
小版本：插件版本号
脚本编号：自增序号，生成版本号时检查是否与已有版本号冲突，版本号从当前最大版本号开始递增
若无特别说明大版本以及小版本号，默认从脚本编号最大版本号开始递增
```