代码检视要求：

输出格式：

的每一项检查结果，需包括：
1. 文件位置和行号
2. 问题简明描述
3. 与规范冲突说明
4. 解决方案建议

检查项：

1.项目模块设计与结构检查

• 模块设计：检查模块划分是否合理，是否遵循分层设计原则，是否存在模块之间的高耦合或低内聚问题。

• 命名规范：检查模块、包、类、方法和变量的命名是否清晰、规范，是否符合项目约定。

• 依赖关系：检查模块间的依赖关系是否合理，是否存在循环依赖或过度依赖问题。


2.Maven 依赖检查

• 检查依赖是否完整，是否存在重复依赖、过时依赖或冲突依赖。

• 检查依赖范围是否合理（如`compile`、`test`、`provided`等）。

• 检查是否有不必要的依赖。


3.目录结构与文件命名规范

• 检查目录结构是否清晰，是否符合约定的项目架构。

• 检查文件命名是否规范，是否与功能或模块相关。


4.代码书写不整洁问题

• 列出代码书写不整洁的文件，按复杂度从大到小排序（前10个）。

• 分析问题（如缩进不一致、变量命名混乱、代码冗长等），并提出改进建议。


5.逻辑不清晰的代码

• 列出逻辑不清晰的文件，按文件长度从大到小排序（前10个）。

• 分析问题（如嵌套过深、逻辑跳跃、缺乏注释等），并提出改进建议。


6.设计模式与最佳实践

• 列出违反单一职责、开闭原则等设计模式的文件（前10个）。

• 分析问题（如类职责过多、代码重复等），并提出改进建议。


7.废弃代码清理

• 检查未使用或无效的类、方法、注释、配置等，至少列出10处。

• 分析问题，并提出清理建议。


8.代码风格一致性

• 列出同类代码实现逻辑和风格不一致的地方（前10处）。

• 分析问题（如命名不一致、缩进不一致等），并提出统一风格的建议。


9.内存泄露与 Bug

• 列出可能存在内存泄露的代码（前5处）。

• 分析问题（如资源未释放、线程未正确关闭等），并提出修复建议。


10.性能问题

• 列出性能问题（如高复杂度算法、数据库查询效率低等）的代码（前5处）。

• 分析问题，并提出优化建议。


11.命名与拼写问题

• 列出命名不规范或拼写错误的代码（前10处）。

• 分析问题，并提出改进建议。


12.安全问题

• 列出不安全的代码（如 SQL 注入、未验证的输入、硬编码敏感信息等）（前10处）。

• 分析问题，并提出改进建议。


13.集群与云部署支持

• 检查代码是否支持集群和云部署（如负载均衡、分布式事务等）。

• 列出不符合要求的代码（前10处），并提出改进建议。


14.Akka 最佳实践

• 检查是否遵循 Akka 框架的最佳实践（如消息传递、Actor 模型等）。

• 列出不符合要求的代码（前10处），并提出改进建议。


15.日志与异常处理

• 分析日志和异常处理的设计是否统一和全面。

• 列出需要改进的地方（前10处），并提出优化建议。


16.容器化部署方案

• 分析代码是否支持容器化部署（如 Docker、Kubernetes）。

• 提出最佳实践建议（至少10处）。


17.埋点与可观测性

• 检查埋点和可观测机制（如监控、日志、指标等）。

• 提出改进建议（至少10处）。


18.数据库支持

• 分析是否支持多种数据库（如 PostgreSQL、MySQL、openGauss）。

• 如果未完全实现，列出需要改进的地方（前10处），并提供实现设计和样例。


19.多架构支持

• 检查是否支持 ARM 和 x86 编译打包。

• 列出不符合要求的代码（前10处），并提出改进建议。


20.文档管理

• 检查版本管理、README、忽略文件（.gitignore）、更新日志、API 文档、测试规范等是否完善。

• 提出改进建议（至少10处）。


21.测试方案

• 检查测试方案是否全面（功能测试、性能测试、UI 测试、API 测试等）。

• 提出改进建议（至少10处）。


22.测试用例生成

• 根据软件说明和测试方案，生成新的测试用例（至少10个不同类型）。


23.插件化机制

• 检查插件化机制设计是否合理，开发是否规范。

• 提出改进建议。


24.国际化支持

• 检查是否支持国际化（如多语言切换、资源文件抽取等）。

• 提出完善机制，并列出需要改进的文件（前10个）。

//样例：进行代码审查，检查1-3项，输出40个问题