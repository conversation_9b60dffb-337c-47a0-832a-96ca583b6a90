import type { RouteRecordRaw } from "vue-router";
import type { SystemConfig } from "./config";
import { addPluginRoutes } from "@/router/utils";

// 声明全局插件注册对象类型
export interface MicroFrontendRegistry {
  registerRoute: (routeConfig: RouteRecordRaw) => void;
  registerRoutes: (routeConfigs: RouteRecordRaw[]) => void;
  getRouteModules: () => RouteRecordRaw[];
}

// 存储所有插件注册的路由模块
const pluginRouteModules: RouteRecordRaw[] = [];

// 声明全局对象
declare global {
  interface Window {
    microFrontendRegistry: MicroFrontendRegistry;
    APP?: SystemConfig;
  }
}

/**
 * 初始化微前端插件注册功能
 */
export function initPluginRegistry() {
  // 创建全局注册对象
  const globalRegistry: MicroFrontendRegistry = {
    // 注册单个路由
    registerRoute: routeConfig => {
      try {
        // 将路由配置添加到插件路由模块集合中
        pluginRouteModules.push(routeConfig);
        // 使用新方法处理和添加路由
        addPluginRoutes([routeConfig]);
        console.log("[Plugin] 注册路由", routeConfig);
      } catch (error) {
        console.error("[Plugin] 注册路由失败", error);
      }
    },

    // 注册路由数组
    registerRoutes: routeConfigs => {
      try {
        // 将路由配置数组添加到插件路由模块集合中
        pluginRouteModules.push(...routeConfigs);
        // 使用新方法处理和添加路由，替代原来的addAsyncRoutes
        addPluginRoutes(routeConfigs);
        console.log("[Plugin] 批量注册路由模块，数量:", routeConfigs.length);
      } catch (error) {
        console.error("[Plugin] 批量注册路由失败", error);
      }
    },

    // 获取所有插件路由模块
    getRouteModules: () => {
      return pluginRouteModules;
    }
  };

  // 将注册函数挂载到全局
  window.microFrontendRegistry = globalRegistry;
}

/**
 * 获取所有插件注册的路由模块
 * @returns 插件路由模块数组
 */
export function getPluginRouteModules(): RouteRecordRaw[] {
  return pluginRouteModules;
}
