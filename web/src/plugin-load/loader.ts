/**
 * 动态加载外部插件
 * @param pluginUrl 插件的JS文件URL
 * @returns Promise
 */

export function loadExternalPlugin(pluginUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // 创建script标签
      const script = document.createElement("script");
      script.src = pluginUrl;
      script.async = true;

      // 加载成功的处理函数
      script.addEventListener("load", () => {
        resolve();
      });

      // 加载失败的处理函数
      script.onerror = () => {
        reject(new Error(`插件加载失败: ${pluginUrl}`));
      };

      // 添加到文档中
      document.head.append(script);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 加载插件样式
 * @param cssUrl 插件的CSS文件URL
 * @returns Promise
 */
export function loadPluginStyle(cssUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // 创建link标签
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = cssUrl;

      // 加载成功的处理函数
      link.addEventListener("load", () => {
        resolve();
      });

      // 加载失败的处理函数
      link.onerror = () => {
        reject(new Error(`插件样式加载失败: ${cssUrl}`));
      };

      // 添加到文档中
      document.head.append(link);
    } catch (error) {
      reject(error);
    }
  });
}
