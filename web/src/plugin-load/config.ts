import { loadExternalPlugin, loadPluginStyle } from "./loader";

/**
 * 插件配置接口
 */
export interface PluginConfig {
  /** 插件唯一标识，可选 */
  id?: string;
  /** 插件脚本资源数组 */
  scripts: string[];
  /** 插件样式资源数组 */
  styles: string[];
}

/**
 * 系统配置接口
 */
export interface SystemConfig {
  /** 系统设置 */
  settings: {
    /** 系统标题 */
    title: string;
    /** 系统logo */
    logo: string;
    /** 网站图标 */
    favicon: string;
    /** 支持的语言 */
    languages: string[];
    /** 路由配置，可选 */
    routes?: any;
  };
  /** 插件配置 */
  plugins: PluginConfig;
}

/**
 * 应用插件配置
 */
export const APP_PLUGINS: PluginConfig = {
  scripts: [],
  styles: []
};

/**
 * 设置插件配置
 * @param plugins 插件配置
 */
export function setPlugins(plugins: PluginConfig): void {
  APP_PLUGINS.scripts = [...plugins.scripts];
  APP_PLUGINS.styles = [...plugins.styles];
}

/**
 * 加载所有插件资源
 * @returns Promise
 */
export async function loadAllPlugins(): Promise<void> {
  try {
    // 从全局配置加载插件
    await loadPlugins(APP_PLUGINS);
  } catch (error) {
    throw error;
  }
}

/**
 * 加载插件配置
 * @param plugins 插件配置
 * @returns Promise
 */
export async function loadPlugins(plugins: PluginConfig): Promise<void> {
  try {
    // 先加载样式资源
    const stylePromises = plugins.styles.map(url => {
      // 处理URL：如果是完整URL（http/https）直接使用，否则创建相对路径
      const processedUrl = url.startsWith("http://") || url.startsWith("https://") 
        ? url 
        : (url.startsWith("/") ? url : `/${url}`);
      return loadPluginStyle(processedUrl);
    });
    await Promise.all(stylePromises);

    // 再加载脚本资源
    const scriptPromises = plugins.scripts.map(url => {
      // 处理URL：如果是完整URL（http/https）直接使用，否则创建相对路径
      const processedUrl = url.startsWith("http://") || url.startsWith("https://") 
        ? url 
        : (url.startsWith("/") ? url : `/${url}`);
      return loadExternalPlugin(processedUrl);
    });
    await Promise.all(scriptPromises);
  } catch (error) {
    throw error;
  }
}

/**
 * 从后端获取插件配置并加载
 * @param configUrl 配置URL
 * @returns Promise
 */
export async function loadPluginsFromRemote(configUrl: string): Promise<void> {
  try {
    // 从远程获取配置
    const response = await fetch(configUrl);
    if (!response.ok) {
      throw new Error(`获取插件配置失败: ${response.statusText}`);
    }

    const config: SystemConfig = await response.json();

    // 设置插件配置
    if (config.plugins) {
      setPlugins(config.plugins);
      // 加载插件
      await loadAllPlugins();
    }
  } catch (error) {
    throw error;
  }
}
