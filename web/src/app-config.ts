import { merge } from "lodash";
import type { SystemConfig } from "./plugin-load/config";

const DEFAULT_CONFIG: SystemConfig = {
  settings: {
    title: "Thing Connect Server",
    logo: "assets/img/icon-spring-boot-admin.svg",
    favicon: "assets/img/favicon.png",
    languages: ["en-US", "zh-CN"]
  },
  plugins: {
    scripts: [],
    styles: []
  }
};

// 本地开发配置
const LOCAL_CONFIG: SystemConfig = {
  settings: {
    title: "Thing Connect Server (本地模式)",
    logo: "assets/img/icon-spring-boot-admin.svg",
    favicon: "assets/img/favicon.png",
    languages: ["en-US", "zh-CN"]
  },
  plugins: {
    scripts: ["http://*************:3000/index.js"],
    styles: ["http://*************:3000/index.css"]
  }
};

// 根据模式选择配置源
let mergedConfig: SystemConfig;

const isDev = true;

if (isDev) {
  console.log("🚀 应用已启动本地模式");
  // 本地模式：使用本地配置
  mergedConfig = merge({}, DEFAULT_CONFIG, LOCAL_CONFIG);
} else {
  console.log("🌐 应用使用网络配置模式");
  // 网络模式：合并默认配置和window.APP中的配置
  mergedConfig = merge({}, DEFAULT_CONFIG, window.APP || {});
}

export default mergedConfig;
