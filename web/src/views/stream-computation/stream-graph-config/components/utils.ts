// 自定义Socket位置计算 - 简化版本，避免依赖问题
export function getCustomSocketPosition() {
  return {
    offset({ x, y }: { x: number; y: number }, nodeId: string, side: 'input' | 'output', key: string) {
      return {
        x: x + 8 * (side === 'input' ? -1 : 1),
        y: y
      };
    },
  };
}

// 添加自定义背景（可选）
export function addCustomBackground(area: any) {
  const container = area.container;
  if (container) {
    container.style.background = 'var(--el-bg-color)';
    
    // 添加网格背景
    const style = document.createElement('style');
    style.textContent = `
      .rete-area {
        background-image: 
          radial-gradient(circle, var(--el-border-color-lighter) 1px, transparent 1px);
        background-size: 20px 20px;
        background-position: 0 0, 10px 10px;
      }
    `;
    document.head.appendChild(style);
  }
} 