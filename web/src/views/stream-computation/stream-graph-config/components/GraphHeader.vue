<template>
  <div class="flex items-center justify-between p-4 bg-gradient-to-r from-white via-blue-50/30 to-purple-50/30 dark:from-gray-800 dark:via-blue-900/20 dark:to-purple-900/20 border-b border-gray-200/60 dark:border-gray-700/60 backdrop-blur-sm">
    <!-- 左侧：图信息 -->
    <div class="flex items-center">
      <!-- 返回按钮 -->
      <el-button
        type="primary"
        text
        class="mr-4 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg px-3 py-2"
        @click="handleGoBack"
      >
        <el-icon size="16" class="mr-2"><ArrowLeft /></el-icon>
        返回
      </el-button>
      
      <div class="flex items-center mr-6">
        <div class="w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg transform hover:scale-105 transition-all duration-300">
          <el-icon size="22" class="text-white">
            <Share />
          </el-icon>
        </div>
        <div>
          <h2 class="text-xl font-bold text-gray-800 dark:text-white bg-gradient-to-r from-gray-800 to-gray-600 dark:from-white dark:to-gray-200 bg-clip-text text-transparent">
            {{ graphInfo.name }}
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ graphInfo.description }}
          </p>
        </div>
      </div>

      <!-- 图状态信息 -->
      <div class="flex items-center space-x-4 text-sm">
        <div class="flex items-center">
          <el-tag 
            :type="graphInfo.status === 'published' ? 'success' : graphInfo.status === 'draft' ? 'warning' : 'info'" 
            size="default"
            effect="light"
            class="px-3 py-1 rounded-full shadow-sm border-0"
            :class="[
              graphInfo.status === 'published' ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 dark:from-green-900/30 dark:to-emerald-900/30 dark:text-green-400' :
              graphInfo.status === 'draft' ? 'bg-gradient-to-r from-orange-100 to-amber-100 text-orange-700 dark:from-orange-900/30 dark:to-amber-900/30 dark:text-orange-400' :
              'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 dark:from-blue-900/30 dark:to-indigo-900/30 dark:text-blue-400'
            ]"
          >
            <div class="flex items-center">
              <div
                :class="[
                  'w-2 h-2 rounded-full mr-2',
                  graphInfo.status === 'published' ? 'bg-green-500 shadow-sm shadow-green-500/50' : 
                  graphInfo.status === 'draft' ? 'bg-orange-500 shadow-sm shadow-orange-500/50' : 
                  'bg-blue-500 shadow-sm shadow-blue-500/50'
                ]"
              />
              {{ getStatusLabel(graphInfo.status) }}
            </div>
          </el-tag>
        </div>
        <div class="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
          <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-xs font-medium">
            版本: {{ graphInfo.version }}
          </span>
        </div>
        <div class="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
          <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-xs font-medium">
            更新: {{ graphInfo.updateTime }}
          </span>
        </div>
      </div>
    </div>

    <!-- 右侧：操作按钮 -->
    <div class="flex items-center space-x-3">
      <!-- 运行状态 -->
      <div v-if="graphInfo.isRunning" class="flex items-center text-sm bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-400 px-3 py-2 rounded-full shadow-sm">
        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2 shadow-sm shadow-green-500/50"></div>
        运行中
      </div>

      <!-- 操作按钮 -->
      <el-button-group class="shadow-sm rounded-lg overflow-hidden">
        <el-button 
          size="default" 
          @click="handleSave"
          class="bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-200 transition-all duration-200"
        >
          <el-icon size="14" class="mr-1"><Document /></el-icon>
          保存
        </el-button>
        <el-button 
          size="default" 
          type="primary"
          :loading="deploying"
          @click="handleDeploy"
          class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 border-0 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
        >
          <el-icon size="14" class="mr-1"><Upload /></el-icon>
          {{ graphInfo.status === 'published' ? '重新部署' : '部署' }}
        </el-button>
      </el-button-group>

      <el-dropdown trigger="click" @command="handleCommand">
        <el-button 
          size="default"
          class="bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow-sm transition-all duration-200 hover:shadow-md"
        >
          更多操作
          <el-icon size="14" class="ml-1"><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu class="modern-dropdown">
            <el-dropdown-item command="validate" class="hover:bg-blue-50 dark:hover:bg-blue-900/30">
              <el-icon size="14" class="mr-2 text-blue-500"><Check /></el-icon>
              验证配置
            </el-dropdown-item>
            <el-dropdown-item command="export" class="hover:bg-green-50 dark:hover:bg-green-900/30">
              <el-icon size="14" class="mr-2 text-green-500"><Download /></el-icon>
              导出配置
            </el-dropdown-item>
            <el-dropdown-item command="duplicate" class="hover:bg-orange-50 dark:hover:bg-orange-900/30">
              <el-icon size="14" class="mr-2 text-orange-500"><CopyDocument /></el-icon>
              复制图
            </el-dropdown-item>
            <el-dropdown-item divided command="delete" class="text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30">
              <el-icon size="14" class="mr-2"><Delete /></el-icon>
              删除图
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 设置按钮 -->
      <el-button 
        size="default" 
        circle 
        @click="handleSettings"
        class="w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 border-0 shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200"
      >
        <el-icon size="16" class="text-gray-600 dark:text-gray-300"><Setting /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  Share,
  Document,
  Upload,
  ArrowDown,
  ArrowLeft,
  Check,
  Download,
  CopyDocument,
  Delete,
  Setting
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 图信息类型定义
interface GraphInfo {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'published' | 'archived';
  version: string;
  updateTime: string;
  isRunning: boolean;
}

// 属性定义
interface Props {
  graphInfo: GraphInfo;
}

const props = defineProps<Props>();

// 事件定义
const emit = defineEmits<{
  goBack: [];
  save: [];
  deploy: [];
  settings: [];
}>();

// 响应式数据
const deploying = ref(false);

// 返回
const handleGoBack = () => {
  emit('goBack');
};

// 获取状态标签
const getStatusLabel = (status: string) => {
  switch (status) {
    case 'draft':
      return '草稿';
    case 'published':
      return '已发布';
    case 'archived':
      return '已归档';
    default:
      return '未知';
  }
};

// 保存
const handleSave = () => {
  emit('save');
  ElMessage.success('保存成功');
};

// 部署
const handleDeploy = async () => {
  deploying.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟部署时间
    emit('deploy');
    ElMessage.success('部署成功');
  } catch (error) {
    ElMessage.error('部署失败');
  } finally {
    deploying.value = false;
  }
};

// 设置
const handleSettings = () => {
  emit('settings');
};

// 处理更多操作
const handleCommand = async (command: string) => {
  switch (command) {
    case 'validate':
      ElMessage.success('配置验证通过');
      break;
    case 'export':
      ElMessage.info('正在导出配置...');
      break;
    case 'duplicate':
      ElMessage.success('图已复制');
      break;
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除图 "${props.graphInfo.name}" 吗？此操作不可撤销。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        ElMessage.success('删除成功');
      } catch {
        // 用户取消
      }
      break;
  }
};
</script>

<style scoped>
/* 现代化下拉菜单样式 */
:deep(.modern-dropdown) {
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

:deep(.modern-dropdown .el-dropdown-menu__item) {
  padding: 12px 16px;
  transition: all 0.2s ease;
  border-radius: 0;
}

:deep(.modern-dropdown .el-dropdown-menu__item:hover) {
  transform: translateX(2px);
}

/* 按钮组样式增强 */
:deep(.el-button-group) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-button-group .el-button) {
  border-radius: 0;
}

:deep(.el-button-group .el-button:first-child) {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

:deep(.el-button-group .el-button:last-child) {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}
</style> 