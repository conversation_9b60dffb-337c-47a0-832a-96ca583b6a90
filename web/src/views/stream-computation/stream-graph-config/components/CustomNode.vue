<template>
  <div
    class="custom-node"
    :class="{ 
      selected: data.selected,
      'configured': isConfigured,
      'unconfigured': !isConfigured,
      'running': isRunning
    }"
    :style="nodeStyles()"
    data-testid="node"
  >
    <!-- 节点标题 -->
    <div class="node-header" data-testid="title">
      <div class="node-title-wrapper">
        <div class="node-icon" :style="{ color: nodeColor }">
          <el-icon size="16">
            <component :is="nodeIconComponent" />
          </el-icon>
        </div>
        <div class="node-title">{{ data.label }}</div>
      </div>
      <div class="node-status-group">
        <!-- 配置状态标识 -->
        <div class="config-status" :class="configStatusClass">
          <el-icon size="12">
            <component :is="configStatusIcon" />
          </el-icon>
          <span class="status-text">{{ configStatusText }}</span>
        </div>
        <!-- 运行状态指示器 -->
        <div v-if="isRunning" class="running-indicator">
          <div class="pulse-dot"></div>
        </div>
      </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="node-content">
      <!-- 左侧输入端口区域 -->
      <div class="node-left">
        <div
          class="input-item"
          v-for="[key, input] in inputs()"
          :key="key + seed"
          :data-testid="'input-' + key"
        >
          <Ref
            class="input-socket"
            :emit="emit"
            :data="{
              type: 'socket',
              side: 'input',
              key: key,
              nodeId: data.id,
              payload: input.socket,
            }"
            data-testid="input-socket"
          />
          <div class="input-label" v-if="input.label">{{ input.label }}</div>
        </div>
      </div>

      <!-- 中间控制区域 -->
      <div class="node-center" v-if="hasControls">
        <div class="control-area">
          <div
            class="control-item"
            v-for="[key, control] in controls()"
            :key="key + seed"
            :data-testid="'control-' + key"
          >
            <Ref
              :emit="emit"
              :data="{
                type: 'control',
                key: key,
                nodeId: data.id,
                payload: control,
              }"
              data-testid="control"
            />
          </div>
        </div>
      </div>

      <!-- 节点描述 -->
      <div v-else-if="nodeDescription" class="node-description">
        <p>{{ nodeDescription }}</p>
      </div>

      <!-- 右侧输出端口区域 -->
      <div class="node-right">
        <div
          class="output-item"
          v-for="[key, output] in outputs()"
          :key="key + seed"
          :data-testid="'output-' + key"
        >
          <div class="output-label" v-if="output.label">{{ output.label }}</div>
          <Ref
            class="output-socket"
            :emit="emit"
            :data="{
              type: 'socket',
              side: 'output',
              key: key,
              nodeId: data.id,
              payload: output.socket,
            }"
            data-testid="output-socket"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Ref } from 'rete-vue-plugin'
import { 
  Check, 
  Warning, 
  Setting, 
  Cpu,
  DataAnalysis,
  Filter,
  Connection,
  Switch
} from '@element-plus/icons-vue'

interface NodeData {
  id: string;
  label: string;
  selected: boolean;
  width?: number;
  height?: number;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  controls: Record<string, any>;
  configured?: boolean;
  running?: boolean;
  nodeType?: string;
  originalData?: {
    icon?: string;
    description?: string;
    color?: string;
  };
}

interface Props {
  data: NodeData;
  emit: Function;
  seed: number;
}

const props = defineProps<Props>();

function sortByIndex(entries: Array<[string, any]>) {
  entries.sort((a, b) => {
    const ai = a[1] && a[1].index || 0;
    const bi = b[1] && b[1].index || 0;
    return ai - bi;
  });
  return entries;
}

function nodeStyles() {
  return {
    width: Number.isFinite(props.data.width) ? `${props.data.width}px` : '',
    height: Number.isFinite(props.data.height) ? `${props.data.height}px` : ''
  };
}

function inputs() {
  return sortByIndex(Object.entries(props.data.inputs));
}

function controls() {
  return sortByIndex(Object.entries(props.data.controls));
}

function outputs() {
  return sortByIndex(Object.entries(props.data.outputs));
}

// 计算节点相关属性
const nodeColor = computed(() => {
  return props.data.originalData?.color || '#6366f1';
});

const nodeIconComponent = computed(() => {
  const nodeType = props.data.nodeType || props.data.originalData?.icon;
  switch (nodeType) {
    case 'data-source':
    case 'input':
      return DataAnalysis;
    case 'filter':
    case 'transform':
      return Filter;
    case 'output':
    case 'sink':
      return Connection;
    case 'switch':
    case 'router':
      return Switch;
    default:
      return Cpu;
  }
});

const nodeDescription = computed(() => {
  return props.data.originalData?.description || '';
});

const hasControls = computed(() => {
  return Object.keys(props.data.controls).length > 0;
});

// 配置状态相关计算属性
const isConfigured = computed(() => {
  return props.data.configured !== false; // 默认认为已配置，除非明确设置为false
});

const isRunning = computed(() => {
  return props.data.running === true;
});

const configStatusClass = computed(() => {
  if (isConfigured.value) {
    return 'status-configured';
  } else {
    return 'status-unconfigured';
  }
});

const configStatusIcon = computed(() => {
  if (isConfigured.value) {
    return Check;
  } else {
    return Warning;
  }
});

const configStatusText = computed(() => {
  if (isConfigured.value) {
    return '已配置';
  } else {
    return '待配置';
  }
});
</script>

<style scoped>
.custom-node {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  border: 2px solid transparent;
  border-radius: 16px;
  cursor: pointer;
  box-sizing: border-box;
  min-width: 240px;
  min-height: 120px;
  position: relative;
  user-select: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: visible;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
}

.custom-node:hover {
  border-color: var(--el-color-primary);
}

.custom-node.selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

.custom-node.configured {
  background: linear-gradient(135deg, rgba(240, 253, 244, 0.95), rgba(236, 253, 245, 0.9));
  border-color: rgba(34, 197, 94, 0.3);
}

.custom-node.unconfigured {
  background: linear-gradient(135deg, rgba(255, 251, 235, 0.95), rgba(254, 249, 195, 0.9));
  border-color: rgba(245, 158, 11, 0.3);
}

.custom-node.running {
  background: linear-gradient(135deg, rgba(239, 246, 255, 0.95), rgba(219, 234, 254, 0.9));
  border-color: rgba(59, 130, 246, 0.4);
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0%, 100% {
    border-color: rgba(59, 130, 246, 0.4);
  }
  50% {
    border-color: rgba(59, 130, 246, 0.8);
  }
}

/* 节点头部 */
.node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 14px 14px 0 0;
  backdrop-filter: blur(10px);
}

.node-title-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.node-icon {
  width: 34px;
  height: 34px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, currentColor, currentColor);
  background-size: 100% 100%;
  color: inherit;
  opacity: 0.1;
  position: relative;
}

.node-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  background: linear-gradient(135deg, currentColor, currentColor);
  opacity: 0.15;
}

.node-icon :deep(.el-icon) {
  position: relative;
  z-index: 1;
  color: currentColor;
  opacity: 1;
}

.node-title {
  color: var(--el-text-color-primary);
  font-family: var(--el-font-family);
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  flex: 1;
}

.node-status-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 配置状态样式 */
.config-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 5px 10px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.status-configured {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));
  color: #059669;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-unconfigured {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-text {
  font-size: 12px;
  white-space: nowrap;
}

/* 运行状态指示器 */
.running-indicator {
  position: relative;
  width: 12px;
  height: 12px;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  position: absolute;
  top: 2px;
  left: 2px;
  animation: pulse-dot 1.5s infinite;
}

.pulse-dot::before {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(16, 185, 129, 0.4);
  top: -2px;
  left: -2px;
  animation: pulse-ring 1.5s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.9);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* 主内容区 */
.node-content {
  padding: 18px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  gap: 18px;
  position: relative;
}

/* 左侧输入端口区域 */
.node-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  flex-shrink: 0;
}

.input-item {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.input-socket {
  transition: all 0.3s ease;
  transform: scale(1.2);
}

.input-socket:hover {
  transform: scale(1.3);
}

.input-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
  white-space: nowrap;
}

/* 中间控制区域 */
.node-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
}

.control-area {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-item {
  display: flex;
  justify-content: center;
}

/* 节点描述 */
.node-description {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}

.node-description p {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  text-align: center;
  line-height: 1.5;
  margin: 0;
  font-style: italic;
}

/* 右侧输出端口区域 */
.node-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  flex-shrink: 0;
}

.output-item {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
  transition: all 0.3s ease;
}

.output-socket {
  transition: all 0.3s ease;
  transform: scale(1.2);
}

.output-socket:hover {
  transform: scale(1.3);
}

.output-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
  white-space: nowrap;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .custom-node {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.95), rgba(17, 24, 39, 0.9));
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%);
  }

  .custom-node.configured {
    background: linear-gradient(135deg, rgba(6, 78, 59, 0.95), rgba(6, 95, 70, 0.9));
  }

  .custom-node.unconfigured {
    background: linear-gradient(135deg, rgba(120, 53, 15, 0.95), rgba(146, 64, 14, 0.9));
  }

  .custom-node.running {
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.95), rgba(29, 78, 216, 0.9));
  }

  .node-header {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.9), rgba(31, 41, 55, 0.8));
  }
}
</style> 