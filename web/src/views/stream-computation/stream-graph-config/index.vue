<template>
  <div class="h-screen flex flex-col bg-white dark:bg-gray-900 no-select-page">
    <!-- 页面头部 -->
    <GraphHeader
      :graph-info="currentGraph"
      @go-back="handleGoBack"
      @save="handleSave"
      @deploy="handleDeploy"
      @settings="handleSettings"
    />

    <!-- 主要内容区域 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 左侧图元库 -->
      <div class="w-96 flex-shrink-0">
        <div class="h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          <GraphElementList @element-click="handleElementClick" />
        </div>
      </div>

      <!-- 右侧画布区域 -->
      <div class="flex-1">
        <div class="h-full bg-gray-50 dark:bg-gray-900">
          <GraphCanvas ref="canvasRef" @element-add="handleElementAdd" />
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="settingsVisible"
      title="图配置设置"
      width="600px"
      :before-close="handleSettingsClose"
      class="modern-dialog"
    >
      <el-form
        ref="settingsFormRef"
        :model="settingsForm"
        :rules="settingsRules"
        label-width="120px"
        label-position="left"
        class="settings-form"
      >
        <el-form-item label="图名称" prop="name">
          <el-input
            v-model="settingsForm.name"
            placeholder="请输入图名称"
            clearable
            class="modern-input"
          />
        </el-form-item>
        <el-form-item label="图描述" prop="description">
          <el-input
            v-model="settingsForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入图描述"
            class="modern-textarea"
          />
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input
            v-model="settingsForm.version"
            placeholder="请输入版本号"
            clearable
            class="modern-input"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="settingsForm.status"
            placeholder="请选择状态"
            class="w-full modern-select"
          >
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer flex justify-end space-x-3 pt-4">
          <el-button @click="handleSettingsClose" class="modern-btn-secondary">取消</el-button>
          <el-button
            type="primary"
            @click="handleSettingsSubmit"
            :loading="settingsSubmitting"
            class="modern-btn-primary"
          >
            保存设置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import GraphHeader from "./components/GraphHeader.vue";
import GraphElementList from "./components/GraphElementList.vue";
import GraphCanvas from "./components/GraphCanvas.vue";

// 路由
const router = useRouter();

// 图元类型定义
interface GraphElement {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  type: string;
  inputPorts: Array<{ name: string; type: string }>;
  outputPorts: Array<{ name: string; type: string }>;
  properties: Array<{ name: string; type: string; description: string }>;
}

// 图信息类型定义
interface GraphInfo {
  id: string;
  name: string;
  description: string;
  status: "draft" | "published" | "archived";
  version: string;
  updateTime: string;
  isRunning: boolean;
}

// 响应式数据
const canvasRef = ref<InstanceType<typeof GraphCanvas>>();
const settingsVisible = ref(false);
const settingsSubmitting = ref(false);
const settingsFormRef = ref<FormInstance>();

// 当前图配置信息
const currentGraph = ref<GraphInfo>({
  id: "graph-001",
  name: "数据处理流程图",
  description: "用于实时数据处理和分析的流程图配置",
  status: "draft",
  version: "1.0.0",
  updateTime: "2024-01-15 14:30",
  isRunning: false
});

// 设置表单
const settingsForm = reactive({
  name: "",
  description: "",
  version: "",
  status: "draft" as "draft" | "published" | "archived"
});

// 设置表单验证规则
const settingsRules: FormRules = {
  name: [
    { required: true, message: "请输入图名称", trigger: "blur" },
    { min: 2, max: 50, message: "图名称长度在 2 到 50 个字符", trigger: "blur" }
  ],
  description: [{ required: true, message: "请输入图描述", trigger: "blur" }],
  version: [
    { required: true, message: "请输入版本号", trigger: "blur" },
    {
      pattern: /^\d+\.\d+\.\d+$/,
      message: "版本号格式不正确，如：1.0.0",
      trigger: "blur"
    }
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }]
};

// 生命周期
onMounted(() => {
  // 初始化页面
  initializeGraph();
});

// 初始化图
const initializeGraph = () => {
  // 初始化图配置
  console.log("初始化流程图配置页面");
};

// 返回上一页
const handleGoBack = () => {
  router.back();
};

// 处理图元点击
const handleElementClick = (element: GraphElement) => {
  console.log("图元被点击:", element);
  // 添加图元到画布
  if (canvasRef.value) {
    canvasRef.value.addElementToCanvas(element);
  }
};

// 处理图元添加到画布
const handleElementAdd = (element: GraphElement) => {
  console.log("图元已添加到画布:", element);
  
  // 模拟设置节点配置状态
  // 50% 的概率设置为未配置状态
  const isConfigured = Math.random() > 0.5;
  const isRunning = Math.random() > 0.7; // 30% 的概率设置为运行状态
  
  // 向元素添加配置和运行状态
  (element as any).configured = isConfigured;
  (element as any).running = isRunning;
  
  console.log(`节点状态 - 配置完成: ${isConfigured}, 运行中: ${isRunning}`);
  
  // 可以在这里处理图元添加后的逻辑
};

// 保存图配置
const handleSave = () => {
  console.log("保存图配置");
  // 实现保存逻辑
  currentGraph.value.updateTime = new Date()
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit"
    })
    .replace(/\//g, "-");
};

// 部署图
const handleDeploy = () => {
  console.log("部署图");
  // 实现部署逻辑
  currentGraph.value.status = "published";
  currentGraph.value.isRunning = true;
};

// 显示设置对话框
const handleSettings = () => {
  // 填充当前配置到表单
  Object.assign(settingsForm, {
    name: currentGraph.value.name,
    description: currentGraph.value.description,
    version: currentGraph.value.version,
    status: currentGraph.value.status
  });
  settingsVisible.value = true;
};

// 关闭设置对话框
const handleSettingsClose = () => {
  settingsVisible.value = false;
  settingsFormRef.value?.resetFields();
};

// 提交设置
const handleSettingsSubmit = async () => {
  if (!settingsFormRef.value) return;

  try {
    await settingsFormRef.value.validate();
    settingsSubmitting.value = true;

    // 模拟保存设置
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 更新当前图配置
    Object.assign(currentGraph.value, {
      name: settingsForm.name,
      description: settingsForm.description,
      version: settingsForm.version,
      status: settingsForm.status,
      updateTime: new Date()
        .toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit"
        })
        .replace(/\//g, "-")
    });

    ElMessage.success("设置保存成功");
    handleSettingsClose();
  } catch (error) {
    console.error("设置验证失败:", error);
  } finally {
    settingsSubmitting.value = false;
  }
};
</script>

<style scoped>
/* 现代化对话框样式 */
:deep(.modern-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

:deep(.modern-dialog .el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-fill-color-light) 100%);
}

:deep(.modern-dialog .el-dialog__body) {
  padding: 24px;
  background: var(--el-bg-color);
}

/* 现代化表单样式 */
.settings-form :deep(.el-form-item__label) {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.modern-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.modern-input :deep(.el-input__wrapper:focus-within) {
  box-shadow: 0 0 0 3px var(--el-color-primary)/20, 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.modern-textarea :deep(.el-textarea__inner) {
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.modern-textarea :deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 3px var(--el-color-primary)/20, 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.modern-select :deep(.el-select__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

/* 现代化按钮样式 */
.modern-btn-primary {
  border-radius: 8px;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.modern-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modern-btn-secondary {
  border-radius: 8px;
  background: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color-light);
  color: var(--el-text-color-regular);
  transition: all 0.3s ease;
}

.modern-btn-secondary:hover {
  background: var(--el-fill-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 禁止选中和拖拽样式 */
.no-select-page {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}
</style>
