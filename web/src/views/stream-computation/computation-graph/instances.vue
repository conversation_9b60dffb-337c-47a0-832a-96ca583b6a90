<template>
  <div class="computation-graph-instances min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 头部导航 -->
    <div
      class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <el-button
            type="primary"
            text
            class="text-primary hover:text-primary/80"
            @click="goBack"
          >
            <el-icon size="16" class="mr-2"><ArrowLeft /></el-icon>
            返回列表
          </el-button>
          <div class="w-px h-6 bg-gray-300 dark:bg-gray-600" />
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              计算图实例 - {{ graphInfo.name }}
            </h1>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
              图ID: {{ graphInfo.id }} | 共 {{ total }} 个实例
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="p-6">
      <!-- 操作栏 -->
      <div
        class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6"
      >
        <div class="flex-1 max-w-md">
          <el-input
            v-model="searchQuery"
            placeholder="搜索实例ID或名称..."
            clearable
            :prefix-icon="Search"
            @input="handleSearch"
          />
        </div>
        <div class="flex items-center space-x-3">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon size="16" class="mr-2"><Plus /></el-icon>
            新建实例
          </el-button>
        </div>
      </div>

    <!-- 图信息卡片 -->
    <el-card class="mb-6" shadow="never">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mr-4"
          >
            <el-icon size="20" class="text-white"><Share /></el-icon>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ graphInfo.name }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ graphInfo.description }}
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">
              {{ graphInfo.instanceCount }}
            </div>
            <div class="text-sm text-gray-500">实例数量</div>
          </div>
          <el-button type="success" @click="handleViewGraphConfig">
            查看图配置
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 实例列表表格 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      style="width: 100%"
      class="rounded-lg"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="graphId" label="图ID" width="100" sortable />
      <el-table-column prop="instanceId" label="实例ID" width="120" sortable />
      <el-table-column prop="instanceName" label="实例名称" min-width="150" sortable>
        <template #default="{ row }">
          <div class="flex items-center">
            <div
              class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3 flex-shrink-0"
            >
              <el-icon size="14" class="text-white"><Document /></el-icon>
            </div>
            <span class="font-medium">{{ row.instanceName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="运行状态" width="120" sortable>
        <template #default="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            effect="light"
          >
            <div class="flex items-center">
              <div
                :class="[
                  'w-2 h-2 rounded-full mr-2',
                  getStatusDotClass(row.status)
                ]"
              />
              {{ getStatusText(row.status) }}
            </div>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="version" label="版本" width="100" sortable />
      <el-table-column prop="executionCount" label="执行次数" width="120" sortable />
      <el-table-column prop="lastExecutionTime" label="最后执行时间" width="160" sortable />
      <el-table-column prop="createTime" label="创建时间" width="160" sortable />
      <el-table-column prop="creator" label="创建人" width="100" sortable />
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="{ row }">
          <el-button
            size="small"
            type="primary"
            text
            @click="handleExecuteInstance(row)"
          >
            执行
          </el-button>
          <el-button
            size="small"
            type="success"
            text
            @click="handleViewInstanceConfig(row)"
          >
            查看配置
          </el-button>
          <el-dropdown
            trigger="click"
            @command="(command: string) => handleCommand(command, row)"
          >
            <el-button size="small" type="primary" text class="ml-2">
              更多
              <el-icon size="12" class="ml-1"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon size="14" class="mr-2 text-blue-500">
                    <Edit />
                  </el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="start" :disabled="row.status === 'running'">
                  <el-icon size="14" class="mr-2 text-green-500">
                    <VideoPlay />
                  </el-icon>
                  启动
                </el-dropdown-item>
                <el-dropdown-item command="stop" :disabled="row.status !== 'running'">
                  <el-icon size="14" class="mr-2 text-orange-500">
                    <VideoPause />
                  </el-icon>
                  停止
                </el-dropdown-item>
                <el-dropdown-item command="restart">
                  <el-icon size="14" class="mr-2 text-purple-500">
                    <Refresh />
                  </el-icon>
                  重启
                </el-dropdown-item>
                <el-dropdown-item command="copy">
                  <el-icon size="14" class="mr-2 text-cyan-500">
                    <CopyDocument />
                  </el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item command="delete" class="text-red-500">
                  <el-icon size="14" class="mr-2"><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

      <!-- 分页 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  Plus,
  Search,
  Share,
  Document,
  ArrowDown,
  ArrowLeft,
  Edit,
  VideoPlay,
  VideoPause,
  Refresh,
  CopyDocument,
  Delete
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

// 路由
const router = useRouter();
const route = useRoute();

// 状态定义
const loading = ref(false);
const searchQuery = ref("");
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const sortField = ref("");
const sortOrder = ref("");

// 图信息
const graphInfo = ref({
  id: "",
  name: "",
  description: "",
  instanceCount: 0
});

// 实例状态映射
const statusMap = {
  running: { text: "运行中", type: "success", dotClass: "bg-green-500 animate-pulse" },
  stopped: { text: "已停止", type: "info", dotClass: "bg-gray-400" },
  error: { text: "异常", type: "danger", dotClass: "bg-red-500" },
  pending: { text: "等待中", type: "warning", dotClass: "bg-yellow-500" }
};

// 获取状态类型
const getStatusType = (status: string) => {
  return statusMap[status]?.type || "info";
};

// 获取状态文本
const getStatusText = (status: string) => {
  return statusMap[status]?.text || "未知";
};

// 获取状态点样式
const getStatusDotClass = (status: string) => {
  return statusMap[status]?.dotClass || "bg-gray-400";
};

// 生成模拟实例数据
const generateInstanceData = (graphId: string) => {
  const statuses = ["running", "stopped", "error", "pending"];
  const creators = ["张三", "李四", "王五", "赵六"];
  
  const data = [];
  const instanceCount = Math.floor(Math.random() * 30) + 10; // 10-40个实例
  
  for (let i = 1; i <= instanceCount; i++) {
    data.push({
      graphId: graphId,
      instanceId: `${graphId}-I${i.toString().padStart(3, '0')}`,
      instanceName: `实例${i}`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      version: `v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}`,
      executionCount: Math.floor(Math.random() * 1000),
      lastExecutionTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().substring(0, 16).replace('T', ' '),
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().substring(0, 16).replace('T', ' '),
      creator: creators[Math.floor(Math.random() * creators.length)]
    });
  }
  return data;
};

const allInstances = ref([]);

// 过滤和分页后的数据
const filteredInstances = computed(() => {
  let data = allInstances.value;
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    data = data.filter(
      instance =>
        instance.instanceId.toLowerCase().includes(query) ||
        instance.instanceName.toLowerCase().includes(query)
    );
  }
  
  // 排序
  if (sortField.value && sortOrder.value) {
    data = [...data].sort((a, b) => {
      const aVal = a[sortField.value];
      const bVal = b[sortField.value];
      
      if (sortOrder.value === 'ascending') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });
  }
  
  total.value = data.length;
  return data;
});

// 表格显示的数据
const tableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredInstances.value.slice(start, end);
});

// 生命周期钩子
onMounted(() => {
  const graphId = route.params.graphId as string;
  if (graphId) {
    initPageData(graphId);
    fetchInstanceData(graphId);
  }
});

// 初始化页面数据
const initPageData = (graphId: string) => {
  // 模拟获取图信息 - 实际应该从API获取
  const graphNames = ["推荐算法图", "风控模型图", "数据清洗图", "特征提取图"];
  const graphDescriptions = [
    "用户商品推荐计算图",
    "金融风险评估计算图", 
    "数据预处理和清洗图",
    "机器学习特征提取图"
  ];
  
  const index = parseInt(graphId.replace(/\D/g, '')) % graphNames.length;
  
  graphInfo.value = {
    id: graphId,
    name: graphNames[index] || `计算图${graphId}`,
    description: graphDescriptions[index] || `计算图${graphId}的描述`,
    instanceCount: 0
  };
};

// 获取实例数据
const fetchInstanceData = async (graphId: string) => {
  loading.value = true;
  try {
    // TODO: 调用API获取实例数据
    console.log("获取图实例数据", graphId);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 生成模拟数据
    allInstances.value = generateInstanceData(graphId);
    graphInfo.value.instanceCount = allInstances.value.length;
  } catch (error) {
    ElMessage.error("获取实例数据失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
};

// 排序处理
const handleSortChange = ({ prop, order }) => {
  sortField.value = prop;
  sortOrder.value = order;
};

// 分页处理
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
};

// 返回列表
const goBack = () => {
  router.push({ name: "ComputationGraph" });
};

// 显示创建对话框
const showCreateDialog = () => {
  ElMessage.info("新建实例功能开发中...");
};

// 查看图配置
const handleViewGraphConfig = () => {
  router.push({
    path: "/stream-graph-config",
    query: {
      graphId: graphInfo.value.id,
      graphName: graphInfo.value.name
    }
  });
};

// 执行实例
const handleExecuteInstance = (instance: any) => {
  ElMessage.success(`执行实例 ${instance.instanceName}`);
};

// 查看实例配置
const handleViewInstanceConfig = (instance: any) => {
  ElMessage.info(`查看实例 ${instance.instanceName} 配置功能开发中...`);
};

// 处理操作命令
const handleCommand = (command: string, instance: any) => {
  switch (command) {
    case "edit":
      ElMessage.info(`编辑实例 ${instance.instanceName}`);
      break;
    case "start":
      ElMessage.success(`启动实例 ${instance.instanceName}`);
      break;
    case "stop":
      ElMessage.warning(`停止实例 ${instance.instanceName}`);
      break;
    case "restart":
      ElMessage.success(`重启实例 ${instance.instanceName}`);
      break;
    case "copy":
      ElMessage.success(`复制实例 ${instance.instanceName}`);
      break;
    case "delete":
      ElMessage.success(`删除实例 ${instance.instanceName}`);
      break;
  }
};
</script> 