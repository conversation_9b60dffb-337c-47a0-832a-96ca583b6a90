<template>
  <div class="h2-config">
    <el-form :model="configData" label-width="120px">
      <!-- 数据库模式选择 -->
      <el-form-item :label="$t('middleware.config.databaseMode')" required>
        <el-radio-group v-model="configData.mode" @change="handleModeChange">
          <el-radio value="memory">{{ $t('middleware.config.memoryMode') }}</el-radio>
          <el-radio value="file">{{ $t('middleware.config.fileMode') }}</el-radio>
          <el-radio value="tcp">{{ $t('middleware.config.serverMode') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 数据库名称 -->
      <el-form-item :label="$t('middleware.config.databaseName')" required>
        <el-input
          v-model="configData.dbName"
          placeholder="testdb"
          @input="handleConfigChange"
        />
        <div class="form-tip">{{ $t('middleware.config.databaseNameTip') }}</div>
      </el-form-item>

      <!-- 文件模式配置 -->
      <template v-if="configData.mode === 'file'">
        <el-form-item :label="$t('middleware.config.filePath')" required>
          <el-input
            v-model="configData.filePath"
            placeholder="./data/h2db"
            @input="handleConfigChange"
          />
          <div class="form-tip">{{ $t('middleware.config.filePathTip') }}</div>
        </el-form-item>
      </template>

      <!-- TCP服务器模式配置 -->
      <template v-if="configData.mode === 'tcp'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.host')" required>
              <el-input
                v-model="configData.host"
                placeholder="localhost"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.port')" required>
              <el-input-number
                v-model="configData.port"
                :min="1"
                :max="65535"
                placeholder="9092"
                style="width: 100%"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 认证配置 -->
      <el-divider content-position="left">{{ $t('middleware.config.authentication') }}</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.username')">
            <el-input
              v-model="configData.username"
              placeholder="sa"
              @input="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.password')">
            <el-input
              v-model="configData.password"
              type="password"
              :placeholder="$t('middleware.config.passwordOptional')"
              show-password
              @input="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 连接池配置 -->
      <el-divider content-position="left">{{ $t('middleware.config.connectionPool') }}</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.minIdle')">
            <el-input-number
              v-model="configData.minIdle"
              :min="1"
              :max="100"
              placeholder="5"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.maxPoolSize')">
            <el-input-number
              v-model="configData.maxPoolSize"
              :min="1"
              :max="1000"
              placeholder="20"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.connectionTimeoutMs')">
            <el-input-number
              v-model="configData.connectionTimeout"
              :min="1000"
              :max="300000"
              placeholder="30000"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.idleTimeout')">
            <el-input-number
              v-model="configData.idleTimeout"
              :min="10000"
              :max="600000"
              placeholder="600000"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 高级配置 -->
      <el-divider content-position="left">{{ $t('middleware.config.advancedSettings') }}</el-divider>

      <el-form-item :label="$t('middleware.config.compatibilityMode')">
        <el-select
          v-model="configData.compatibilityMode"
          :placeholder="$t('middleware.config.selectCompatibilityMode')"
          style="width: 200px"
          @change="handleConfigChange"
        >
          <el-option label="MySQL" value="MYSQL" />
          <el-option label="PostgreSQL" value="POSTGRESQL" />
          <el-option label="Oracle" value="ORACLE" />
          <el-option label="DB2" value="DB2" />
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('middleware.config.autoServerMode')">
            <el-switch
              v-model="configData.autoServerMode"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('middleware.config.autoReconnect')">
            <el-switch
              v-model="configData.autoReconnect"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('middleware.config.autoCommit')">
            <el-switch
              v-model="configData.autoCommit"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";

defineOptions({
  name: "H2Config"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({})
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端H2Config.java的17个字段保持一致
const configData = reactive({
  dbName: 'testdb',
  mode: 'memory', // memory, file, tcp
  filePath: './data/h2db',
  host: 'localhost',
  port: 9092,
  username: 'sa',
  password: '',
  compatibilityMode: 'MYSQL',
  autoServerMode: false,
  autoReconnect: true,
  maxPoolSize: 10,
  minIdle: 1,
  idleTimeout: 30000,
  connectionTimeout: 30000,
  maxLifetime: 1800000,
  autoCommit: true
});

// 处理模式变化
const handleModeChange = () => {
  // 根据模式设置默认值
  if (configData.mode === 'memory') {
    configData.dbName = 'testdb';
  } else if (configData.mode === 'file') {
    configData.filePath = './data/h2db';
  } else if (configData.mode === 'tcp') {
    configData.host = 'localhost';
    configData.port = 9092;
  }
  handleConfigChange();
};

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
.h2-config {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 20px;
  }
}
</style>
