<template>
  <div class="mos-mqtt-config">
    <el-form :model="configData" label-width="120px">
      <!-- 基础连接信息 -->
      <el-form-item :label="$t('middleware.config.serverUri')" required>
        <el-input 
          v-model="configData.serverUri" 
          placeholder="tcp://localhost:1883"
          @input="handleConfigChange"
        />
        <div class="form-tip">{{ $t('middleware.config.serverUriTip') }}</div>
      </el-form-item>
      
      <el-form-item :label="$t('middleware.config.clientId')">
        <el-input 
          v-model="configData.clientId" 
          :placeholder="$t('middleware.config.clientIdPlaceholder')"
          @input="handleConfigChange"
        />
        <div class="form-tip">{{ $t('middleware.config.clientIdTip') }}</div>
      </el-form-item>
      
      <!-- 认证配置 -->
      <el-divider content-position="left">{{ $t('middleware.config.authentication') }}</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.username')">
            <el-input 
              v-model="configData.username" 
              :placeholder="$t('middleware.config.usernameOptional')"
              @input="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.password')">
            <el-input 
              v-model="configData.password" 
              type="password"
              :placeholder="$t('middleware.config.passwordOptional')"
              show-password
              @input="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 连接配置 -->
      <el-divider content-position="left">{{ $t('middleware.config.connectionSettings') }}</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.connectionTimeout')">
            <el-input-number 
              v-model="configData.connectionTimeout" 
              :min="1" 
              :max="300"
              placeholder="30"
              style="width: 100%"
              @change="handleConfigChange"
            />
            <div class="form-tip">{{ $t('middleware.config.connectionTimeoutTip') }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.keepAliveInterval')">
            <el-input-number 
              v-model="configData.keepAliveInterval" 
              :min="10" 
              :max="3600"
              placeholder="60"
              style="width: 100%"
              @change="handleConfigChange"
            />
            <div class="form-tip">{{ $t('middleware.config.keepAliveIntervalTip') }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.defaultQos')">
            <el-select 
              v-model="configData.defaultQos" 
              :placeholder="$t('middleware.config.selectQos')"
              style="width: 100%"
              @change="handleConfigChange"
            >
              <el-option :label="$t('middleware.config.qos0')" :value="0" />
              <el-option :label="$t('middleware.config.qos1')" :value="1" />
              <el-option :label="$t('middleware.config.qos2')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.maxInflight')">
            <el-input-number 
              v-model="configData.maxInflight" 
              :min="1" 
              :max="65535"
              placeholder="10"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('middleware.config.cleanSession')">
            <el-switch 
              v-model="configData.cleanSession" 
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('middleware.config.automaticReconnect')">
            <el-switch 
              v-model="configData.automaticReconnect" 
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('middleware.config.defaultRetained')">
            <el-switch 
              v-model="configData.defaultRetained" 
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item :label="$t('middleware.config.maxReconnectDelay')">
        <el-input-number 
          v-model="configData.maxReconnectDelay" 
          :min="1000" 
          :max="300000"
          placeholder="128000"
          style="width: 200px"
          @change="handleConfigChange"
        />
        <div class="form-tip">{{ $t('middleware.config.maxReconnectDelayTip') }}</div>
      </el-form-item>
      
      <!-- 遗嘱消息配置 -->
      <el-divider content-position="left">{{ $t('middleware.config.willMessage') }}</el-divider>
      
      <el-form-item :label="$t('middleware.config.willTopic')">
        <el-input 
          v-model="configData.willTopic" 
          :placeholder="$t('middleware.config.willTopicPlaceholder')"
          @input="handleConfigChange"
        />
      </el-form-item>
      
      <el-form-item :label="$t('middleware.config.willMessage')">
        <el-input 
          v-model="configData.willMessage" 
          type="textarea"
          :rows="3"
          :placeholder="$t('middleware.config.willMessagePlaceholder')"
          @input="handleConfigChange"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.willQos')">
            <el-select 
              v-model="configData.willQos" 
              :placeholder="$t('middleware.config.selectQos')"
              style="width: 100%"
              @change="handleConfigChange"
            >
              <el-option :label="$t('middleware.config.qos0')" :value="0" />
              <el-option :label="$t('middleware.config.qos1')" :value="1" />
              <el-option :label="$t('middleware.config.qos2')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('middleware.config.willRetained')">
            <el-switch 
              v-model="configData.willRetained" 
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- SSL/TLS配置 -->
      <el-divider content-position="left">{{ $t('middleware.config.sslTlsSettings') }}</el-divider>
      
      <el-form-item :label="$t('middleware.config.useSsl')">
        <el-switch 
          v-model="configData.useSsl" 
          @change="handleConfigChange"
        />
      </el-form-item>
      
      <template v-if="configData.useSsl">
        <el-form-item :label="$t('middleware.config.keyStore')">
          <el-input 
            v-model="configData.keyStore" 
            :placeholder="$t('middleware.config.keyStorePlaceholder')"
            @input="handleConfigChange"
          />
        </el-form-item>
        
        <el-form-item :label="$t('middleware.config.keyStorePassword')">
          <el-input 
            v-model="configData.keyStorePassword" 
            type="password"
            :placeholder="$t('middleware.config.keyStorePasswordPlaceholder')"
            show-password
            @input="handleConfigChange"
          />
        </el-form-item>
        
        <el-form-item :label="$t('middleware.config.trustStore')">
          <el-input 
            v-model="configData.trustStore" 
            :placeholder="$t('middleware.config.trustStorePlaceholder')"
            @input="handleConfigChange"
          />
        </el-form-item>
        
        <el-form-item :label="$t('middleware.config.trustStorePassword')">
          <el-input 
            v-model="configData.trustStorePassword" 
            type="password"
            :placeholder="$t('middleware.config.trustStorePasswordPlaceholder')"
            show-password
            @input="handleConfigChange"
          />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";

defineOptions({
  name: "MosMQTTConfig"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({})
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端MosMQTTConfig.java的20个字段保持一致
const configData = reactive({
  serverUri: 'tcp://localhost:1883',
  clientId: '',
  username: '',
  password: '',
  cleanSession: true,
  connectionTimeout: 30,
  keepAliveInterval: 60,
  automaticReconnect: true,
  maxReconnectDelay: 128000,
  maxInflight: 10,
  defaultQos: 1,
  defaultRetained: false,
  willTopic: '',
  willMessage: '',
  willQos: 1,
  willRetained: false,
  useSsl: false,
  keyStore: '',
  keyStorePassword: '',
  trustStore: '',
  trustStorePassword: ''
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  
  // 如果没有设置客户端ID，自动生成一个
  if (!config.clientId) {
    config.clientId = `mqtt-client-${Date.now()}`;
  }
  
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
.mos-mqtt-config {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #303133;
}
</style>
