middleware:
  management: 中间件管理
  resourceManagement: 资源管理
  serviceManagement: 服务管理
  detailPage: 详情
  configuration: 配置

  overview:
    resourceDescription: 管理各种资源配置，包括数据库、缓存、消息队列等
    serviceDescription: 管理各种服务配置，提供统一的服务接口
    totalResources: 资源配置总数
    enabledResources: 已启用资源
    totalServices: 服务配置总数
    enabledServices: 已启用服务

  config:
    # 基础字段
    host: 主机地址
    port: 端口号
    username: 用户名
    password: 密码
    database: 数据库
    databaseName: 数据库名称
    databaseNameTip: 数据库名称，用于标识数据库实例
    schema: 模式
    filePath: 文件路径
    filePathTip: 数据库文件存储路径，相对于应用根目录

    # 连接池配置
    connectionPool: 连接池配置
    maxConnections: 最大连接数
    minConnections: 最小连接数
    maxPoolSize: 最大连接数
    minIdle: 最小空闲连接数
    maxIdleConnections: 最大空闲连接数
    minIdleConnections: 最小空闲连接数
    connectionTimeout: 连接超时
    connectionTimeoutMs: 连接超时(ms)
    connectionTimeoutTip: 连接超时时间（秒）
    commandTimeout: 命令超时时间(ms)
    commandTimeoutTip: 执行命令的超时时间，单位毫秒
    idleTimeout: 空闲超时(ms)
    maxLifetime: 连接最大生命周期(ms)

    # 超时设置
    timeoutSettings: 超时设置

    # 认证配置
    authentication: 认证配置
    passwordOptional: 密码（可选）
    usernameOptional: 用户名（可选）

    # 高级设置
    advancedSettings: 高级设置
    autoCommit: 自动提交
    autoReconnect: 自动重连

    # H2数据库特有
    databaseMode: 数据库模式
    memoryMode: 内存模式
    fileMode: 文件模式
    serverMode: 服务器模式
    compatibilityMode: 兼容模式
    selectCompatibilityMode: 请选择兼容模式
    autoServerMode: 自动服务器模式

    # MQTT配置
    serverUri: 服务器地址
    serverUriTip: MQTT Broker服务器地址，如 tcp://localhost:1883
    clientId: 客户端ID
    clientIdPlaceholder: 留空将自动生成
    clientIdTip: MQTT客户端唯一标识符，留空将自动生成
    brokerUrl: Broker地址
    brokerUrlTip: MQTT Broker服务器地址
    connectionSettings: 连接设置
    keepAliveInterval: 心跳间隔
    keepAliveIntervalTip: 心跳保持间隔（秒）
    defaultQos: 默认QoS
    selectQos: 请选择QoS等级
    qos: 服务质量
    qosTip: 消息传输质量等级（0-2）
    qos0: QoS 0 - 最多一次
    qos1: QoS 1 - 至少一次
    qos2: QoS 2 - 恰好一次
    maxInflight: 最大飞行窗口
    cleanSession: 清理会话
    cleanSessionTip: 是否在连接时清理会话
    automaticReconnect: 自动重连
    defaultRetained: 默认保留消息
    retained: 保留消息
    retainedTip: 是否保留最后一条消息
    maxReconnectDelay: 最大重连延迟
    maxReconnectDelayTip: 最大重连延迟时间（毫秒）

    # 遗嘱消息配置
    willMessage: 遗嘱消息
    willTopic: 遗嘱主题
    willTopicPlaceholder: 请输入遗嘱消息主题
    willMessagePlaceholder: 请输入遗嘱消息内容
    willQos: 遗嘱QoS
    willRetained: 遗嘱保留

    # Kafka配置
    brokers: Broker地址
    brokersPlaceholder: 请输入Kafka Broker地址，如：localhost:9092
    brokersTip: Kafka集群的Broker服务器地址列表
    consumerGroup: 消费者组
    consumerGroupPlaceholder: 请输入消费者组名称
    consumerGroupTip: Kafka消费者组标识符
    topic: 主题
    topicPlaceholder: 请输入主题名称
    topicTip: Kafka消息主题名称

    # SSL/TLS配置
    sslTlsSettings: SSL/TLS设置
    useSsl: 使用SSL
    enableSsl: 启用SSL
    enableSslTip: 是否启用SSL/TLS加密连接
    keyStore: 密钥库
    keyStoreTip: 客户端密钥库文件路径
    keyStorePlaceholder: 请输入密钥库文件路径
    keyStorePassword: 密钥库密码
    keyStorePasswordPlaceholder: 请输入密钥库密码
    trustStore: 信任库
    trustStoreTip: 信任库文件路径
    trustStorePlaceholder: 请输入信任库文件路径
    trustStorePassword: 信任库密码
    trustStorePasswordPlaceholder: 请输入信任库密码

  table:
    id: ID
    name: 名称
    type: 类型
    resourceType: 资源类型
    serviceType: 服务类型
    status: 状态
    enabled: 是否启用
    instantiated: 是否实例化
    description: 描述
    config: 配置
    createTime: 创建时间
    updateTime: 更新时间
    createdBy: 创建人
    updatedBy: 更新人
    operation: 操作
    resourceConfig: 关联资源
    category: 类别
    uiComponent: UI组件
    supportedResourceCategory: 支持的资源类别

  form:
    basicInfo: 基础信息
    configArea: 配置区域
    id: ID
    name: 名称
    description: 描述
    serviceType: 服务类型
    resourceType: 资源类型
    resourceConfig: 资源配置
    config: 配置
    placeholder:
      name: 请输入名称
      description: 请输入描述
      selectResourceType: 请选择资源类型
      selectServiceType: 请选择服务类型
      selectResourceConfig: 请选择关联资源配置
      jsonConfig: 请输入JSON格式的配置
    rules:
      nameRequired: 名称不能为空
      resourceTypeRequired: 资源类型不能为空
      serviceTypeRequired: 服务类型不能为空
      resourceConfigRequired: 关联资源配置不能为空

  button:
    add: 新增
    addResource: 新增资源配置
    addService: 新增服务配置
    edit: 编辑
    delete: 删除
    enable: 启用
    disable: 禁用
    instantiate: 实例化
    destroy: 销毁
    testConnection: 测试连接
    detail: 详情
    save: 保存
    cancel: 取消
    confirm: 确认
    back: 返回
    refresh: 刷新
    search: 搜索
    reset: 重置
    export: 导出
    import: 导入

  status:
    enabled: 已启用
    disabled: 已禁用
    running: 运行中
    stopped: 已停止
    error: 错误
    unknown: 未知
    healthy: 健康
    unhealthy: 不健康
    instantiated: 已实例化
    notInstantiated: 未实例化

  message:
    addSuccess: 添加成功
    editSuccess: 编辑成功
    deleteSuccess: 删除成功
    enableSuccess: 启用成功
    disableSuccess: 禁用成功
    instantiateSuccess: 实例化成功
    destroySuccess: 销毁成功
    testConnectionSuccess: 连接测试成功
    testConnectionFailed: 连接测试失败
    operationSuccess: 操作成功
    operationFailed: 操作失败
    confirmDelete: 确认删除该配置吗？
    confirmEnable: 确认启用该配置吗？
    confirmDisable: 确认禁用该配置吗？
    confirmInstantiate: 确认实例化该资源吗？
    confirmDestroy: 确认销毁该资源实例吗？
    loadingData: 正在加载数据...
    noData: 暂无数据
    configRequired: 配置信息不能为空
    invalidConfig: 配置信息格式不正确

  search:
    placeholder: 请输入搜索关键词
    name: 按名称搜索
    type: 按类型筛选
    status: 按状态筛选
    all: 全部

  detail:
    basicConfig: 基础配置
    maintenanceManagement: 维护管理
    healthInfo: 健康信息
    healthStatus: 健康状态
    lastCheckTime: 最后检查时间
    metrics: 健康指标
    errors: 错误信息
    warnings: 警告信息
    connectionCount: 连接数
    memoryUsage: 内存使用
    cpuLoad: CPU负载
    responseTime: 响应时间
    comingSoon: 维护管理功能正在开发中，敬请期待。
    logs: 日志
    noLogs: 暂无日志
    logStreamError: 日志流连接失败

  resourceType:
    mysql: MySQL
    redis: Redis
    h2: H2
    postgresql: PostgreSQL
    kafka: Kafka
    mqtt: MQTT
    httpServer: HTTP服务器

  serviceType:
    database: 数据库服务
    keyValueStore: 键值存储服务
    messageQueue: 消息队列服务
    httpService: HTTP服务
    sitewebPersistent: Siteweb持久化服务

  category:
    relationalDb: 关系型数据库
    keyValueStore: 键值存储
    messageQueue: 消息队列
    webServer: Web服务器

  configFields:
    host: 主机
    port: 端口
    username: 用户名
    password: 密码
    database: 数据库
    url: 连接URL
    timeout: 超时时间
    maxConnections: 最大连接数
    minConnections: 最小连接数
    brokers: Broker地址
    topic: 主题
    consumerGroup: 消费者组
    ssl: SSL配置
    auth: 认证配置

  validation:
    success: 验证成功
    failed: 验证失败
    errors: 验证错误
    warnings: 验证警告

  connection:
    testing: 正在测试连接...
    success: 连接成功
    failed: 连接失败
    timeout: 连接超时
    responseTime: 响应时间


