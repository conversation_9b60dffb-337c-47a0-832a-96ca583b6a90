# pekko {
#
#   extensions = [org.apache.pekko.persistence.Persistence]
#
# persistence {
#
#   journal {
#   plugin = "org.apache.pekko.persistence.journal.leveldb"
#   auto-start-journals = ["org.apache.pekko.persistence.journal.leveldb"]
# }
#
# //snapshot-store {
# //plugin = "org.apache.pekko.persistence.snapshot-store.local"
# //auto-start-snapshot-stores = ["org.apache.pekko.persistence.snapshot-store.local"]
# //}
#
# }
#
# }

pekko {
  # 使用集群作为 Actor 提供者
  loglevel = "DEBUG"
  actor {
    provider = "cluster"
  }

  # 配置远程通信
  remote {
    artery {
      canonical {
        hostname = "*************"
        port = 2551
      }
      transport = tcp
    }
  }

  # 配置集群
  cluster {
    debug {
          verbose-heartbeat = on
          verbose-membership = on
        }
    seed-nodes = [
      "pekko://TcsSystem@*************:2551"
      ,
      "pekko://TcsSystem@*************:2551"
    ]

    # 配置脑裂解决策略
    downing-provider-class = "org.apache.pekko.cluster.sbr.SplitBrainResolverProvider"

    # 配置分片
    sharding {
      # 使用分布式数据存储分片状态
      state-store-mode = ddata

      # 记忆分片分配状态
      remember-entities = on

      # 使用一致性哈希分片策略
      use-lease = off

      # 分片分配策略
      least-shard-allocation-strategy {
        # 节点间分片数量差异超过此阈值时触发重平衡
        rebalance-threshold = 10

        # 每次最多迁移的分片数量
        max-simultaneous-rebalance = 3
      }
    }
  }
}
