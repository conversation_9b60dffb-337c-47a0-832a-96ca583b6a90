spring:
  profiles:
    active: h2
  servlet:
    multipart:
#      单次请求文件大小设置
      max-file-size: 100MB
      max-request-size: 120MB
  flyway:
    enabled: true
    baseline-on-migrate: true
    out-of-order: true
    locations: classpath:sql
    table: tcs_flyway_schema_history
---
spring:
  mvc:
    servlet:
      load-on-startup: 1



---
spring:
  plugins:
    #启用开关
    enabled: false
    #运行模式：
    runtime-mode: @spring.plugins.runtime-mode@
    #扩展插件目录 【默认项目./plugins】
    path: plugins
    #备份插件目录 【默认项目./backup】
    backup-path: backup
    #调试插件路径，当runtime-mode 为 development时，直接使用idea调试
    dev-path:
#      - tcs-south-crcc
      - tcs-south-cucc
      - tcs-south-omc-siteweb
#      - tcs-south-cmcc
#      - tcs-south-cucc
      - tcs-south-cmcc
#      - tcs-south-seed
      # 暂时禁用所有插件
#       - tcs-south-ctcc
      # - tcs-north-test
      # - tcs-north-s6
      # - tcs-south-simulator
      # - tcs-south-modbus
      # - tcs-south-cmcc
---
# SPA 客户端配置
spring:
  boot:
    admin:
      client:
        enabled: false
        url: http://localhost:9800
  #        username: admin
  #        password: admin
        instance:
          service-url: http://localhost:9700
          service-host-type: IP
          metadata:
            tags:
              environment: test
              service-test-1: A large content
              service-test-2: A large content
              service-test-3: A large content
            service-test-4: A large content
            service-test-5: A large content
          prefer-ip: true
# SPA的EndPoints默认配置
management:
  health:
    redis:
      enabled: false
  endpoints:
    enabled-by-default: true
    web:
      base-path: /api/thing/manage
      exposure:
        include: '*'
  endpoint:
    refresh:
      enabled: true
    restart:
      enabled: true
    shutdown:
      enabled: true
    env:
      post:
        enabled: true
    health:
      show-details: ALWAYS
    logfile:
      enabled: true
      external-file: ./logs/siteweb-tcs.log
  trace:
    http:
      enabled: true

info:
  appName: boot-admin
  appVersion: 1.0.0

# Siteweb 配置服务客户端连接的服务器 - 暂时禁用
# http://*************:8011
siteweb:
 config:
   client:
     baseUrl: http://*************:8011
     idPrefix: thing-client

# ---
# spring:
#   main:
#     allow-bean-definition-overriding: true

webAuth:
  jwt:
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC/DZ8QrtQaoyFwO/fBno3LguBv2CWsUqJlCjh0opI3jrDnI8bTIRGotKLrknT0GSEXw5CtakEAa1C8tudwJ5nL09UAvQXRAgH23d/P6HSQVjDwh/2VIf9j9hh6meJGH/7xFr4HRMyB/cVHgqwvmiCNZIbvCWbyijcTXr0VPkPzL4sWF2pgdvJZzmgKkAz0xcOHqUfqp5YsyEIRovX8xmp2qcBzvpxHZYkaYujv/cO7FNc9bOA6dhh5ayZu3MeAU313g72nqt/nA4yTWq27d0zkKfdy17ZhKA+vYvLJKlrv+8tO5UDpIpB0vQhKd7+S2w9r7tHVGfezUWCZgKxZ/LL3AgMBAAECggEBALK1FRLP1crMyJxpG4javJueYj18G1EjQo/sjX5cCxU4vbSXPIWEqzX5MWPU7NzfHJtT7OKpPwAbYbwEAlxgTnXgQZ+dL/GfRSMbyxx4vX+9f62eJs72rCNesOsNQiCCEUCGG15FNl5pd706N8GXE9fuLmEtlEROkNHnjkpuobS4JWhGGJci4mNQqw4ZgCOuUr5YZ4QA/Mb6BBAu6bcvu6es23oGB84ZcxpMjh69PSwfiYFFsppuiJflzjQjohtyv99MqstDRvrzPi+i/T3qZ2W9FRUuP1pf/dMaamg7PoTAoOgJjKbSo7aEgLZIo/SF7TKnjtB6SmBnlWxi7HLyKyECgYEA+PP0CbhnkcVolsCQtLwa4lL/3mOurjjtgCWYXBkYjbZ9vJgGWw2oJ+HUBNhpEa7ELrzjUvXumgqCnicDeUC/9QJDCf029ldGje9GkrHsOSbU6IHzzdGbmYHwgfnjB6vFwZx5VjvT2t3VBMYeet5HqrL33KD+WxRYziel6URSbKkCgYEAxHYYcw0ORtq57g8N7SakScNE2oVf+/1H3WqSUJDg+T38WEkxU9M6sqnJ9q2v9QSR2QynxS0SZKxvwJc4LzUbiHMP7v20xhBrA5+a8kzjPqKlM50p0C/Nkt7pSwFiX+LwFQlMUV9tWdY/Z6fBhKks9uxpEPuCbCjDlCg0T0iNRp8CgYEA7WinlwV2LztUrD7jQJgKAz8npsrk8Fx1kTlI/LsqASrA6bMIjJiPfckMSbqfKC/EAtY66wiBDAFt4qhN1bn71QjdKY+CdJVyQTSn1ok6Pp5bd4dGG0cC3fdehnTpHo2evy4bQDM5q4TU+gJ9WqrTKWQWnx4gsnbK4X5J6BQxjlECgYBxtZi5Hplg0UBEVVpOJMt6FhdIE2JWy2ZI9WHyV6ifGg1wXAy848lZl4RZznXFbvurkPOZ4FiBBH06D0xppmdlNpPGU/nJmb8Wvc5E59OvcRwFH7YP1Vs64uJMk2SI8yTaSCNwBbeZA7R3HlWXnwNzd6noNmpqh72LhymfqfJ7KQKBgEjDxUq7q3KU0KeYiitT3WGOHQ5kGmfEwNFjuxMqJohyyJ4Z3ajHbIbrm2TZ5Wq3Ims+XcQFiOhZ8UIIUiOldjgFPlL5sBWLyh2Q0MO8lvBH+AJVAsirmNSTHzCIcvGDLEHnppSnmBOtC89h6n8Hdcz2Ybd1kVuYzrVXBnodakL0
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2fEK7UGqMhcDv3wZ6Ny4Lgb9glrFKiZQo4dKKSN46w5yPG0yERqLSi65J09BkhF8OQrWpBAGtQvLbncCeZy9PVAL0F0QIB9t3fz+h0kFYw8If9lSH/Y/YYepniRh/+8Ra+B0TMgf3FR4KsL5ogjWSG7wlm8oo3E169FT5D8y+LFhdqYHbyWc5oCpAM9MXDh6lH6qeWLMhCEaL1/MZqdqnAc76cR2WJGmLo7/3DuxTXPWzgOnYYeWsmbtzHgFN9d4O9p6rf5wOMk1qtu3dM5Cn3cte2YSgPr2LyySpa7/vLTuVA6SKQdL0ISne/ktsPa+7R1Rn3s1FgmYCsWfyy9wIDAQAB
  token:
    expireMinute: 30

# 中间件配置
middleware:
  encryption:
    enabled: true
    secret-key: ${MIDDLEWARE_ENCRYPTION_KEY:default-dev-key-not-for-production}

  # 资源配置
  resource:
    # 资源健康检查配置
    health-check:
      enabled: true
      initial-delay: 60000  # 启动后延迟60秒进行首次健康检查
      fixed-delay: 300000   # 每5分钟进行一次健康检查

  # 服务配置
  service:
    # 服务健康检查配置
    health-check:
      enabled: true
      initial-delay: 90000  # 启动后延迟90秒进行首次健康检查
      fixed-delay: 300000   # 每5分钟进行一次健康检查
#debug: true
server:
  port: 8550
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html
    min-response-size: 102400
  replica-count: 1 # 1-单机 2-双机 3-

# Pekko cluster configuration
pekko:
  cluster:
    is-seed-node: true
    hostname: ${PEKKO_HOSTNAME:*************}
    port: ${PEKKO_PORT:2551}
    seed-nodes: ${PEKKO_SEED_NODES:pekko://TcsSystem@*************:2551}
