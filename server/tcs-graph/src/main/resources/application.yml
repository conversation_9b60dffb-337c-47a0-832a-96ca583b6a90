spring:
  profiles:
    active: mysql, neo4j
  application:
    name: tcs-graph

# Server configuration
server:
  port: 9710
  servlet:
    context-path: /api/graph

# Logging configuration
logging:
  level:
    root: INFO
    com.siteweb.tcs.graph: DEBUG
    org.springframework.data.neo4j: INFO
    org.neo4j.driver: INFO

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: ALWAYS

# Application info
info:
  app:
    name: TCS Graph Management
    description: Graph management and query module for TCS
    version: 1.0.0
