spring:
  neo4j:
    uri: bolt://localhost:7687
    authentication:
      username: neo4j
      password: password
    connection:
      pool:
        max-connection-lifetime: 1h
        max-connection-pool-size: 100
        metrics-enabled: true
        connection-acquisition-timeout: 30s
        idle-time-before-connection-test: 1m
        max-transaction-retry-time: 30s

# Graph module configuration
tcs:
  graph:
    # Synchronization settings
    sync:
      # Batch size for synchronization
      batch-size: 1000
      # Interval in milliseconds for scheduled synchronization
      interval-ms: 3600000
      # Whether to enable automatic synchronization
      auto-sync-enabled: true
      # Initial delay in milliseconds before first synchronization
      initial-delay-ms: 60000
      # Maximum number of retries for failed synchronization
      max-retries: 3
      # Delay in milliseconds between retries
      retry-delay-ms: 5000
      # Whether to perform full sync on startup
      sync-on-startup: true

    # Consistency check settings
    consistency:
      # Whether to check consistency during synchronization
      check-enabled: true
      # Threshold for consistency warnings (percentage)
      warning-threshold: 95
      # Threshold for consistency errors (percentage)
      error-threshold: 80
      # Interval in milliseconds for scheduled consistency checks
      check-interval-ms: 86400000
      # Whether to auto-repair inconsistencies
      auto-repair: true

    # Query settings
    query:
      # Default limit for query results
      default-limit: 100
      # Maximum depth for relationship traversal
      max-traversal-depth: 5
      # Timeout in seconds for long-running queries
      timeout-seconds: 30
      # Whether to cache query results
      cache-enabled: true
      # Cache expiration in seconds
      cache-expiration-seconds: 300
      # Maximum cache size
      cache-max-size: 1000

    # Async task settings
    async:
      # Core pool size for async tasks
      core-pool-size: 5
      # Maximum pool size for async tasks
      max-pool-size: 10
      # Queue capacity for async tasks
      queue-capacity: 25
      # Thread name prefix for async tasks
      thread-name-prefix: graph-async-

    # Analysis settings
    analysis:
      # Maximum number of nodes to return in analysis results
      max-nodes: 1000
      # Whether to include node properties in analysis results
      include-properties: true
      # Whether to include relationship properties in analysis results
      include-relationship-properties: true
      # Default similarity threshold for finding similar nodes
      default-similarity-threshold: 0.5
