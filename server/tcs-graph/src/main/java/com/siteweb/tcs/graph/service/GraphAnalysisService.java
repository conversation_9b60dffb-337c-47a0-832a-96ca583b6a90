package com.siteweb.tcs.graph.service;

import java.util.List;
import java.util.Map;

/**
 * 图分析服务接口
 * 提供高级图分析功能，如影响分析和依赖分析
 */
public interface GraphAnalysisService {
    
    /**
     * 执行影响分析
     * 分析指定节点故障时可能影响的所有节点
     * 
     * @param nodeId 节点ID
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @param maxDepth 最大遍历深度
     * @return 受影响节点列表及其详细信息
     */
    Map<String, Object> performImpactAnalysis(String nodeId, String nodeType, int maxDepth);
    
    /**
     * 执行依赖分析
     * 分析指定节点所依赖的所有节点
     * 
     * @param nodeId 节点ID
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @param maxDepth 最大遍历深度
     * @return 依赖节点列表及其详细信息
     */
    Map<String, Object> performDependencyAnalysis(String nodeId, String nodeType, int maxDepth);
    
    /**
     * 查找共同依赖
     * 查找多个节点共同依赖的节点
     * 
     * @param nodeIds 节点ID列表
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @return 共同依赖节点列表
     */
    List<Map<String, Object>> findCommonDependencies(List<String> nodeIds, String nodeType);
    
    /**
     * 查找关键路径
     * 查找两个节点之间的所有路径并按重要性排序
     * 
     * @param sourceId 源节点ID
     * @param targetId 目标节点ID
     * @param maxDepth 最大遍历深度
     * @return 路径列表，按重要性排序
     */
    List<Map<String, Object>> findCriticalPaths(String sourceId, String targetId, int maxDepth);
    
    /**
     * 查找孤立节点
     * 查找没有任何关系连接的节点
     * 
     * @param nodeType 节点类型 (Region, Gateway, Device)，可为null查询所有类型
     * @return 孤立节点列表
     */
    List<Map<String, Object>> findIsolatedNodes(String nodeType);
    
    /**
     * 查找相似节点
     * 基于关系模式查找与指定节点相似的节点
     * 
     * @param nodeId 节点ID
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @param similarityThreshold 相似度阈值 (0-1)
     * @return 相似节点列表及其相似度
     */
    List<Map<String, Object>> findSimilarNodes(String nodeId, String nodeType, double similarityThreshold);
}
