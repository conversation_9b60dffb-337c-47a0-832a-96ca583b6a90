package com.siteweb.tcs.graph.service.impl;

import com.siteweb.tcs.graph.model.node.DeviceNode;
import com.siteweb.tcs.graph.model.node.GatewayNode;
import com.siteweb.tcs.graph.model.node.RegionNode;
import com.siteweb.tcs.graph.repository.DeviceNodeRepository;
import com.siteweb.tcs.graph.repository.GatewayNodeRepository;
import com.siteweb.tcs.graph.repository.RegionNodeRepository;
import com.siteweb.tcs.graph.service.GraphQueryService;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.Driver;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Implementation of the GraphQueryService interface
 */
@Service
@Slf4j
public class GraphQueryServiceImpl implements GraphQueryService {
    
    @Autowired
    private RegionNodeRepository regionNodeRepository;
    
    @Autowired
    private GatewayNodeRepository gatewayNodeRepository;
    
    @Autowired
    private DeviceNodeRepository deviceNodeRepository;
    
    @Autowired
    private Driver neo4jDriver;
    
    @Value("${tcs.graph.query.max-traversal-depth:5}")
    private int maxTraversalDepth;
    
    @Value("${tcs.graph.query.default-limit:100}")
    private int defaultLimit;
    
    @Override
    public List<RegionNode> getRegionHierarchy() {
        log.info("Getting region hierarchy");
        
        try {
            List<RegionNode> rootRegions = regionNodeRepository.findRootRegions();
            log.info("Found {} root regions", rootRegions.size());
            return rootRegions;
        } catch (Exception e) {
            log.error("Error getting region hierarchy", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public Optional<RegionNode> getRegionWithDescendants(Long regionId, int depth) {
        log.info("Getting region with ID {} and its descendants to depth {}", regionId, depth);
        
        try {
            RegionNode region = regionNodeRepository.findById(regionId).orElse(null);
            if (region == null) {
                log.warn("Region not found with ID: {}", regionId);
                return Optional.empty();
            }
            
            int actualDepth = Math.min(depth, maxTraversalDepth);
            return regionNodeRepository.findRegionWithDescendants(region.getSourceId(), actualDepth);
        } catch (Exception e) {
            log.error("Error getting region with descendants", e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<GatewayNode> getGatewaysInRegion(Long regionId, int depth) {
        log.info("Getting gateways in region with ID {} to depth {}", regionId, depth);
        
        try {
            RegionNode region = regionNodeRepository.findById(regionId).orElse(null);
            if (region == null) {
                log.warn("Region not found with ID: {}", regionId);
                return Collections.emptyList();
            }
            
            int actualDepth = Math.min(depth, maxTraversalDepth);
            return gatewayNodeRepository.findGatewaysInRegion(region.getSourceId(), actualDepth);
        } catch (Exception e) {
            log.error("Error getting gateways in region", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public List<DeviceNode> getDevicesInRegion(Long regionId, int depth) {
        log.info("Getting devices in region with ID {} to depth {}", regionId, depth);
        
        try {
            RegionNode region = regionNodeRepository.findById(regionId).orElse(null);
            if (region == null) {
                log.warn("Region not found with ID: {}", regionId);
                return Collections.emptyList();
            }
            
            int actualDepth = Math.min(depth, maxTraversalDepth);
            return deviceNodeRepository.findDevicesInRegion(region.getSourceId(), actualDepth);
        } catch (Exception e) {
            log.error("Error getting devices in region", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public List<DeviceNode> getDevicesByTypeInRegion(Long regionId, String deviceType, int depth) {
        log.info("Getting devices of type {} in region with ID {} to depth {}", deviceType, regionId, depth);
        
        try {
            RegionNode region = regionNodeRepository.findById(regionId).orElse(null);
            if (region == null) {
                log.warn("Region not found with ID: {}", regionId);
                return Collections.emptyList();
            }
            
            int actualDepth = Math.min(depth, maxTraversalDepth);
            return deviceNodeRepository.findDevicesInRegionByType(region.getSourceId(), deviceType, actualDepth);
        } catch (Exception e) {
            log.error("Error getting devices by type in region", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public List<DeviceNode> getDevicesByManufacturerInRegion(Long regionId, String manufacturer, int depth) {
        log.info("Getting devices from manufacturer {} in region with ID {} to depth {}", manufacturer, regionId, depth);
        
        try {
            RegionNode region = regionNodeRepository.findById(regionId).orElse(null);
            if (region == null) {
                log.warn("Region not found with ID: {}", regionId);
                return Collections.emptyList();
            }
            
            int actualDepth = Math.min(depth, maxTraversalDepth);
            
            try (Session session = neo4jDriver.session()) {
                String query = "MATCH (r:Region {source_id: $regionSourceId})-[:CONTAINS*1..$depth]->(g:Gateway)-[:HAS_DEVICE]->(d:Device) " +
                        "WHERE d.manufacturer = $manufacturer " +
                        "RETURN d";
                
                Result result = session.run(query, Map.of(
                        "regionSourceId", region.getSourceId(),
                        "manufacturer", manufacturer,
                        "depth", actualDepth
                ));
                
                List<DeviceNode> devices = new ArrayList<>();
                result.list().forEach(record -> {
                    Map<String, Object> deviceProps = record.get("d").asNode().asMap();
                    String deviceId = deviceProps.get("source_id").toString();
                    deviceNodeRepository.findBySourceId(deviceId).ifPresent(devices::add);
                });
                
                return devices;
            }
        } catch (Exception e) {
            log.error("Error getting devices by manufacturer in region", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public Optional<GatewayNode> getGatewayWithDevices(String gatewayId) {
        log.info("Getting gateway with ID {} and its devices", gatewayId);
        
        try {
            return gatewayNodeRepository.findGatewayWithDevices(gatewayId);
        } catch (Exception e) {
            log.error("Error getting gateway with devices", e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<Map<String, Object>> findPath(String sourceId, String targetId) {
        log.info("Finding path between source {} and target {}", sourceId, targetId);
        
        try (Session session = neo4jDriver.session()) {
            String query = "MATCH path = shortestPath((a)-[*..10]-(b)) " +
                    "WHERE a.source_id = $sourceId AND b.source_id = $targetId " +
                    "RETURN path";
            
            Result result = session.run(query, Map.of(
                    "sourceId", sourceId,
                    "targetId", targetId
            ));
            
            List<Map<String, Object>> paths = new ArrayList<>();
            result.list().forEach(record -> {
                List<Map<String, Object>> path = new ArrayList<>();
                record.get("path").asPath().nodes().forEach(node -> {
                    Map<String, Object> nodeMap = new HashMap<>(node.asMap());
                    nodeMap.put("id", node.id());
                    nodeMap.put("labels", node.labels());
                    path.add(nodeMap);
                });
                paths.add(Map.of("nodes", path));
            });
            
            return paths;
        } catch (Exception e) {
            log.error("Error finding path", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public Map<String, Object> getGraphStatistics() {
        log.info("Getting graph statistics");
        
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            long regionCount = regionNodeRepository.count();
            long gatewayCount = gatewayNodeRepository.count();
            long deviceCount = deviceNodeRepository.count();
            
            statistics.put("nodeCount", Map.of(
                    "regions", regionCount,
                    "gateways", gatewayCount,
                    "devices", deviceCount,
                    "total", regionCount + gatewayCount + deviceCount
            ));
            
            try (Session session = neo4jDriver.session()) {
                String query = "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count";
                Result result = session.run(query);
                
                Map<String, Long> relationshipCounts = new HashMap<>();
                result.list().forEach(record -> {
                    relationshipCounts.put(record.get("type").asString(), record.get("count").asLong());
                });
                
                statistics.put("relationshipCount", relationshipCounts);
                
                // Get some additional statistics
                query = "MATCH (r:Region) RETURN count(r) as regionCount, " +
                        "sum(size((r)-[:CONTAINS]->(:Region))) as regionRelationships, " +
                        "sum(size((r)-[:CONTAINS]->(:Gateway))) as gatewayRelationships";
                result = session.run(query);
                
                if (result.hasNext()) {
                    Record record = result.next();
                    statistics.put("regionStats", Map.of(
                            "count", record.get("regionCount").asLong(),
                            "childRegions", record.get("regionRelationships").asLong(),
                            "gateways", record.get("gatewayRelationships").asLong()
                    ));
                }
                
                query = "MATCH (g:Gateway) RETURN count(g) as gatewayCount, " +
                        "sum(size((g)-[:HAS_DEVICE]->(:Device))) as deviceRelationships";
                result = session.run(query);
                
                if (result.hasNext()) {
                    Record record = result.next();
                    statistics.put("gatewayStats", Map.of(
                            "count", record.get("gatewayCount").asLong(),
                            "devices", record.get("deviceRelationships").asLong()
                    ));
                }
            }
            
            log.info("Graph statistics: {} regions, {} gateways, {} devices",
                    regionCount, gatewayCount, deviceCount);
        } catch (Exception e) {
            log.error("Error getting graph statistics", e);
            statistics.put("error", e.getMessage());
        }
        
        return statistics;
    }
}
