package com.siteweb.tcs.graph.model.relationship;

import com.siteweb.tcs.graph.model.node.BaseNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

/**
 * Contains relationship entity for Neo4j
 * Used for region-to-region and region-to-gateway relationships
 */
@RelationshipProperties
@Data
@EqualsAndHashCode(callSuper = true)
public class ContainsRelationship extends BaseRelationship {
    
    @TargetNode
    private BaseNode target;
}
