package com.siteweb.tcs.graph.repository;

import com.siteweb.tcs.graph.model.node.RegionNode;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for RegionNode entities
 */
@Repository
public interface RegionNodeRepository extends Neo4jRepository<RegionNode, Long> {
    
    /**
     * Find a region by its source ID
     * 
     * @param sourceId the source ID from the relational database
     * @return the region node if found
     */
    Optional<RegionNode> findBySourceId(String sourceId);
    
    /**
     * Find all regions that don't have a parent region
     * 
     * @return list of root regions
     */
    @Query("MATCH (r:Region) WHERE NOT (r)<-[:CONTAINS]-(:Region) RETURN r")
    List<RegionNode> findRootRegions();
    
    /**
     * Find all regions with their child regions and gateways
     * 
     * @return list of all regions with relationships
     */
    @Query("MATCH (r:Region) OPTIONAL MATCH (r)-[:CONTAINS]->(cr:Region) " +
           "OPTIONAL MATCH (r)-[:CONTAINS]->(g:Gateway) RETURN r, cr, g")
    List<RegionNode> findAllWithRelationships();
    
    /**
     * Find a region with all its descendants (regions and gateways)
     * 
     * @param sourceId the source ID of the region
     * @param depth the maximum depth to traverse
     * @return the region with all its descendants
     */
    @Query("MATCH (r:Region {source_id: $sourceId}) " +
           "OPTIONAL MATCH (r)-[:CONTAINS*1..$depth]->(d) " +
           "RETURN r, collect(d) as descendants")
    Optional<RegionNode> findRegionWithDescendants(@Param("sourceId") String sourceId, @Param("depth") int depth);
}
