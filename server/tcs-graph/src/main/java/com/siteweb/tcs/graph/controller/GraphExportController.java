package com.siteweb.tcs.graph.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.graph.service.GraphExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 图数据导出控制器
 * 提供导出图数据的REST API
 */
@RestController
@RequestMapping("/export")
@Slf4j
public class GraphExportController {
    
    @Autowired
    private GraphExportService graphExportService;
    
    @Value("${tcs.graph.query.max-traversal-depth:5}")
    private int maxTraversalDepth;
    
    /**
     * 导出整个图数据为JSON格式
     * 
     * @return 包含图数据的JSON
     */
    @GetMapping(value = "/json", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> exportGraphToJson() {
        log.info("收到导出图数据为JSON格式的请求");
        
        try {
            String jsonData = graphExportService.exportGraphToJson();
            return ResponseEntity.ok(jsonData);
        } catch (Exception e) {
            log.error("导出图数据为JSON格式时出错", e);
            return ResponseEntity.status(500).body("{ \"error\": \"" + e.getMessage() + "\" }");
        }
    }
    
    /**
     * 导出指定区域的图数据为JSON格式
     * 
     * @param regionId 区域ID
     * @param depth 最大遍历深度 (可选，默认为1)
     * @return 包含区域图数据的JSON
     */
    @GetMapping(value = "/json/regions/{regionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> exportRegionToJson(
            @PathVariable Long regionId,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("收到导出区域 {} 的图数据为JSON格式的请求，深度为 {}", regionId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            String jsonData = graphExportService.exportRegionToJson(regionId, actualDepth);
            return ResponseEntity.ok(jsonData);
        } catch (Exception e) {
            log.error("导出区域图数据为JSON格式时出错", e);
            return ResponseEntity.status(500).body("{ \"error\": \"" + e.getMessage() + "\" }");
        }
    }
    
    /**
     * 导出指定网关的图数据为JSON格式
     * 
     * @param gatewayId 网关ID
     * @return 包含网关图数据的JSON
     */
    @GetMapping(value = "/json/gateways/{gatewayId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> exportGatewayToJson(@PathVariable String gatewayId) {
        log.info("收到导出网关 {} 的图数据为JSON格式的请求", gatewayId);
        
        try {
            String jsonData = graphExportService.exportGatewayToJson(gatewayId);
            return ResponseEntity.ok(jsonData);
        } catch (Exception e) {
            log.error("导出网关图数据为JSON格式时出错", e);
            return ResponseEntity.status(500).body("{ \"error\": \"" + e.getMessage() + "\" }");
        }
    }
    
    /**
     * 导出图统计信息为JSON格式
     * 
     * @return 包含图统计信息的JSON
     */
    @GetMapping(value = "/json/statistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> exportStatisticsToJson() {
        log.info("收到导出图统计信息为JSON格式的请求");
        
        try {
            String jsonData = graphExportService.exportStatisticsToJson();
            return ResponseEntity.ok(jsonData);
        } catch (Exception e) {
            log.error("导出图统计信息为JSON格式时出错", e);
            return ResponseEntity.status(500).body("{ \"error\": \"" + e.getMessage() + "\" }");
        }
    }
    
    /**
     * 导出图数据为GraphML格式
     * 
     * @param download 是否作为文件下载 (可选，默认为false)
     * @return 包含图数据的GraphML
     */
    @GetMapping(value = "/graphml", produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> exportGraphToGraphML(
            @RequestParam(required = false, defaultValue = "false") boolean download) {
        log.info("收到导出图数据为GraphML格式的请求");
        
        try {
            String graphMLData = graphExportService.exportGraphToGraphML();
            
            if (download) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_XML);
                headers.setContentDispositionFormData("attachment", "graph.graphml");
                
                return ResponseEntity.ok()
                        .headers(headers)
                        .body(graphMLData);
            } else {
                return ResponseEntity.ok(graphMLData);
            }
        } catch (Exception e) {
            log.error("导出图数据为GraphML格式时出错", e);
            return ResponseEntity.status(500).body("<!-- Error: " + e.getMessage() + " -->");
        }
    }
    
    /**
     * 导出图数据为CSV格式
     * 
     * @return 包含图数据的CSV文件列表
     */
    @GetMapping(value = "/csv", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> exportGraphToCSV() {
        log.info("收到导出图数据为CSV格式的请求");
        
        try {
            Map<String, String> csvFiles = graphExportService.exportGraphToCSV();
            return ResponseHelper.successful(csvFiles);
        } catch (Exception e) {
            log.error("导出图数据为CSV格式时出错", e);
            return ResponseHelper.failed("导出图数据为CSV格式时出错: " + e.getMessage());
        }
    }
    
    /**
     * 下载节点CSV文件
     * 
     * @return 节点CSV文件
     */
    @GetMapping(value = "/csv/nodes", produces = "text/csv")
    public ResponseEntity<byte[]> downloadNodesCSV() {
        log.info("收到下载节点CSV文件的请求");
        
        try {
            Map<String, String> csvFiles = graphExportService.exportGraphToCSV();
            String nodesCSV = csvFiles.get("nodes.csv");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment", "nodes.csv");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(nodesCSV.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("下载节点CSV文件时出错", e);
            return ResponseEntity.status(500).body(("Error: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }
    
    /**
     * 下载关系CSV文件
     * 
     * @return 关系CSV文件
     */
    @GetMapping(value = "/csv/relationships", produces = "text/csv")
    public ResponseEntity<byte[]> downloadRelationshipsCSV() {
        log.info("收到下载关系CSV文件的请求");
        
        try {
            Map<String, String> csvFiles = graphExportService.exportGraphToCSV();
            String relationshipsCSV = csvFiles.get("relationships.csv");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment", "relationships.csv");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(relationshipsCSV.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("下载关系CSV文件时出错", e);
            return ResponseEntity.status(500).body(("Error: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }
}
