package com.siteweb.stream.service.service;


import com.siteweb.stream.common.stream.GraphInstanceStateProbe;
import com.siteweb.stream.core.entity.StreamGraph;
import com.siteweb.stream.core.provider.StreamGraphProvider;
import com.siteweb.stream.core.manager.StreamGraphInstanceManager;
import org.apache.pekko.actor.ActorContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StreamGraphInstanceService {

    @Autowired
    private StreamGraphProvider streamGraphProvider;
    @Autowired
    private StreamGraphService streamGraphService;

//    @Autowired
//    private StreamGraphInstanceManager streamGraphInstanceManager;

    public boolean startStreamGraphInstance(long streamGraphInstanceId) {
        try {
            StreamGraph graph = streamGraphService.findGraph(streamGraphInstanceId);
            // TODO 上下文只能从Actor内部获取 具体创建逻辑需在Actor内部进行
            ActorContext actorContext = null;
            StreamGraphInstanceManager.getInstance().createGraph(actorContext, graph, null);
            StreamGraphInstanceManager.getInstance().startGraph(streamGraphInstanceId);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void stopStreamGraphInstance(long streamGraphInstanceId) {
        StreamGraphInstanceManager.getInstance().stopGraph(streamGraphInstanceId);
    }

    public GraphInstanceStateProbe getGraphInstanceState(long streamGraphInstanceId) {
        return StreamGraphInstanceManager.getInstance().getStreamGraphInstanceState(streamGraphInstanceId);
    }
}
