package com.siteweb.tcs.tracking.connector.process;

import com.siteweb.tcs.common.actions.SubscribeAction;
import com.siteweb.tcs.common.actions.UnsubscribeAction;
import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.tracking.connector.letter.TrackingDataMessage;
import com.siteweb.tcs.tracking.connector.letter.TrackingStrategyMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 埋点系统根Actor
 * 负责管理埋点数据的收集、处理和分发
 */
@Slf4j
public class TrackingGuard extends ProbeActor {

    private final RedisTemplate<String, Object> redisTemplate;
    private final List<ActorRef> subscribers = new ArrayList<>();

    // 子Actor引用
    private final ActorRef trackingDataAdapter;
    private final ActorRef trackingDataStore;
    private final ActorRef trackingDataSpout;
    private final ActorRef trackingStrategyManager;
    private final ActorRef trackingAlertManager;
    private final ActorRef trackingAggregator;

    // 日志队列名称
    private static final String TRACKING_LOG = "TrackingLog";
    private static final String STRATEGY_LOG = "StrategyLog";
    private static final String ALERT_LOG = "AlertLog";

    /**
     * 创建TrackingGuard的Props
     */
    public static Props props(RedisTemplate<String, Object> redisTemplate) {
        return Props.create(TrackingGuard.class, redisTemplate);
    }

    /**
     * 构造函数
     */
    public TrackingGuard(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;

        // 初始化日志队列
        getProbe().addWindowLog(TRACKING_LOG);
        getProbe().addWindowLog(STRATEGY_LOG);
        getProbe().addWindowLog(ALERT_LOG);

        // 初始化计数器
        getProbe().addCounter("TrackingDataCounter");
        getProbe().addCounter("StrategyCounter");
        getProbe().addCounter("AlertCounter");

        // 初始化仪表盘
        getProbe().addGauge("ActiveStrategies");
        getProbe().addGauge("ActiveAlerts");

        // 创建子Actor
        trackingDataSpout = getContext().actorOf(
                TrackingDataSpout.props(),
                "TrackingDataSpout"
        );

        trackingDataStore = getContext().actorOf(
                TrackingDataStore.props(trackingDataSpout),
                "TrackingDataStore"
        );

        trackingDataAdapter = getContext().actorOf(
                TrackingDataAdapter.props(trackingDataStore),
                "TrackingDataAdapter"
        );

        trackingStrategyManager = getContext().actorOf(
                TrackingStrategyManager.props(trackingDataAdapter),
                "TrackingStrategyManager"
        );

        trackingAlertManager = getContext().actorOf(
                TrackingAlertManager.props(),
                "TrackingAlertManager"
        );

        trackingAggregator = getContext().actorOf(
                TrackingAggregator.props(trackingAlertManager),
                "TrackingAggregator"
        );

        // 日志记录
        getProbe().info("TrackingGuard initialized successfully");
    }

    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(SubscribeAction.class, this::handleSubscribe)
                .match(UnsubscribeAction.class, this::handleUnsubscribe)
                .match(TrackingDataMessage.class, this::handleTrackingData)
                .match(TrackingStrategyMessage.class, this::handleTrackingStrategy)
                .matchAny(this::unhandled)
                .build()
                .orElse(super.createReceive());
    }

    /**
     * 处理订阅请求
     */
    private void handleSubscribe(SubscribeAction action) {
        if (!subscribers.contains(action.getSubscriber())) {
            subscribers.add(action.getSubscriber());
            getProbe().info("New subscriber registered: " + action.getSubscriber().path().name());
        }
    }

    /**
     * 处理取消订阅请求
     */
    private void handleUnsubscribe(UnsubscribeAction action) {
        subscribers.remove(action.getSubscriber());
        getProbe().info("Subscriber unregistered: " + action.getSubscriber().path().name());
    }

    /**
     * 处理埋点数据消息
     */
    private void handleTrackingData(TrackingDataMessage message) {
        getProbe().enqueueWindowLogItem(TRACKING_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Received tracking data: " + message.toString()));

        // 转发到数据适配器
        trackingDataAdapter.tell(message, getSelf());

        // 更新计数器
        getProbe().incrementCounterAmount("TrackingDataCounter", 1);
    }

    /**
     * 处理埋点策略消息
     */
    private void handleTrackingStrategy(TrackingStrategyMessage message) {
        getProbe().enqueueWindowLogItem(STRATEGY_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Received tracking strategy: " + message.toString()));

        // 转发到策略管理器
        trackingStrategyManager.tell(message, getSelf());

        // 更新计数器
        getProbe().incrementCounterAmount("StrategyCounter", 1);
    }

}
