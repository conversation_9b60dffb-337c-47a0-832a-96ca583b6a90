package com.siteweb.tcs.tracking.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.tracking.dal.entity.TrackingStrategy;
import com.siteweb.tcs.tracking.service.TrackingStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 埋点策略控制器
 * 提供埋点策略的REST API
 */
@Slf4j
@RestController
@RequestMapping("/tracking/strategy")
@Api(tags = "埋点策略管理")
public class TrackingStrategyController {

    @Autowired
    private TrackingStrategyService strategyService;

    /**
     * 获取所有策略
     */
    @ApiOperation("获取所有埋点策略")
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllStrategies() {
        List<TrackingStrategy> strategies = strategyService.getAllStrategies();
        return ResponseHelper.successful(strategies);
    }

    /**
     * 获取策略详情
     */
    @ApiOperation("获取埋点策略详情")
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStrategyById(@PathVariable("id") Integer id) {
        TrackingStrategy strategy = strategyService.getStrategyById(id);
        if (strategy == null) {
            return ResponseHelper.failed("Strategy not found: " + id);
        }
        return ResponseHelper.successful(strategy);
    }

    /**
     * 创建策略
     */
    @ApiOperation("创建埋点策略")
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createStrategy(@RequestBody TrackingStrategy strategy) {
        try {
            TrackingStrategy createdStrategy = strategyService.createStrategy(strategy);
            return ResponseHelper.successful(createdStrategy);
        } catch (Exception e) {
            log.error("Error creating strategy", e);
            return ResponseHelper.failed("Error creating strategy: " + e.getMessage());
        }
    }

    /**
     * 更新策略
     */
    @ApiOperation("更新埋点策略")
    @PutMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateStrategy(@RequestBody TrackingStrategy strategy) {
        try {
            TrackingStrategy updatedStrategy = strategyService.updateStrategy(strategy);
            return ResponseHelper.successful(updatedStrategy);
        } catch (Exception e) {
            log.error("Error updating strategy", e);
            return ResponseHelper.failed("Error updating strategy: " + e.getMessage());
        }
    }

    /**
     * 删除策略
     */
    @ApiOperation("删除埋点策略")
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteStrategy(@PathVariable("id") Integer id) {
        try {
            strategyService.deleteStrategy(id);
            return ResponseHelper.successful();
        } catch (Exception e) {
            log.error("Error deleting strategy", e);
            return ResponseHelper.failed("Error deleting strategy: " + e.getMessage());
        }
    }

    /**
     * 激活策略
     */
    @ApiOperation("激活埋点策略")
    @PostMapping(value = "/{id}/activate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> activateStrategy(@PathVariable("id") Integer id) {
        try {
            strategyService.activateStrategy(id);
            return ResponseHelper.successful();
        } catch (Exception e) {
            log.error("Error activating strategy", e);
            return ResponseHelper.failed("Error activating strategy: " + e.getMessage());
        }
    }

    /**
     * 停用策略
     */
    @ApiOperation("停用埋点策略")
    @PostMapping(value = "/{id}/deactivate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deactivateStrategy(@PathVariable("id") Integer id) {
        try {
            strategyService.deactivateStrategy(id);
            return ResponseHelper.successful();
        } catch (Exception e) {
            log.error("Error deactivating strategy", e);
            return ResponseHelper.failed("Error deactivating strategy: " + e.getMessage());
        }
    }

    /**
     * 暂停策略
     */
    @ApiOperation("暂停埋点策略")
    @PostMapping(value = "/{id}/pause", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> pauseStrategy(@PathVariable("id") Integer id) {
        try {
            strategyService.pauseStrategy(id);
            return ResponseHelper.successful();
        } catch (Exception e) {
            log.error("Error pausing strategy", e);
            return ResponseHelper.failed("Error pausing strategy: " + e.getMessage());
        }
    }
}
