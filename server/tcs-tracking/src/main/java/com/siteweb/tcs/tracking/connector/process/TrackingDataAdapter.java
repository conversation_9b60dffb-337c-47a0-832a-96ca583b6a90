package com.siteweb.tcs.tracking.connector.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.tracking.connector.letter.TrackingDataMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 埋点数据适配器Actor
 * 负责接收和预处理埋点数据
 */
@Slf4j
public class TrackingDataAdapter extends AbstractActor {

    private final ActorProbe probe;
    private final ActorRef trackingDataStore;
    
    // 日志队列名称
    private static final String ADAPTER_LOG = "AdapterLog";

    /**
     * 创建TrackingDataAdapter的Props
     */
    public static Props props(ActorRef trackingDataStore) {
        return Props.create(TrackingDataAdapter.class, trackingDataStore);
    }

    /**
     * 构造函数
     */
    public TrackingDataAdapter(ActorRef trackingDataStore) {
        this.trackingDataStore = trackingDataStore;
        this.probe = createProbe(this);
        
        // 初始化日志队列
        probe.addWindowLog(ADAPTER_LOG);
        
        // 初始化计数器
        probe.addCounter("ReceivedDataCounter");
        probe.addCounter("ProcessedDataCounter");
        probe.addCounter("FilteredDataCounter");
        
        // 日志记录
        probe.info("TrackingDataAdapter initialized successfully");
    }

    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(TrackingDataMessage.class, this::handleTrackingData)
                .matchAny(this::unhandled)
                .build();
    }

    /**
     * 处理埋点数据消息
     */
    private void handleTrackingData(TrackingDataMessage message) {
        // 更新接收计数器
        probe.incrementCounterAmount("ReceivedDataCounter", 1);
        
        // 记录日志
        probe.enqueueWindowLogItem(ADAPTER_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Processing tracking data: " + message.getWindowLogString()));
        
        // 数据验证和预处理
        if (validateTrackingData(message)) {
            // 转发到数据存储Actor
            trackingDataStore.tell(message, getSelf());
            
            // 更新处理计数器
            probe.incrementCounterAmount("ProcessedDataCounter", 1);
        } else {
            // 更新过滤计数器
            probe.incrementCounterAmount("FilteredDataCounter", 1);
            
            // 记录警告日志
            probe.warn("Invalid tracking data filtered: " + message.getWindowLogString());
        }
    }

    /**
     * 验证埋点数据
     */
    private boolean validateTrackingData(TrackingDataMessage message) {
        // 验证必要字段
        if (message.getStrategyId() == null || message.getPointId() == null) {
            probe.warn("Missing required fields in tracking data");
            return false;
        }
        
        // 验证时间戳
        if (message.getTimestamp() == null) {
            // 如果没有时间戳，使用当前时间
            message.setTimestamp(java.time.LocalDateTime.now());
        }
        
        // 验证数据
        if (message.getData() == null || message.getData().isEmpty()) {
            probe.warn("Empty data in tracking message");
            return false;
        }
        
        return true;
    }

    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
}
