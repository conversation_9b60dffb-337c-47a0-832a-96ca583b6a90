package com.siteweb.tcs.tracking.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.tracking.dal.entity.TrackingData;
import com.siteweb.tcs.tracking.service.TrackingDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 埋点数据控制器
 * 提供埋点数据的REST API
 */
@Slf4j
@RestController
@RequestMapping("/tracking/data")
@Api(tags = "埋点数据管理")
public class TrackingDataController {

    @Autowired
    private TrackingDataService dataService;

    /**
     * 收集埋点数据
     */
    @ApiOperation("收集埋点数据")
    @PostMapping(value = "/collect", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> collectData(
            @RequestParam("strategyId") Integer strategyId,
            @RequestParam("pointCode") String pointCode,
            @RequestBody Map<String, Object> data,
            @RequestParam(value = "sourceId", required = false) String sourceId,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "sessionId", required = false) String sessionId) {
        
        try {
            dataService.collectData(strategyId, pointCode, data, sourceId, userId, sessionId);
            return ResponseHelper.successful();
        } catch (Exception e) {
            log.error("Error collecting tracking data", e);
            return ResponseHelper.failed("Error collecting tracking data: " + e.getMessage());
        }
    }

    /**
     * 查询埋点数据
     */
    @ApiOperation("查询埋点数据")
    @GetMapping(value = "/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> queryData(
            @RequestParam(value = "strategyId", required = false) Integer strategyId,
            @RequestParam(value = "pointId", required = false) Integer pointId,
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(value = "sourceId", required = false) String sourceId,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "limit", defaultValue = "100") Integer limit) {
        
        try {
            List<TrackingData> dataList = dataService.queryData(strategyId, pointId, startTime, endTime, sourceId, userId, limit);
            return ResponseHelper.successful(dataList);
        } catch (Exception e) {
            log.error("Error querying tracking data", e);
            return ResponseHelper.failed("Error querying tracking data: " + e.getMessage());
        }
    }

    /**
     * 获取埋点数据统计
     */
    @ApiOperation("获取埋点数据统计")
    @GetMapping(value = "/statistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDataStatistics(
            @RequestParam(value = "strategyId", required = false) Integer strategyId,
            @RequestParam(value = "pointId", required = false) Integer pointId,
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            Map<String, Object> statistics = dataService.getDataStatistics(strategyId, pointId, startTime, endTime);
            return ResponseHelper.successful(statistics);
        } catch (Exception e) {
            log.error("Error getting tracking data statistics", e);
            return ResponseHelper.failed("Error getting tracking data statistics: " + e.getMessage());
        }
    }
}
