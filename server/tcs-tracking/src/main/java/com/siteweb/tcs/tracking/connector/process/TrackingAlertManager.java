package com.siteweb.tcs.tracking.connector.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.tracking.dal.entity.TrackingAlert;
import com.siteweb.tcs.tracking.dal.entity.TrackingAlertRule;
import com.siteweb.tcs.tracking.dal.entity.TrackingAggregation;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 埋点告警管理Actor
 * 负责管理埋点告警规则和生成告警
 */
@Slf4j
public class TrackingAlertManager extends AbstractActor {

    private final ActorProbe probe;
    
    // 告警规则缓存
    private final Map<Integer, TrackingAlertRule> ruleCache = new ConcurrentHashMap<>();
    
    // 告警状态缓存
    private final Map<String, TrackingAlert> activeAlerts = new ConcurrentHashMap<>();
    
    // 日志队列名称
    private static final String ALERT_LOG = "AlertLog";

    /**
     * 创建TrackingAlertManager的Props
     */
    public static Props props() {
        return Props.create(TrackingAlertManager.class);
    }

    /**
     * 构造函数
     */
    public TrackingAlertManager() {
        this.probe = createProbe(this);
        
        // 初始化日志队列
        probe.addWindowLog(ALERT_LOG);
        
        // 初始化计数器
        probe.addCounter("AlertRuleCounter");
        probe.addCounter("AlertGeneratedCounter");
        probe.addCounter("AlertResolvedCounter");
        
        // 初始化仪表盘
        probe.addGauge("ActiveAlerts");
        probe.addGauge("ActiveRules");
        
        // 日志记录
        probe.info("TrackingAlertManager initialized successfully");
    }

    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(TrackingAlertRule.class, this::handleAlertRule)
                .match(TrackingAggregation.class, this::handleAggregation)
                .match(AlertCommand.class, this::handleAlertCommand)
                .matchAny(this::unhandled)
                .build();
    }

    /**
     * 处理告警规则
     */
    private void handleAlertRule(TrackingAlertRule rule) {
        // 记录日志
        probe.enqueueWindowLogItem(ALERT_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Processing alert rule: " + rule.getName()));
        
        // 根据状态处理规则
        if (TrackingAlertRule.RuleStatus.ACTIVE.name().equals(rule.getStatus())) {
            // 添加或更新规则
            ruleCache.put(rule.getId(), rule);
            probe.info("Alert rule activated: " + rule.getName());
        } else {
            // 移除规则
            ruleCache.remove(rule.getId());
            probe.info("Alert rule deactivated: " + rule.getName());
        }
        
        // 更新计数器和仪表盘
        probe.incrementCounterAmount("AlertRuleCounter", 1);
        probe.updateGauge("ActiveRules", ruleCache.size());
    }

    /**
     * 处理聚合数据
     */
    private void handleAggregation(TrackingAggregation aggregation) {
        // 记录日志
        probe.enqueueWindowLogItem(ALERT_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Checking aggregation against alert rules: " + aggregation.getAggregationType() +
                        " for point " + aggregation.getPointId()));
        
        // 检查所有适用的规则
        for (TrackingAlertRule rule : ruleCache.values()) {
            // 只检查与聚合点位匹配的规则
            if (rule.getPointId().equals(aggregation.getPointId())) {
                checkRule(rule, aggregation);
            }
        }
    }

    /**
     * 处理告警命令
     */
    private void handleAlertCommand(AlertCommand command) {
        switch (command.getType()) {
            case ACKNOWLEDGE:
                acknowledgeAlert(command.getAlertId());
                break;
            case RESOLVE:
                resolveAlert(command.getAlertId());
                break;
            default:
                probe.warn("Unknown alert command: " + command.getType());
                break;
        }
    }

    /**
     * 检查规则
     */
    private void checkRule(TrackingAlertRule rule, TrackingAggregation aggregation) {
        try {
            // 根据条件类型检查
            switch (TrackingAlertRule.ConditionType.valueOf(rule.getConditionType())) {
                case THRESHOLD:
                    checkThresholdCondition(rule, aggregation);
                    break;
                case CHANGE_RATE:
                    checkChangeRateCondition(rule, aggregation);
                    break;
                case ANOMALY:
                    checkAnomalyCondition(rule, aggregation);
                    break;
                default:
                    probe.warn("Unknown condition type: " + rule.getConditionType());
                    break;
            }
        } catch (Exception e) {
            probe.error("Error checking rule: " + e.getMessage());
            log.error("Error checking rule", e);
        }
    }

    /**
     * 检查阈值条件
     */
    private void checkThresholdCondition(TrackingAlertRule rule, TrackingAggregation aggregation) {
        // 解析条件值
        String[] parts = rule.getConditionValue().split(":");
        if (parts.length != 2) {
            probe.warn("Invalid threshold condition format: " + rule.getConditionValue());
            return;
        }
        
        String operator = parts[0];
        double threshold;
        try {
            threshold = Double.parseDouble(parts[1]);
        } catch (NumberFormatException e) {
            probe.warn("Invalid threshold value: " + parts[1]);
            return;
        }
        
        // 检查条件
        boolean triggered = false;
        double value = aggregation.getValue();
        
        switch (operator) {
            case ">":
                triggered = value > threshold;
                break;
            case ">=":
                triggered = value >= threshold;
                break;
            case "<":
                triggered = value < threshold;
                break;
            case "<=":
                triggered = value <= threshold;
                break;
            case "==":
                triggered = Math.abs(value - threshold) < 0.0001;
                break;
            case "!=":
                triggered = Math.abs(value - threshold) >= 0.0001;
                break;
            default:
                probe.warn("Unknown operator: " + operator);
                return;
        }
        
        // 如果触发，生成告警
        if (triggered) {
            generateAlert(rule, aggregation, 
                    "Value " + value + " " + operator + " " + threshold);
        } else {
            // 检查是否需要解决告警
            String alertKey = rule.getId() + ":" + aggregation.getPointId();
            if (activeAlerts.containsKey(alertKey)) {
                resolveAlertByKey(alertKey);
            }
        }
    }

    /**
     * 检查变化率条件
     */
    private void checkChangeRateCondition(TrackingAlertRule rule, TrackingAggregation aggregation) {
        // TODO: 实现变化率检查逻辑
        probe.info("Change rate condition check not implemented yet");
    }

    /**
     * 检查异常条件
     */
    private void checkAnomalyCondition(TrackingAlertRule rule, TrackingAggregation aggregation) {
        // TODO: 实现异常检测逻辑
        probe.info("Anomaly condition check not implemented yet");
    }

    /**
     * 生成告警
     */
    private void generateAlert(TrackingAlertRule rule, TrackingAggregation aggregation, String message) {
        // 生成告警键
        String alertKey = rule.getId() + ":" + aggregation.getPointId();
        
        // 检查是否已存在活跃告警
        if (activeAlerts.containsKey(alertKey)) {
            // 已存在告警，不重复生成
            return;
        }
        
        // 创建告警对象
        TrackingAlert alert = new TrackingAlert()
                .setRuleId(rule.getId())
                .setPointId(aggregation.getPointId())
                .setStrategyId(aggregation.getStrategyId())
                .setTimestamp(LocalDateTime.now())
                .setSeverity(rule.getSeverity())
                .setMessage(rule.getName() + ": " + message)
                .setStatus(TrackingAlert.AlertStatus.ACTIVE.name())
                .setCreatedAt(LocalDateTime.now());
        
        // 添加到活跃告警缓存
        activeAlerts.put(alertKey, alert);
        
        // 更新计数器和仪表盘
        probe.incrementCounterAmount("AlertGeneratedCounter", 1);
        probe.updateGauge("ActiveAlerts", activeAlerts.size());
        
        // 记录日志
        probe.enqueueWindowLogItem(ALERT_LOG, new ActorLogItem(ActorLogLevel.WARN,
                "Alert generated: " + alert.getMessage()));
        
        // TODO: 持久化告警
        
        // TODO: 发送通知
    }

    /**
     * 确认告警
     */
    private void acknowledgeAlert(Long alertId) {
        // 查找告警
        for (Map.Entry<String, TrackingAlert> entry : activeAlerts.entrySet()) {
            TrackingAlert alert = entry.getValue();
            if (alert.getId() != null && alert.getId().equals(alertId)) {
                // 更新状态
                alert.setStatus(TrackingAlert.AlertStatus.ACKNOWLEDGED.name());
                
                // 记录日志
                probe.info("Alert acknowledged: " + alert.getMessage());
                
                // TODO: 持久化更新
                
                break;
            }
        }
    }

    /**
     * 解决告警
     */
    private void resolveAlert(Long alertId) {
        // 查找告警
        String keyToRemove = null;
        for (Map.Entry<String, TrackingAlert> entry : activeAlerts.entrySet()) {
            TrackingAlert alert = entry.getValue();
            if (alert.getId() != null && alert.getId().equals(alertId)) {
                keyToRemove = entry.getKey();
                break;
            }
        }
        
        // 移除告警
        if (keyToRemove != null) {
            resolveAlertByKey(keyToRemove);
        }
    }

    /**
     * 根据键解决告警
     */
    private void resolveAlertByKey(String alertKey) {
        TrackingAlert alert = activeAlerts.remove(alertKey);
        if (alert != null) {
            // 更新状态
            alert.setStatus(TrackingAlert.AlertStatus.RESOLVED.name());
            alert.setResolvedAt(LocalDateTime.now());
            
            // 更新计数器和仪表盘
            probe.incrementCounterAmount("AlertResolvedCounter", 1);
            probe.updateGauge("ActiveAlerts", activeAlerts.size());
            
            // 记录日志
            probe.info("Alert resolved: " + alert.getMessage());
            
            // TODO: 持久化更新
        }
    }

    /**
     * 告警命令
     */
    private static class AlertCommand {
        private final CommandType type;
        private final Long alertId;
        
        public AlertCommand(CommandType type, Long alertId) {
            this.type = type;
            this.alertId = alertId;
        }
        
        public CommandType getType() {
            return type;
        }
        
        public Long getAlertId() {
            return alertId;
        }
    }

    /**
     * 命令类型枚举
     */
    private enum CommandType {
        ACKNOWLEDGE,
        RESOLVE
    }

    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
}
