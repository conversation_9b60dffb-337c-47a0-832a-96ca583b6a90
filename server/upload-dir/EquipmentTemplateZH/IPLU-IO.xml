<?xml version="1.0" encoding="UTF-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="755000014" ParentTemplateId="0" EquipmentTemplateName="IPLU-IO设备" ProtocolCode="IPLU-IO6-00" EquipmentCategory="51" EquipmentType="1" Memo="tieta" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1004" StationCategory="0">
    <Signals Name="模板信号">
      <Signal SignalId="510001251" SignalName="DI1状态" SignalCategory="2" SignalType="1" ChannelNo="16" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:0;1:1" />
      <Signal SignalId="510001252" SignalName="DI2状态" SignalCategory="2" SignalType="1" ChannelNo="17" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="15" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:0;1:1" />
      <Signal SignalId="510001255" SignalName="DI5状态" SignalCategory="2" SignalType="1" ChannelNo="20" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="18" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:0;1:1" />
      <Signal SignalId="510001030" SignalName="DI6状态" SignalCategory="2" SignalType="1" ChannelNo="21" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="19" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:0;1:1" />
      <Signal SignalId="510001141" SignalName="I2C湿度" SignalCategory="1" SignalType="1" ChannelNo="7" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="%RH" StoreInterval="28800" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004003001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="24" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510001131" SignalName="I2C温度" SignalCategory="1" SignalType="1" ChannelNo="6" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="℃" StoreInterval="28800" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004001001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="23" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510001111" SignalName="电池总电压" SignalCategory="1" SignalType="1" ChannelNo="5" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101170001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="25" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000000" SignalName="继电器2状态" SignalCategory="2" SignalType="1" ChannelNo="25" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="27" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:失电;1:得电" />
      <Signal SignalId="510001253" SignalName="门碰-DI3" SignalCategory="2" SignalType="1" ChannelNo="18" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="16" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:0;1:1" />
      <Signal SignalId="510001254" SignalName="门锁-DI4" SignalCategory="2" SignalType="1" ChannelNo="19" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="17" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:0;1:1" />
      <Signal SignalId="510001250" SignalName="门锁-DO1" SignalCategory="2" SignalType="1" ChannelNo="24" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="26" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:失电;1:得电" />
      <Signal SignalId="510000011" SignalName="模拟输入01值" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="/" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000021" SignalName="模拟输入02值" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="/" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000031" SignalName="模拟输入03值" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="/" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000041" SignalName="模拟输入04值" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="/" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000051" SignalName="模拟输入05值" SignalCategory="1" SignalType="1" ChannelNo="4" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="/" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="-3" SignalName="设备通讯状态" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:通讯异常;1:通讯正常" />
      <Signal SignalId="510001000" SignalName="水浸通道" SignalCategory="2" SignalType="1" ChannelNo="22" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="20" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:水浸告警" />
    </Signals>
    <Events Name="模板事件">
      <Event EventId="-3" EventName="设备通讯状态" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="通讯异常" EquipmentState="" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510001000" EventName="水浸通道" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510001000]" SuppressExpression="" SignalId="510001000" Enable="True" Visible="True" Description="" DisplayIndex="8" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="水浸告警" EquipmentState="2" BaseTypeId="1004005001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510001131" EventName="I2C温度" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510001131]" SuppressExpression="" SignalId="510001131" Enable="True" Visible="True" Description="" DisplayIndex="11" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="&gt;" StartCompareValue="45" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="温度超高" EquipmentState="2" BaseTypeId="1004307001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&gt;" StartCompareValue="40" StartDelay="120" EndOperation="&gt;" EndCompareValue="45" EndDelay="20" Frequency="" FrequencyThreshold="" Meanings="温度过高" EquipmentState="" BaseTypeId="1004001001" StandardName="" />
          <EventCondition EventConditionId="2" EventSeverity="0" StartOperation="&lt;" StartCompareValue="-10" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="温度过低" EquipmentState="" BaseTypeId="1004002001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510001141" EventName="I2C湿度" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510001141]" SuppressExpression="" SignalId="510001141" Enable="True" Visible="True" Description="" DisplayIndex="12" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="95" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="湿度过高" EquipmentState="2" BaseTypeId="1004003001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="10" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="湿度过低" EquipmentState="" BaseTypeId="1004004001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510001251" EventName="电池总电压事件" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510001111]" SuppressExpression="" SignalId="510001111" Enable="True" Visible="True" Description="" DisplayIndex="15" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="60" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="电压过高" EquipmentState="" BaseTypeId="1101370001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="46" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="电压过低" EquipmentState="" BaseTypeId="1101170001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510001252" EventName="门碰-DI3" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510001253]" SuppressExpression="" SignalId="510001253" Enable="True" Visible="True" Description="" DisplayIndex="16" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="" BaseTypeId="1004007001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510001253" EventName="门锁-DI4" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510001254]" SuppressExpression="" SignalId="510001254" Enable="True" Visible="True" Description="" DisplayIndex="17" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="" BaseTypeId="1004308001" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="模板控制">
      <Control ControlId="510000000" ControlName="继电器2状态" ControlCategory="1" CmdToken="11,0" BaseTypeId="" ControlSeverity="1" SignalId="510000000" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="2" CommandType="2" ControlType="1" DataType="0" MaxValue="1" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:失电;1:得电" />
      <Control ControlId="510001250" ControlName="远程开门-DO1" ControlCategory="1" CmdToken="10,250" BaseTypeId="1004010001" ControlSeverity="1" SignalId="510001250" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="1" CommandType="2" ControlType="1" DataType="0" MaxValue="1" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:失电;1:得电" />
    </Controls>
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="755000016" SamplerName="IPLU-IO设备" SamplerType="18" ProtocolCode="IPLU-IO6-00" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="IDUIO.so" Setting="9600,n,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>