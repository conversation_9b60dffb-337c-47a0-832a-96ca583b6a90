package com.siteweb.tcs.middleware.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity;
import com.siteweb.tcs.middleware.mapper.ResourceConfigurationMapper;
import com.siteweb.tcs.middleware.service.ResourceConfigurationService;
import com.siteweb.tcs.middleware.util.EncryptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 资源配置服务实现�?
 */
@Service
public class ResourceConfigurationServiceImpl extends ServiceImpl<ResourceConfigurationMapper, ResourceConfigurationEntity> implements ResourceConfigurationService {

    @Autowired
    private EncryptionUtil encryptionUtil;

    @Override
    public IPage<ResourceConfigurationEntity> pageResourceConfigurations(
            Page<ResourceConfigurationEntity> page,
            String resourceId,
            String name,
            String status) {
        return baseMapper.pageResourceConfigurations(page, resourceId, name, status);
    }

    @Override
    public ResourceConfigurationEntity getResourceConfigurationById(String id) {
        ResourceConfigurationEntity entity = baseMapper.getResourceConfigurationById(id);
        if (entity != null && entity.getConfig() != null) {
            // 解密敏感字段
            encryptionUtil.decryptConfigFields(entity.getConfig());
        }
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResourceConfigurationEntity createResourceConfiguration(ResourceConfigurationEntity resourceConfiguration) {
        // ID由前端输入，不需要后端
        // 如果ID为空，才生成一个新的ID
        if (resourceConfiguration.getId() == null || resourceConfiguration.getId().isEmpty()) {
            resourceConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        }

        // 处理配置中需要加密的字段
        if (resourceConfiguration.getConfig() != null) {
            encryptionUtil.encryptConfigFields(resourceConfiguration.getConfig());
        }

        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        resourceConfiguration.setCreateTime(now);
        resourceConfiguration.setUpdateTime(now);

        // 保存实体
        save(resourceConfiguration);

        return getResourceConfigurationById(resourceConfiguration.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResourceConfigurationEntity updateResourceConfiguration(String id, ResourceConfigurationEntity resourceConfiguration) {
        ResourceConfigurationEntity entity = getById(id);
        if (entity == null) {
            return null;
        }

        // 处理配置中需要加密的字段
        if (resourceConfiguration.getConfig() != null) {
            encryptionUtil.encryptConfigFields(resourceConfiguration.getConfig());
        }

        // 更新属�?
        resourceConfiguration.setId(id); // 确保ID不变
        resourceConfiguration.setUpdateTime(LocalDateTime.now());

        // 更新实体
        updateById(resourceConfiguration);

        return getResourceConfigurationById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteResourceConfiguration(String id) {
        return removeById(id);
    }

    @Override
    public List<ResourceConfigurationEntity> listResourceConfigurationsByResourceId(String resourceId) {
        LambdaQueryWrapper<ResourceConfigurationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceConfigurationEntity::getResourceId, resourceId);
        queryWrapper.orderByDesc(ResourceConfigurationEntity::getCreateTime);

        return list(queryWrapper);
    }

}

