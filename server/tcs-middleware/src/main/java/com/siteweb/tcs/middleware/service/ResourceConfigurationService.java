package com.siteweb.tcs.middleware.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity;

import java.util.List;

/**
 * 资源配置服务接口
 */
public interface ResourceConfigurationService extends IService<ResourceConfigurationEntity> {
    
    /**
     * 分页查询资源配置
     *
     * @param page 分页参数
     * @param resourceId 资源类型ID，可为null
     * @param name 资源配置名称，可为null
     * @param status 资源配置状态，可为null
     * @return 分页结果
     */
    IPage<ResourceConfigurationEntity> pageResourceConfigurations(
            Page<ResourceConfigurationEntity> page,
            String resourceId,
            String name,
            String status);
    
    /**
     * 根据ID查询资源配置
     *
     * @param id 资源配置ID
     * @return 资源配置实体
     */
    ResourceConfigurationEntity getResourceConfigurationById(String id);
    
    /**
     * 创建资源配置
     *
     * @param resourceConfiguration 资源配置实体
     * @return 创建后的资源配置实体
     */
    ResourceConfigurationEntity createResourceConfiguration(ResourceConfigurationEntity resourceConfiguration);
    
    /**
     * 更新资源配置
     *
     * @param id 资源配置ID
     * @param resourceConfiguration 资源配置实体
     * @return 更新后的资源配置实体
     */
    ResourceConfigurationEntity updateResourceConfiguration(String id, ResourceConfigurationEntity resourceConfiguration);
    
    /**
     * 删除资源配置
     *
     * @param id 资源配置ID
     * @return 是否删除成功
     */
    boolean deleteResourceConfiguration(String id);
    
    /**
     * 根据资源类型ID查询资源配置列表
     *
     * @param resourceId 资源类型ID
     * @return 资源配置列表
     */
    List<ResourceConfigurationEntity> listResourceConfigurationsByResourceId(String resourceId);
}
