package com.siteweb.tcs.middleware.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.middleware.entity.ServiceConfigurationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 服务配置Mapper接口
 */
@Mapper
public interface ServiceConfigurationMapper extends BaseMapper<ServiceConfigurationEntity> {

    /**
     * 分页查询服务配置，包含服务类型名称和资源配置名称
     *
     * @param page 分页参数
     * @param serviceId 服务类型ID，可为null
     * @param name 服务配置名称，可为null
     * @param status 服务配置状态，可为null
     * @return 分页结果
     */
    IPage<ServiceConfigurationEntity> pageServiceConfigurations(
            Page<ServiceConfigurationEntity> page,
            @Param("serviceId") String serviceId,
            @Param("name") String name,
            @Param("status") String status);

    /**
     * 根据ID查询服务配置，包含服务类型名称和资源配置名称
     *
     * @param id 服务配置ID
     * @return 服务配置实体
     */
    ServiceConfigurationEntity getServiceConfigurationById(@Param("id") String id);
}
