package com.siteweb.tcs.middleware.factory;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceProvider;
import com.siteweb.tcs.middleware.common.resource.ResourceProviderFactory;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import com.siteweb.tcs.middleware.common.resource.provider.H2ResourceProvider;
import com.siteweb.tcs.middleware.common.resource.provider.HttpServerResourceProvider;
import com.siteweb.tcs.middleware.common.resource.provider.KafkaResourceProvider;
import com.siteweb.tcs.middleware.common.resource.provider.MosMQTTResourceProvider;
import com.siteweb.tcs.middleware.common.resource.provider.MySQLResourceProvider;
import com.siteweb.tcs.middleware.common.resource.provider.PostgreSQLResourceProvider;
import com.siteweb.tcs.middleware.common.resource.provider.RedisResourceProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 资源提供者工厂实现类
 * 使用Spring容器管理资源提供者实例，确保依赖注入正常工作
 */
@Component
public class ResourceProviderFactoryImpl implements ResourceProviderFactory {

    private static final Logger logger = LoggerFactory.getLogger(ResourceProviderFactoryImpl.class);

    @Autowired
    private ApplicationContext applicationContext;

    // 使用实例Map存储资源提供者实例，避免重复创建
    private final Map<String, ResourceProvider<? extends Resource>> providerMap = new ConcurrentHashMap<>();

    /**
     * 获取资源提供者
     *
     * @param type 资源类型
     * @return 资源提供者
     */
    private ResourceProvider<? extends Resource> getProviderByType(String type) {
        // 使用computeIfAbsent方法，如果不存在则创建新的提供者
        return providerMap.computeIfAbsent(type, key -> {
            // 根据类型创建新的提供者
            ResourceProvider<? extends Resource> provider = createProvider(key);

            // 如果创建成功，则记录日志
            if (provider != null) {
                logger.info("创建资源提供者: {}", key);
            }

            return provider;
        });
    }

    /**
     * 根据类型创建资源提供者
     *
     * @param type 资源类型
     * @return 资源提供者
     */
    private ResourceProvider<? extends Resource> createProvider(String type) {
        // 使用ResourceType枚举的fromCodeIgnoreCase方法，忽略大小写
        ResourceType resourceType = ResourceType.fromCodeIgnoreCase(type);

        if (resourceType == null) {
            return null;
        }

        try {
            // 使用Spring容器获取Provider实例，确保依赖注入正常工作
            switch (resourceType) {
                case H2:
                    return applicationContext.getBean(H2ResourceProvider.class);
                case MYSQL:
                    return applicationContext.getBean(MySQLResourceProvider.class);
                case POSTGRESQL:
                    return applicationContext.getBean(PostgreSQLResourceProvider.class);
                case REDIS:
                    return applicationContext.getBean(RedisResourceProvider.class);
                case HTTP_SERVER:
                    return applicationContext.getBean(HttpServerResourceProvider.class);
                case KAFKA:
                    return applicationContext.getBean(KafkaResourceProvider.class);
                case MQTT:
                    return applicationContext.getBean(MosMQTTResourceProvider.class);
                // 可以在这里添加更多资源类型的case
                default:
                    return null;
            }
        } catch (Exception e) {
            logger.error("Failed to create resource provider for type: {}", type, e);
            return null;
        }
    }

    @Override
    public ResourceProvider<? extends Resource> getProvider(String type) throws MiddlewareBusinessException {
        ResourceProvider<? extends Resource> provider = getProviderByType(type);
        if (provider == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "资源类型不存在: " + type
            );
        }
        return provider;
    }

    @Override
    public Map<String, ResourceProvider<? extends Resource>> getAllProviders() {
        // 这里我们需要确保所有已知的资源提供者类型都被加载
        // 这样getAllProviders才能返回所有可能的提供者
        getProviderByType(ResourceType.H2.getCode());
        getProviderByType(ResourceType.MYSQL.getCode());
        getProviderByType(ResourceType.POSTGRESQL.getCode());
        getProviderByType(ResourceType.REDIS.getCode());
        getProviderByType(ResourceType.HTTP_SERVER.getCode());
        getProviderByType(ResourceType.KAFKA.getCode());
        getProviderByType(ResourceType.MQTT.getCode());
        // 可以在这里添加更多资源类型

        return new HashMap<>(providerMap);
    }
}
