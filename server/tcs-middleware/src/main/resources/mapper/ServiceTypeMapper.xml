<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.middleware.mapper.ServiceTypeMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.middleware.entity.ServiceTypeEntity">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="default_config" property="defaultConfig" typeHandler="com.siteweb.tcs.middleware.util.MapStringObjectTypeHandler" />
        <result column="ui_component" property="uiComponent" />
        <result column="supported_resource_category" property="supportedResourceCategory" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, default_config, ui_component, supported_resource_category, create_time, update_time
    </sql>
    
</mapper>
