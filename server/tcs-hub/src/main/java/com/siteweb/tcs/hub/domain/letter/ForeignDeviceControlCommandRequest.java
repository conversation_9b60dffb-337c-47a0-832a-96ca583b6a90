package com.siteweb.tcs.hub.domain.letter;


import com.siteweb.tcs.hub.domain.letter.enums.CmdDeviceCategoryEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class ForeignDeviceControlCommandRequest {
    private String foreignGatewayId;
    private String foreignDeviceId;
    private CmdDeviceCategoryEnum category;
    private List<ForeignControlCommandRequest> commandRequestList;
}
