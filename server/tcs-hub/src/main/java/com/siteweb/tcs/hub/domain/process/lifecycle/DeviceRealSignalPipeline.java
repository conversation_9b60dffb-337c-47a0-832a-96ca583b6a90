package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.process.DeviceRealSignalAdapter;
import com.siteweb.tcs.hub.domain.process.EquipmentRealSignalSpout;
import com.siteweb.tcs.hub.domain.process.EquipmentSignalStateStore;
import com.siteweb.tcs.hub.domain.process.EquipmentStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * 设备实时信号管道
 * 负责处理设备实时信号数据的数据流
 */
public class DeviceRealSignalPipeline extends DataPipeline {
    private final ForeignDevice device;
    
    /**
     * 构造函数
     * @param context Actor上下文
     * @param device 外部设备实体
     */
    public DeviceRealSignalPipeline(ActorContext context, ForeignDevice device, ActorRef pipelinePublisher) {
        super(context, pipelinePublisher);
        this.device = device;
    }
    
    @Override
    public void create() {
        // 创建信号发送Actor
        ActorRef spoutActor = getContext().actorOf(
                Props.create(EquipmentRealSignalSpout.class, device, getPipelinePublisher()),
                "EquipmentRealSignalSpout"
        );
        setSpoutActor(spoutActor);
        
        // 创建信号存储Actor
        ActorRef storeActor = getContext().actorOf(
                EquipmentSignalStateStore.props(spoutActor, device),
                "EquipmentSignalStateStore"
        );
        setStoreActor(storeActor);
        
        // 创建信号适配器Actor
        ActorRef adapterActor = getContext().actorOf(
                DeviceRealSignalAdapter.props(device, storeActor),
                "DeviceRealSignalAdapter"
        );
        setAdapterActor(adapterActor);
    }
}

