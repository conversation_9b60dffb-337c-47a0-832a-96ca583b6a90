package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.dto.MenuTreeFullPathDTO;
import com.siteweb.tcs.hub.dal.dto.RegionDTO;
import com.siteweb.tcs.hub.dal.entity.MenuItem;
import com.siteweb.tcs.hub.dal.entity.Permission;
import com.siteweb.tcs.hub.dal.entity.Region;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    @Select("SELECT * FROM tcs_menu_item ")
    List<MenuItem> getAllMenuItem();

    @Select("select * from tcs_menu_item where menuItemId in(select distinct(permissionId) from tcs_role_permission_map a " +
            "inner join tcs_account_role_map b on b.RoleId = a.RoleId and b.UserId = #{userId} " +
            "where PermissionType = 1)")
    List<MenuItem> getUserPermMenu(Integer userId);

    @Insert({
            "<script>",
            "INSERT INTO tcs_role_permission_map (PluginId, RoleId, PermissionId, permissionType) VALUES ",
            "<foreach collection='bindIdList' item='bindId' separator=','>",
            "(#{pluginId}, #{roleId}, #{bindId}, #{permissionType})",
            "</foreach>",
            "</script>"
    })
    int createMenuRoleMap(@Param("roleId") Integer roleId,
                          @Param("pluginId") String pluginId,
                          @Param("bindIdList") List<String> bindIdList,
                          @Param("permissionType") Integer permissionType);

    @Delete("delete from tcs_role_permission_map where roleId = #{roleId} and pluginId = #{pluginId} and permissionType = #{permissionType} ")
    void deleteMenuRoleMap(Integer roleId, String pluginId, Integer permissionType);

    @Select("select * from tcs_menu_item where menuItemId in(select distinct(permissionId) from tcs_role_permission_map " +
            "where roleId = #{roleId} and permissionType = 1)")
    List<MenuItem> getUserPermMenuByRoleId(Integer roleId);


    @Select("select * from tcs_regions where regionId in " +
            "(select PermissionId from tcs_role_permission_map where roleId = #{roleId} and permissionType = 2)")
    List<Region> getRegionListByRoleId(Integer roleId);


    @Select("""
                WITH RECURSIVE MenuPath AS (
                    SELECT 
                        MenuItemId,
                        PluginId,
                        MenuItemName,
                        ParentMenuItemId,
                        Path,
                        LayoutPosition,
                        CASE 
                            WHEN Path = '#' THEN ''
                            ELSE CAST(Path AS CHAR(1000))
                        END AS FullPath,
                        CAST(MenuItemId AS CHAR(100)) AS IdPath,
                        1 AS Level
                    FROM tcs_menu_item
                    WHERE ParentMenuItemId = 0
                    UNION ALL
                    SELECT 
                        m.MenuItemId,
                        m.PluginId,
                        m.MenuItemName,
                        m.ParentMenuItemId,
                        m.Path,
                        m.LayoutPosition,
                        CASE 
                            WHEN m.Path = '#' THEN mp.FullPath
                            ELSE CONCAT(mp.FullPath, '/', m.Path)
                        END AS FullPath,
                        CONCAT(mp.IdPath, ',', m.MenuItemId) AS IdPath,
                        mp.Level + 1 AS Level
                    FROM tcs_menu_item m
                    JOIN MenuPath mp ON m.ParentMenuItemId = mp.MenuItemId
                )
                SELECT 
                    MenuItemId,
                    PluginId,
                    MenuItemName,
                    ParentMenuItemId,
                    Path,
                    FullPath,
                    IdPath,
                    Level,
                    LayoutPosition
                FROM MenuPath
                ORDER BY IdPath
            """)
    List<MenuTreeFullPathDTO> getMenuTreeWithPaths();

    @Select("""
             WITH RECURSIVE menu_path AS (
                             SELECT
                             MenuItemId,
                             MenuItemName,
                             ParentMenuItemId
                             FROM tcs_menu_item
                             WHERE MenuItemId IN (select distinct(permissionId) from tcs_role_permission_map a
                                               inner join tcs_account_role_map b on b.RoleId = a.RoleId and b.UserId = #{userId}
                                               where PermissionType = 1)
                             UNION ALL
                             -- 递归查询父级菜单
                             SELECT
                             p.MenuItemId,
                             p.MenuItemName,
                             p.ParentMenuItemId
                             FROM tcs_menu_item p
                             JOIN menu_path mp ON p.MenuItemId = mp.ParentMenuItemId
                         )
                         SELECT distinct * FROM menu_path
            """)
    List<MenuItem> getUserPermMenuTree(Integer userId);

    @Select("""
            WITH RECURSIVE region_info AS (
             SELECT
             regionId,
             regionName,
             ParentId
             FROM tcs_regions
             WHERE regionId IN (select distinct(permissionId) from tcs_role_permission_map a
            				   inner join tcs_account_role_map b on b.RoleId = a.RoleId and b.UserId = #{userId}
            				   where PermissionType = 2)
             UNION ALL
             -- 递归查询父级菜单
             SELECT
             p.regionId,
             p.regionName,
             p.ParentId
             FROM tcs_regions p
             JOIN region_info mp ON p.regionId = mp.ParentId
            )
            SELECT distinct * FROM region_info
                        
            """)
    List<RegionDTO> getRegionListByUserId(Integer userId);

    @Delete({"SET SQL_SAFE_UPDATES = 0; " ,
            "DELETE FROM tcs_role_permission_map WHERE roleId = #{roleId}; " ,
            "SET SQL_SAFE_UPDATES = 1; "})
    void deletePermissionRoleMapByRoleId(Integer roleId);
}
