package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

@Slf4j
public class DeviceHistoryDataAdapter extends ProbeActor {


    private final ActorRef deviceSignalStateStore;
    private final ForeignDevice foreignDevice;
    private final HashMap<String, Integer> signalMap = new HashMap<>();

    public DeviceHistoryDataAdapter(ForeignDevice foreignDevice, ActorRef equipmentHistoryDataStore) {
        this.deviceSignalStateStore = equipmentHistoryDataStore;
        this.foreignDevice = foreignDevice;
        this.foreignDevice.getForeignSignalList().forEach(signal -> {
            signalMap.put(signal.getForeignSignalID(), signal.getSignalId());
        });
        // Optionally, add probe metrics if needed
        // this.probe.addRateCalculator("historyDataRateIn", 60);
        getContext().watchWith(equipmentHistoryDataStore,new StoreDeadLetter());
    }

    public static Props props(ForeignDevice foreignDevice, ActorRef equipmentHistoryDataStore) {
        return Props.create(DeviceHistoryDataAdapter.class, foreignDevice, equipmentHistoryDataStore);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .match(ForeignDeviceHisData.class, this::handleForeignDeviceHisData)
                .match(ForeignDeviceHistoryData.class, this::handleForeignDeviceHistoryData)
                .match(ForeignDeviceHisBatCurveData.class, this::handleForeignDeviceHisBatCurveData)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            ForeignDevice foreignDevice = (ForeignDevice) needUpdateAction.getConfig();
            this.signalMap.clear();
            foreignDevice.getForeignSignalList().forEach(signal -> {
                this.signalMap.put(signal.getForeignSignalID(), signal.getSignalId());
            });
        }
    }

    /**
     * 处理周期存储的历史数据
     */
    private void handleForeignDeviceHisData(ForeignDeviceHisData foreignDeviceHisData) {
        // 验证设备ID是否匹配
        if (!Objects.equals(this.foreignDevice.getForeignDeviceID(), foreignDeviceHisData.getForeignDeviceId())) {
            return;
        }
        // 转换并发送数据
        EquipmentHisData equipmentHisData = convertToEquipmentHistoryData(foreignDeviceHisData);
        deviceSignalStateStore.tell(equipmentHisData, getSelf());
    }

    /**
     * 处理周期内统计的最大、最小值历史数据
     */
    private void handleForeignDeviceHistoryData(ForeignDeviceHistoryData foreignDeviceHistoryData) {
        // 验证设备ID是否匹配
        if (!Objects.equals(this.foreignDevice.getForeignDeviceID(), foreignDeviceHistoryData.getForeignDeviceId())) {
            return;
        }
        // 转换并发送数据
        EquipmentHistoryData equipmentHistoryData = convertToEquipmentHistoryData(foreignDeviceHistoryData);
        deviceSignalStateStore.tell(equipmentHistoryData, getSelf());

    }

    /**
     * 处理电池放电曲线历史数据
     */
    private void handleForeignDeviceHisBatCurveData(ForeignDeviceHisBatCurveData foreignDeviceHisBatCurveData) {
        // 验证设备ID是否匹配
        if (!Objects.equals(this.foreignDevice.getForeignDeviceID(), foreignDeviceHisBatCurveData.getForeignDeviceId())) {
            return;
        }
        // 转换并发送数据
        EquipmentHisBatCurveData equipmentHistoryData = convertToEquipmentHistoryData(foreignDeviceHisBatCurveData);
        deviceSignalStateStore.tell(equipmentHistoryData, getSelf());
    }

    private EquipmentHisData convertToEquipmentHistoryData(ForeignDeviceHisData foreignDeviceHisData) {
        EquipmentHisData equipmentHisData = new EquipmentHisData();
        equipmentHisData.setEquipmentId(this.foreignDevice.getEquipmentId());
        equipmentHisData.setEquipmentTemplateId(this.foreignDevice.getEquipmentTemplateId());
        equipmentHisData.setStationId(this.foreignDevice.getStationId());
        equipmentHisData.setSignalLists(new ArrayList<>());
        foreignDeviceHisData.getSignalLists().forEach(hisData -> {
            if (signalMap.containsKey(hisData.getForeignSignalId())) {
                EquipmentSignalHisData equipmentSignalHisData = new EquipmentSignalHisData();
                equipmentSignalHisData.setEquipmentId(this.foreignDevice.getEquipmentId());
                equipmentSignalHisData.setSignalId(signalMap.get(hisData.getForeignSignalId()));
                equipmentSignalHisData.setTriggerVal(hisData.getTriggerVal());
                equipmentSignalHisData.setRecordTime(hisData.getRecordTime());
                equipmentHisData.getSignalLists().add(equipmentSignalHisData);
            }
        });
        return equipmentHisData;
    }

    private EquipmentHistoryData convertToEquipmentHistoryData(ForeignDeviceHistoryData foreignDeviceHistoryData) {
        EquipmentHistoryData equipmentHistoryData = new EquipmentHistoryData();
        equipmentHistoryData.setEquipmentId(this.foreignDevice.getEquipmentId());
        equipmentHistoryData.setStationId(this.foreignDevice.getStationId());
        equipmentHistoryData.setSignalLists(new ArrayList<>());
        foreignDeviceHistoryData.getSignalLists().forEach(historyDataItem -> {
            if (signalMap.containsKey(historyDataItem.getForeignSignalId())) {
                EquipmentSignalHistoryData historyData = new EquipmentSignalHistoryData();
                historyData.setEquipmentId(String.valueOf(this.foreignDevice.getEquipmentId()));
                historyData.setSignalId(String.valueOf(signalMap.get(historyDataItem.getForeignSignalId())));
                historyData.setMinVal(historyDataItem.getMinVal());
                historyData.setMinValTime(historyDataItem.getMinValTime());
                historyData.setMaxVal(historyDataItem.getMaxVal());
                historyData.setMaxValTime(historyDataItem.getMaxValTime());
                historyData.setPeriodStart(historyDataItem.getPeriodStart());
                historyData.setPeriodEnd(historyDataItem.getPeriodEnd());
                equipmentHistoryData.getSignalLists().add(historyData);
            }
        });
        return equipmentHistoryData;
    }

    private EquipmentHisBatCurveData convertToEquipmentHistoryData(ForeignDeviceHisBatCurveData foreignDeviceHisBatCurveData) {
        EquipmentHisBatCurveData equipmentHistoryData = new EquipmentHisBatCurveData();
        equipmentHistoryData.setEquipmentId(String.valueOf(this.foreignDevice.getEquipmentId()));
        equipmentHistoryData.setSignalIds(foreignDeviceHisBatCurveData.getForeignSignalIds());
        equipmentHistoryData.setStationId(this.foreignDevice.getStationId());
        equipmentHistoryData.setSignalLists(new ArrayList<>());
        equipmentHistoryData.setStartTime(foreignDeviceHisBatCurveData.getStartTime());
        foreignDeviceHisBatCurveData.getRecordLists().forEach(batCurveData -> {
            EquipmentHisBatRecord historyData = new EquipmentHisBatRecord();
            historyData.setEquipmentId(String.valueOf(this.foreignDevice.getEquipmentId()));
            historyData.setCurrentVal(batCurveData.getCurrentVal());
            historyData.setVoltageVal(batCurveData.getVoltageVal());
            historyData.setOffsetSeconds(batCurveData.getOffsetSeconds());
            historyData.setVoltageType(batCurveData.getVoltageType());
            historyData.setStartTime(batCurveData.getStartTime());
            equipmentHistoryData.getSignalLists().add(historyData);
        });
        return equipmentHistoryData;
    }

}

