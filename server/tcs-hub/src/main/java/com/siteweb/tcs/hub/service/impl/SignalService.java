package com.siteweb.tcs.hub.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.provider.SignalProvider;
import com.siteweb.tcs.hub.dal.dto.EquipmentDTO;
import com.siteweb.tcs.hub.dal.dto.SignalDTO;
import com.siteweb.tcs.hub.dal.entity.door.DoorTemplateSignal;
import com.siteweb.tcs.hub.dal.provider.DoorTemplateProvider;
import com.siteweb.tcs.hub.domain.letter.ForeignDoorDeviceConfigChange;
import com.siteweb.tcs.hub.domain.letter.ForeignSignalConfigChange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SignalService {

    @Autowired
    private SignalProvider signalProvider;

    @Autowired
    private DoorTemplateProvider doorTemplateProvider;

    private static final Logger log = LoggerFactory.getLogger(SignalService.class);

    public SignalDTO createSignal(Integer equipmentTemplateId, ForeignSignalConfigChange signalConfigChange){
        SignalConfigItem signalConfigItem = null;
        if(doorTemplateProvider.existSignal(signalConfigChange.getForeignSignalId())){
            DoorTemplateSignal doorTemplateSignal = doorTemplateProvider.getTemplateSignal(signalConfigChange.getForeignSignalId());
            signalConfigItem = signalConfigChange.toSignalConfigItem(equipmentTemplateId,doorTemplateSignal);
        }else {
            signalConfigItem = signalConfigChange.toSignalConfigItem(equipmentTemplateId);
        }
        SignalConfigItem result;
        try {
            result = signalProvider.createSignal(signalConfigItem);
        }catch (Exception ex){
            log.error("[DEVICE LIFE CYCLE MANAGER] CREATE SIGNAL: -> error occurs during call config server, the error reason is {}, the call params is {}",ex.getCause(),signalConfigChange);
            return null;
        }
        SignalDTO signalDTO = new SignalDTO();
        BeanUtils.copyProperties(result, signalDTO);
        return signalDTO;
    }

    /**
     * 批量删除信号
     * @param equipmentTemplateId
     * @param signalIdList
     * @return
     */
    public boolean batchDeleteSignal(Integer equipmentTemplateId, List<Integer> signalIdList) {
        if(ObjectUtil.isEmpty(equipmentTemplateId) || CollectionUtil.isEmpty(signalIdList)) return false;
        boolean delFlag;
        try {
            delFlag = signalProvider.batchDeleteSignal(equipmentTemplateId, signalIdList) > 0;
        }catch (Exception ex){
            delFlag = false;
            log.error("[DEVICE LIFE CYCLE MANAGER] BATCH DELETE SIGNAL: -> error occurs during call config server, the error reason is {}, the call equipment template id is {}, and the signal id list is {}",ex.getCause(),equipmentTemplateId,signalIdList);
        }
        return delFlag;
    }

    public Boolean updateSignal(Integer equipmentTemplateId,Integer signalId,ForeignSignalConfigChange foreignSignalConfigChange){
        SignalConfigItem signalConfigItem = foreignSignalConfigChange.toSignalConfigItem(equipmentTemplateId);
        signalConfigItem.setSignalId(signalId);
        try {
            return signalProvider.updateSignal(signalConfigItem) != null;
        }catch (Exception ex){
            log.error("[DEVICE LIFE CYCLE MANAGER] UPDATE SIGNAL: -> error occurs during call config server, the error reason is {}, the call equipment template id is {}, and the signal id list is {}",ex.getCause(),equipmentTemplateId,foreignSignalConfigChange);
            return false;
        }
    }

    public SignalDTO getSignalInfo(Integer equipmentTemplateId,Integer signalId){
        if(ObjectUtil.isEmpty(equipmentTemplateId) || ObjectUtil.isEmpty(signalId)) return null;
        SignalConfigItem result;
        try {
            result = signalProvider.getSignalDetail(equipmentTemplateId,signalId);
        }catch (Exception ex){
            log.error("[DEVICE LIFE CYCLE MANAGER] QUERY SIGNAL: -> error occurs during call config server, the error reason is {}, the call equipment template id is {}, and the signal id is {}",ex.getCause(),equipmentTemplateId,signalId);
            return null;
        }
        SignalDTO signalDTO = new SignalDTO();
        BeanUtils.copyProperties(result, signalDTO);
        return signalDTO;
    }

    private SignalDTO changeToSignalDTO(SignalConfigItem signalConfigItem){
        if(ObjectUtil.isEmpty(signalConfigItem)) return null;
        SignalDTO signalDTO = new SignalDTO();
        BeanUtils.copyProperties(signalConfigItem,signalDTO);
        return signalDTO;
    }

    /**
     * 创建门禁特殊通道
     * @param equipmentDTO
     * @param e
     * @return
     */
    public SignalDTO createDoorCategorySignal(EquipmentDTO equipmentDTO, ForeignDoorDeviceConfigChange e) {
        DoorTemplateSignal templateSignal = doorTemplateProvider.getTemplateSignal("tcs_signal_door_73");
        SignalConfigItem signalConfigItem = templateSignal.getSignalConfigItem();
        signalConfigItem.setEquipmentTemplateId(equipmentDTO.getEquipmentTemplateId());
        signalConfigItem.setExpression(String.valueOf(e.getDoorCategoryId()));
        SignalConfigItem result;
        try {
            result = signalProvider.createSignal(signalConfigItem);
        }catch (Exception ex){
            log.error("[DEVICE LIFE CYCLE MANAGER] CREATE DOOR CATEGORY SIGNAL: -> error occurs during call config server, the error reason is {}, the call params is {}",ex.getCause(),equipmentDTO);
            return null;
        }
        SignalDTO signalDTO = new SignalDTO();
        BeanUtils.copyProperties(result, signalDTO);
        return signalDTO;
    }
}
