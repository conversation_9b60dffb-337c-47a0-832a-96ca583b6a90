package com.siteweb.tcs.hub.domain.process;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.EquipmentLoggingEvent;
import com.siteweb.tcs.hub.domain.letter.enums.LoggingEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;

/**
 * @ClassName: EquipmentLoggingEventStore
 * @descriptions: 日志类型事件存储类
 * @author: xsx
 * @date: 2024/9/25 10:56
 **/
@Slf4j
public class EquipmentLoggingEventStore extends ProbeActor {
    private final ActorRef equipmentLoggingEventSpout;
    private final Map<LoggingEventEnum,LimitedQueue> eventEnumLimitedQueueMap = new HashMap<>();

    /**
     * 保留最新5个包
     * @param o
     * @return
     */
    private final Integer queueSize = 5;

    private EquipmentLoggingEventStore(ActorRef equipmentLoggingEventSpout){
        this.equipmentLoggingEventSpout = equipmentLoggingEventSpout;
    }

    public static Props props(ActorRef equipmentLoggingEventSpout) {
        return Props.create(EquipmentLoggingEventStore.class,()->new EquipmentLoggingEventStore(equipmentLoggingEventSpout));
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(EquipmentLoggingEvent.class,this::onEquipmentLoggingEvent)
                .build()
                .orElse(super.createReceive());
    }

    private void onEquipmentLoggingEvent(EquipmentLoggingEvent equipmentLoggingEvent) {
        if(ObjectUtil.isEmpty(equipmentLoggingEvent))return;
        if(eventEnumLimitedQueueMap.containsKey(equipmentLoggingEvent.getLoggingEventEnum())){
            eventEnumLimitedQueueMap.get(equipmentLoggingEvent.getLoggingEventEnum()).add(equipmentLoggingEvent);
        }else {
            LimitedQueue<EquipmentLoggingEvent> equipmentLoggingEventLimitedQueue = new LimitedQueue<>(queueSize);
            equipmentLoggingEventLimitedQueue.add(equipmentLoggingEvent);
            eventEnumLimitedQueueMap.put(equipmentLoggingEvent.getLoggingEventEnum(),equipmentLoggingEventLimitedQueue);
        }
        //往外送
        equipmentLoggingEventSpout.tell(equipmentLoggingEvent,getSelf());
    }

    private static class LimitedQueue<E> extends LinkedList<E> {
        private final int limit;

        public LimitedQueue(int limit) {
            this.limit = limit;
        }

        @Override
        public boolean add(E o) {
            super.add(o);
            while (size() > limit) { super.remove(); }
            return true;
        }
    }
}

