package com.siteweb.tcs.hub.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.hub.dal.entity.OperationLog;
import com.siteweb.tcs.hub.dal.mapper.OperationLogMapper;
import com.siteweb.tcs.hub.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void createLog(Integer userId, Integer objectId, String objectName, Integer objectType,
                          String operationType, String oldValue, String newValue) {
        try {
            OperationLog log = new OperationLog();
            log.setUserId(userId);
            log.setObjectId(objectId);
            log.setObjectName(objectName);
            log.setObjectType(objectType);
            log.setOperationType(operationType);
            log.setOperationTime(LocalDateTime.now());
            log.setOldValue(oldValue);
            log.setNewValue(newValue);

            operationLogMapper.insert(log);
        } catch (Exception e) {
            log.error("Failed to create operation log", e);
            throw new RuntimeException("Failed to create operation log: " + e.getMessage());
        }
    }
}