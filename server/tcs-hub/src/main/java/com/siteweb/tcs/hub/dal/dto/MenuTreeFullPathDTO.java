package com.siteweb.tcs.hub.dal.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Data
public class MenuTreeFullPathDTO {
    /**
     * 菜单ID
     */
    private Integer menuItemId;
    /**
     * 菜单名称
     */
    private String menuItemName;
    /**
     * 父节点ID
     */
    private Integer parentMenuItemId;
    /**
     * 路由
     */
    private String path;
    /**
     * 完整路由
     */
    private String fullPath;
    /**
     * 插件ID
     */
    private String PluginId;
    /**
     * 父子关系路径
     */
    private String idPath;
    /**
     * 排序字段
     */
    private Integer layoutPosition;

    private List<MenuTreeFullPathDTO> children;
    public List<MenuTreeFullPathDTO> getChildren() {
        if(children == null){
            children = new ArrayList<>();
        }
        // 按 layout 排序，null 值排在最后
        children.sort(Comparator.comparing(MenuTreeFullPathDTO::getLayoutPosition, Comparator.nullsLast(Comparator.naturalOrder())));
        return children;
    }
    public void addChild(MenuTreeFullPathDTO child) {
        if (children != null) {
            children.add(child);
        }
    }
}
