package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.entity.Role;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RoleMapper extends BaseMapper<Role> {

    @Select("SELECT r.RoleId, r.RoleName, r.Description FROM tcs_role r left join tcs_account_role_map arm "
            +    " on r.RoleId=arm.RoleId where arm.userId=#{userId}")
    List<Role> findRolesByUserId(Integer userId);


    @Delete("DELETE FROM tcs_account_role_map WHERE UserId = #{userId}")
    int deleteRolesByUserId(@Param("userId") Integer userId);

    @Insert("INSERT INTO tcs_account_role_map(UserId, RoleId) VALUES(#{userId}, #{roleId})")
    int insertUserRole(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    @Delete({"SET SQL_SAFE_UPDATES = 0; ",
            "DELETE FROM tcs_account_role_map WHERE roleId = #{roleId}; ",
            "SET SQL_SAFE_UPDATES = 1; "})
    void deleteRoleAccountMapByRoleId(Integer roleId);
}
