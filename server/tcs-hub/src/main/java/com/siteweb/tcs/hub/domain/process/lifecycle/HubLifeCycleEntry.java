package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.LogUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.*;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 生命周期管理入口Actor
 * 负责管理多个GatewayLCM实例，并将对应的生命周期事件转发给相应的管理器处理
 */
@Slf4j
public class HubLifeCycleEntry extends ProbeActor {
    private final ActorRef lifeCycleSpout;
    private final GatewayPersistHandler gatewayPersistHandler;

    // 使用两层Map结构，第一层key为pluginInstanceId，第二层key为gatewayId，value为对应的GatewayLCM实例
    private final Map<String, Map<String, ActorRef>> globalGatewayLifeCycleManagers = new ConcurrentHashMap<>();

    public HubLifeCycleEntry(GatewayPersistHandler gatewayPersistHandler, List<ForeignGateway> foreignGatewayList) {
        this.gatewayPersistHandler = gatewayPersistHandler;
        loadForeignGateways(foreignGatewayList);
        this.lifeCycleSpout = this.context().actorOf(LifeCycleEventSpout.props(), "lifeCycleSpout");
        getProbe().info("LifeCycleEntry started");
    }

    private void loadForeignGateways(List<ForeignGateway> foreignGatewayList) {
        try {
            for (ForeignGateway gateway : foreignGatewayList) {
                // 创建网关生命周期管理器
                loadGatewayLifeCycleManager(gateway);
            }

            LogUtil.info(log, "hub.lifecycle.gateway.load.complete");
            getProbe().info("Gateway Load completed");
        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.gateway.load.failed", e);
            getProbe().error("Failed to Load gateways: " + e.getMessage());
        }
    }

    public static Props props(GatewayPersistHandler gatewayPersistHandler, List<ForeignGateway> foreignGatewayList) {
        return Props.create(HubLifeCycleEntry.class, gatewayPersistHandler, foreignGatewayList);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(LifeCycleEvent.class, this::onLifeEvent)
                .match(QueryPluginForeignGateways.class, this::handleGetPluginGatewayIds)
                .build()
                .orElse(super.createReceive());
    }

    /**
     * 处理生命周期事件
     * 根据事件类型和目标对象，将事件转发给对应的GatewayLCM处理
     */
    private void onLifeEvent(LifeCycleEvent event) {
        try {
            // 如果是网关创建事件，需要先创建对应的GatewayLCM
            if (event.getThingType() == ThingType.GATEWAY
                && event.getEventType() == LifeCycleEventType.CREATE
                && !hasGatewayLifeCycleManager(event.getPluginInstanceId(), event.getForeignGatewayId())) {

                createGatewayLifeCycleManager(event); //if 内存LCM存在，实际这里忽略了create。
            }

            if (event.getThingType() == ThingType.GATEWAY && event.getEventType() == LifeCycleEventType.DELETE) {
                // 删除网关生命周期管理器
                deleteGatewayLifeCycleManager(event);
            }

            if (event.getThingType() == ThingType.GATEWAY && event.getEventType() == LifeCycleEventType.FIELD_UPDATE) {
                // 更新网关生命周期管理器(property)
                updateGatewayLifeCycleManager(event);
            }

            if (event.getThingType() == ThingType.GATEWAY && event.getEventType() == LifeCycleEventType.GATEWAY_ID_CHANGE) {
                // 更新网关生命周期管理器(id)
                updateIdGatewayLifeCycleManager(event);
            }

            // 只有设备事件且对应网关存在才转发
            if ((event.getThingType() == ThingType.DEVICE && hasGatewayLifeCycleManager(event.getPluginInstanceId(), event.getForeignGatewayId()))) {

                forwardEventToManager(event);
            }
        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.event.process.failed", e);
            getProbe().error("Failed to process lifecycle event: " + e.getMessage());
        }
    }

    private void updateIdGatewayLifeCycleManager(LifeCycleEvent event) {
        try {
            if (!gatewayPersistHandler.handleGatewayIdChange(event)) {
                log.error("hub.lifecycle.gateway.id.update.failed: {}", event.getForeignGatewayId());
                return;
            }

            ForeignGatewayConfigChange config = (ForeignGatewayConfigChange) event.getForeignConfigChange();
            String newForeignGatewayId = config.getForeignGatewayID();

            ActorRef manager = getGatewayLifeCycleManager(event.getPluginInstanceId(), event.getForeignGatewayId());

            if (manager != null) {
                manager.tell(new UpdateGatewayIdAction(newForeignGatewayId), getSelf()); ///update id
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateGatewayLifeCycleManager(LifeCycleEvent event) {
        try {
            gatewayPersistHandler.handleUpdateGateway(event); //only monitor unit table
            forwardEventToManager(event);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void deleteGatewayLifeCycleManager(LifeCycleEvent event) {
        try {
            gatewayPersistHandler.handleDeleteGateway(event.getPluginInstanceId(), event.getForeignGatewayId());

            String key = generateGatewayKey(event.getPluginInstanceId(), event.getForeignGatewayId());
            Map<String, ActorRef> gatewayManagers = globalGatewayLifeCycleManagers.get(event.getPluginInstanceId());

            if (gatewayManagers != null) {
                gatewayManagers.get(key).tell(new TerminationMessage(), getSelf());
                gatewayManagers.remove(key);
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }

    }

    private void createGatewayLifeCycleManager(LifeCycleEvent event) {
        try {
            //persist
            gatewayPersistHandler.handleCreateGateway(event);

            //load
            ForeignGateway foreignGateway = gatewayPersistHandler.getForeignGateway(event.getForeignGatewayId());
            loadGatewayLifeCycleManager(foreignGateway);

            //notify
            lifeCycleSpout.tell(event,getSelf());

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 转发事件给对应的网关生命周期管理器
     */
    private void forwardEventToManager(LifeCycleEvent event) {
        ActorRef manager = getGatewayLifeCycleManager(event.getPluginInstanceId(), event.getForeignGatewayId());
        if (manager != null) {
            manager.tell(event, getSelf());
        } else {
            log.warn("hub.lifecycle.gateway.manager.not.found: {} {}", event.getPluginInstanceId(), event.getForeignGatewayId());
        }
    }

    private void handleGetPluginGatewayIds(QueryPluginForeignGateways queryPluginForeignGateways) {
        String pluginInstanceId = queryPluginForeignGateways.getPluginInstanceId();
        getSender().tell(globalGatewayLifeCycleManagers.get(pluginInstanceId).keySet(), getSelf());
    }

    /**
     * load网关生命周期管理器
     */
    private void loadGatewayLifeCycleManager(ForeignGateway foreignGateway) {
        String key = generateGatewayKey(foreignGateway.getPluginId(), foreignGateway.getForeignGatewayID());
        ActorRef manager = getContext().actorOf(
                GatewayLCM.props(foreignGateway, this.lifeCycleSpout),
                "gatewayLCM-" + key);

        Map<String, ActorRef> gatewayManagers = globalGatewayLifeCycleManagers.computeIfAbsent(foreignGateway.getPluginId(), k -> new ConcurrentHashMap<>());
        gatewayManagers.put(foreignGateway.getForeignGatewayID(), manager);

        LogUtil.info(log, "hub.lifecycle.gateway.manager.created", foreignGateway.getPluginId(), foreignGateway.getForeignGatewayID());
    }

    /**
     * 生成网关唯一标识键
     */
    private String generateGatewayKey(String pluginId, String gatewayId) {
        return pluginId + "-" + gatewayId;
    }

    /**
     * 检查是否存在指定的网关生命周期管理器
     */
    private boolean hasGatewayLifeCycleManager(String pluginInstanceId, String gatewayId) {
        Map<String, ActorRef> gatewayManagers = globalGatewayLifeCycleManagers.get(pluginInstanceId);
        return gatewayManagers != null && gatewayManagers.containsKey(gatewayId);
    }

    /**
     * 获取指定的网关生命周期管理器
     */
    private ActorRef getGatewayLifeCycleManager(String pluginInstanceId, String gatewayId) {
        Map<String, ActorRef> gatewayManagers = globalGatewayLifeCycleManagers.get(pluginInstanceId);
        if (gatewayManagers != null) {
            return gatewayManagers.get(gatewayId);
        }
        return null;
    }
}

