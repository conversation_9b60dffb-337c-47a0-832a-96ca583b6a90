package com.siteweb.tcs.hub.domain.letter;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.dal.entity.door.DoorTemplateControl;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import lombok.Data;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;

import java.io.Serializable;
import java.util.Collections;

@Data
public class ForeignControlConfigChange implements Serializable {

    /**
     * 南向控制id
     * 必须有
     */
    private String foreignControlId;

    /**
     * 南向控制名称
     * 必须有
     */
    private String foreignControlName;

    /**
     * 南向关联信号id
     * 必须有
     */
    private String foreignSignalId;

    /**
     * 南向控制含义
     * 必须有
     */
    private String foreignControlMeaning;

    /**
     * 南向控制值
     * 必须有
     */
    private Integer foreignControlValue;

    /**
     * 控制种类
     * 电信B接口下
     * DO：遥控 -> REMOTE_CONTROL
     * AO：遥调 -> REMOTE_REGULATING
     */
    private Integer controlCategoryEnum;

    /**
     * 控制输入最大值
     * 非必须，如果传空，则默认为99
     */
    private Double maxValue;

    /**
     * 控制输入最小值
     * 非必须，如果传空，则默认为0
     */
    private Double minValue;

    private LifeCycleEventType eventType;

    /**
     * 是否可见
     */
    private Boolean visible = true;

    /**
     * 基类id
     */
    private Long baseTypeId;

    /**
     * 描述
     */
    private String description;

    public ControlConfigItem toControlConfigItem(Integer equipmentTemplateId,Integer signalId) {
        return toControlConfigItem(equipmentTemplateId,signalId,null,null);
    }

    public ControlConfigItem toControlConfigItem(Integer equipmentTemplateId, Integer signalId, DoorTemplateControl doorTemplateControl) {
        return toControlConfigItem(equipmentTemplateId,signalId,null,null,doorTemplateControl);
    }

    public ControlConfigItem toControlConfigItem(Integer equipmentTemplateId,Integer signalId,Integer controlId,Integer meaningId) {
        return toControlConfigItem(equipmentTemplateId,signalId,controlId,meaningId,null);
    }

    public ControlConfigItem toControlConfigItem(Integer equipmentTemplateId,Integer signalId,Integer controlId,Integer meaningId,DoorTemplateControl doorTemplateControl){
        ControlMeanings controlMeaningsDTO = new ControlMeanings();
        controlMeaningsDTO.setMeanings(foreignControlMeaning);
        controlMeaningsDTO.setEquipmentTemplateId(equipmentTemplateId);
        controlMeaningsDTO.setParameterValue(foreignControlValue);
        controlMeaningsDTO.setId(meaningId);
        controlMeaningsDTO.setControlId(controlId);
        ControlConfigItem controlConfigItem = new ControlConfigItem();
        controlConfigItem.setControlName(foreignControlName);
        controlConfigItem.setSignalId(signalId);
        controlConfigItem.setEquipmentTemplateId(equipmentTemplateId);
        controlConfigItem.setControlMeaningsList(Collections.singletonList(controlMeaningsDTO));
        controlConfigItem.setVisible(visible);
        controlConfigItem.setDisplayIndex(1);
        controlConfigItem.setCmdToken(" ");
        controlConfigItem.setControlSeverity(1);
        controlConfigItem.setEnable(true);
        controlConfigItem.setControlType((short)1);
        controlConfigItem.setControlId(controlId);
        controlConfigItem.setModuleNo(0);
        controlConfigItem.setCmdToken("16,0");
        controlConfigItem.setBaseTypeId(baseTypeId);

        // 字符串
        controlConfigItem.setDataType((short) 1);
        // 普通控制
        controlConfigItem.setCommandType(controlCategoryEnum);
        controlConfigItem.setDescription(description);

        //默认都是普通控制
        // controlConfigItem.setControlType((short) 1);
         controlConfigItem.setControlCategory(1);

        if (ObjectUtil.isEmpty(maxValue)) {
            controlConfigItem.setMaxValue(99.0);
        } else {
            controlConfigItem.setMaxValue(maxValue);
        }
        if (ObjectUtil.isEmpty(minValue)) {
            controlConfigItem.setMinValue(0.0);
        } else {
            controlConfigItem.setMinValue(minValue);
        }
        if(ObjectUtil.isNotEmpty(doorTemplateControl)){
            controlConfigItem.setBaseTypeId(doorTemplateControl.getBaseTypeId().longValue());
            controlConfigItem.setCmdToken(doorTemplateControl.getCmdToken());
            controlConfigItem.setControlCategory(doorTemplateControl.getControlCategory());
        }
        return controlConfigItem;
    }
}
