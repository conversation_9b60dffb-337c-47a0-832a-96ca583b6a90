package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.ForeignGatewayChange;
import com.siteweb.tcs.hub.domain.letter.enums.EnumGatewayConnectState;
import com.siteweb.tcs.hub.domain.process.GatewayChangeAdapter;
import com.siteweb.tcs.hub.domain.process.MonitorUnitChangeSpout;
import com.siteweb.tcs.hub.domain.process.MonitorUnitStateStore;
import lombok.Setter;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

@Setter
public class GatewayChangePipeline extends DataPipeline{

    private ForeignGateway foreignGateway;

    public GatewayChangePipeline(ActorContext context, ActorRef pipelinePublisher) {
        super(context, pipelinePublisher);
    }

    @Override
    public void create() {
        this.setSpoutActor(getContext().actorOf(
                MonitorUnitChangeSpout.props(getPipelinePublisher()),
                "MonitorUnitChangeSpout"
        ));

        this.setStoreActor(getContext().actorOf(
                MonitorUnitStateStore.props(foreignGateway, this.getSpoutActor()),
                "MonitorUnitStateStore"
        ));

        this.setAdapterActor(getContext().actorOf(
                GatewayChangeAdapter.props(foreignGateway, this.getStoreActor()),
                "GatewayChangeAdapter"
        ));

        //定制逻辑,当pipeLine创建时,让系统默认认为该网关在线
        ForeignGatewayChange foreignGatewayChange = new ForeignGatewayChange()
                .setID(foreignGateway.getForeignGatewayID())
                .setConnectState(EnumGatewayConnectState.ONLINE)
                .setPluginID(foreignGateway.getPluginId());

        this.getAdapterActor().tell(foreignGatewayChange, this.getContext().self());
    }
}

