package com.siteweb.tcs.hub.service.impl;

import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.provider.PortProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: PortService
 * @descriptions: 采集单元端口业务实现类
 * @author: xsx
 * @date: 2024/8/23 10:48
 **/
@Service
public class PortService {
    @Autowired
    private PortProvider portProvider;

    private static final String portNameTemplate = "COM%s";

    public Port createPort(Integer monitorUnitId){
        Integer maxPortNo = getMaxPortNoByMonitorUnitId(monitorUnitId);
        Integer createPortNo = maxPortNo+1;
        Port port = new Port();
        port.setMonitorUnitId(monitorUnitId);
        port.setPortNo(createPortNo);
        port.setPortName(String.format(portNameTemplate,createPortNo));
        port.setDescription("TCS create");
        port.setLinkSamplerUnitId(0);
        //虚拟端口
        port.setPortType(34);
        port.setSetting("comm_host_dev.so");
        Port result = portProvider.createConfig(port);
        return result;
    }

    private Integer getMaxPortNoByMonitorUnitId(Integer monitorUnitId) {
        // 这里需要你补充实际的查询逻辑，暂时返回0
        // 例如：return portProvider.getMaxPortNoByMonitorUnitId(monitorUnitId);
        return 0;
    }
}
