package com.siteweb.tcs.hub.domain.process;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentLoggingEvent;
import com.siteweb.tcs.hub.domain.letter.ForeignLoggingEventChange;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.StoreDeadLetter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

@Slf4j
public class DeviceLoggingEventAdapter extends ProbeActor {
    private ForeignDevice foreignDevice;
    private final ActorRef equipmentSwapCardStore;


    private DeviceLoggingEventAdapter(ForeignDevice foreignDevice, ActorRef equipmentSwapCardStore){
        this.foreignDevice = foreignDevice;
        this.equipmentSwapCardStore = equipmentSwapCardStore;
        getContext().watchWith(equipmentSwapCardStore,new StoreDeadLetter());
    }

    public static Props props(ForeignDevice foreignDevice, ActorRef equipmentSwapCardStore) {
        return Props.create(DeviceLoggingEventAdapter.class, () -> new DeviceLoggingEventAdapter(foreignDevice,equipmentSwapCardStore));
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(ForeignLoggingEventChange.class,this::onForeignLoggingEventChange)
                .match(NeedUpdateAction.class,this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.foreignDevice = (ForeignDevice) needUpdateAction.getConfig();
        }
    }

    private void onForeignLoggingEventChange(ForeignLoggingEventChange foreignLoggingEventChange) {
        if(ObjectUtil.isEmpty(foreignLoggingEventChange)) return;
        String foreignGatewayId = foreignLoggingEventChange.getForeignGatewayId();
        String foreignDeviceId = foreignLoggingEventChange.getForeignDeviceId();
        if(!StringUtils.equals(foreignDevice.getForeignGatewayID(),foreignGatewayId) || !StringUtils.equals(foreignDevice.getForeignDeviceID(),foreignDeviceId))
            return;
        if(CollectionUtil.isEmpty(foreignLoggingEventChange.getLoggingEvent()))
            return;
        EquipmentLoggingEvent equipmentLoggingEvent = new EquipmentLoggingEvent();
        BeanUtil.copyProperties(foreignLoggingEventChange,equipmentLoggingEvent);
        equipmentLoggingEvent.setStationId(foreignDevice.getStationId());
        equipmentLoggingEvent.setMonitorUnitId(foreignDevice.getMonitorUnitId());
        equipmentLoggingEvent.setEquipmentId(foreignDevice.getEquipmentId());
        equipmentSwapCardStore.tell(equipmentLoggingEvent,getSelf());
    }
}

