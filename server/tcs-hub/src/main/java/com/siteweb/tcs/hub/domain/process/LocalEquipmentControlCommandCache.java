package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.ForeignDeviceControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.LiveControlCommand;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class LocalEquipmentControlCommandCache {
    private static final ConcurrentHashMap<String, LiveControlCommand> equipmentLiveControlCommandMap = new ConcurrentHashMap<>();

    public static void saveEquipmentControlCommandRequest(ForeignDeviceControlCommandRequest equipmentControlCommandRequest)
    {
        //遍历request对象，如果没有对应CommandSequenceId的ControlCommandRequest存在，增加转换request加入equipmentLiveControlCommandMap
        equipmentControlCommandRequest.getCommandRequestList().forEach(controlCommandRequest -> {
            if (!equipmentLiveControlCommandMap.containsKey(controlCommandRequest.getSequenceNo())) {
//                LiveControlCommand liveControlCommand = new LiveControlCommand(controlCommandRequest);
//                equipmentLiveControlCommandMap.put(controlCommandRequest.getSequenceNo(), liveControlCommand);
            }
        });
    }

    public static List<LiveControlCommand> queryAllLiveControlCommand() {
        return equipmentLiveControlCommandMap.values().stream().toList();
    }

    public static void saveEquipmentControlCommandResponse(EquipmentControlCommandResponse equipmentControlCommandResponse)
    {
        equipmentControlCommandResponse.getCommandResponseList().forEach(controlCommandResponse -> {
            if (equipmentLiveControlCommandMap.containsKey(controlCommandResponse.getSequenceNo())) {
                LiveControlCommand liveControlCommand = equipmentLiveControlCommandMap.get(controlCommandResponse.getSequenceNo());
                liveControlCommand.update(controlCommandResponse);
            }
        });
    }
}
