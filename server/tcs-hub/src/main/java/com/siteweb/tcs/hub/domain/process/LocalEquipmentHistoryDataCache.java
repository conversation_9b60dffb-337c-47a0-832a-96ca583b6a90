package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.hub.domain.letter.EquipmentSignalHisData;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class LocalEquipmentHistoryDataCache {

    private static final ConcurrentHashMap<Integer, List<EquipmentSignalHisData>> historyDataCache = new ConcurrentHashMap<>();

    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    // 静态代码块，初始化一些默认数据
    static {
        // 初始化设备1的历史数据
        List<EquipmentSignalHisData> equipment1DataList = createMockEquipmentSignalHisData(451013048, LocalDateTime.now().minusDays(1), 5);
        saveEquipmentHistoryData(equipment1DataList);

        // 初始化设备2的历史数据
        List<EquipmentSignalHisData> equipment2DataList = createMockEquipmentSignalHisData(451013049, LocalDateTime.now().minusDays(3), 3);
        saveEquipmentHistoryData(equipment2DataList);

        // 安排每天凌晨执行清理任务
        scheduleDailyCleanup();
    }

    private static void scheduleDailyCleanup() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextMidnight = now.toLocalDate().plusDays(1).atStartOfDay();
        long initialDelay = Duration.between(now, nextMidnight).getSeconds();

        scheduler.scheduleAtFixedRate(() -> {
            cleanupAllOldData();
        }, initialDelay, TimeUnit.DAYS.toSeconds(1), TimeUnit.SECONDS);
    }

    public static void saveEquipmentHistoryData(List<EquipmentSignalHisData> equipmentSignalHisDataList) {
        if (equipmentSignalHisDataList == null || equipmentSignalHisDataList.isEmpty()) {
            return;
        }

        for (EquipmentSignalHisData signalData : equipmentSignalHisDataList) {
            if (signalData == null || signalData.getEquipmentId() == null) {
                continue;
            }
            Integer equipmentId = signalData.getEquipmentId();
            List<EquipmentSignalHisData> dataList = historyDataCache.computeIfAbsent(equipmentId, k -> Collections.synchronizedList(new ArrayList<>()));
            dataList.add(signalData);
        }
    }

    private static void cleanupAllOldData() {
        historyDataCache.forEach((equipmentId, dataList) -> {
            cleanupOldData(dataList);
        });
    }

    private static void cleanupOldData(List<EquipmentSignalHisData> dataList) {
        if (dataList == null) {
            return;
        }
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(7);
        synchronized (dataList) {
            dataList.removeIf(data -> data.getRecordTime().isBefore(cutoffDate));
        }
    }

    public static List<EquipmentSignalHisData> queryEquipmentHistoryData(Integer equipmentId) {
        List<EquipmentSignalHisData> dataList = historyDataCache.get(equipmentId);
        if (dataList == null) {
            return Collections.emptyList();
        }
        synchronized (dataList) {
            return new ArrayList<>(dataList);
        }
    }

    public static Map<Integer, List<EquipmentSignalHisData>> queryAllEquipmentHistoryData() {
        Map<Integer, List<EquipmentSignalHisData>> allData = new HashMap<>();
        historyDataCache.forEach((equipmentId, dataList) -> {
            synchronized (dataList) {
                allData.put(equipmentId, new ArrayList<>(dataList));
            }
        });
        return allData;
    }

    // 创建模拟数据的方法
    private static List<EquipmentSignalHisData> createMockEquipmentSignalHisData(Integer equipmentId, LocalDateTime startTime, int dataCount) {
        List<EquipmentSignalHisData> signalList = new ArrayList<>();
        for (int i = 0; i < dataCount; i++) {
            EquipmentSignalHisData signalData = new EquipmentSignalHisData();
            signalData.setEquipmentId(equipmentId);
            signalData.setSignalId(i);
            signalData.setTriggerVal(Math.random() * 100);
            signalData.setMeanings("信号含义 " + i);
            signalData.setRecordTime(startTime.plusHours(i));
            signalList.add(signalData);
        }
        return signalList;
    }
}