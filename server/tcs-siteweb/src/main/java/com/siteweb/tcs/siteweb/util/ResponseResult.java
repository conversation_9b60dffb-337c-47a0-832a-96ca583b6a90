package com.siteweb.tcs.siteweb.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult {
    /**
     * 执行结果
     */
    @JsonView(RestfulView.Default.class)
    private int code;
    /**
     * 错误消息
     */
    @JsonProperty("msg")
    @JsonView(RestfulView.Default.class)
    private String message;
    /**
     * 執行時間
     */
    @JsonView(RestfulView.Default.class)
    private long timestamp;

    /**
     * 返回内容
     */
    @JsonView(RestfulView.Default.class)
    private Object data;

    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @return 成功的响应结果
     */
    public static ResponseResult success(Object data) {
        ResponseResult result = new ResponseResult();
        result.setCode(200);
        result.setMessage("success");
        result.setData(data);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    /**
     * 创建成功响应（无数据）
     *
     * @return 成功的响应结果
     */
    public static ResponseResult success() {
        return success(null);
    }

    /**
     * 创建失败响应
     *
     * @param message 错误消息
     * @return 失败的响应结果
     */
    public static ResponseResult fail(String message) {
        ResponseResult result = new ResponseResult();
        result.setCode(500);
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }

    /**
     * 创建失败响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @return 失败的响应结果
     */
    public static ResponseResult fail(int code, String message) {
        ResponseResult result = new ResponseResult();
        result.setCode(code);
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
}
