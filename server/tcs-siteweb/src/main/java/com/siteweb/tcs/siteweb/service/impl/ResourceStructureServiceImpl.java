package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.ResourceStructureMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.IResourceStructureService;
import com.siteweb.tcs.siteweb.util.I18n;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * Resource Structure Service Implementation
 */
@Slf4j
@Service
public class ResourceStructureServiceImpl extends ServiceImpl<ResourceStructureMapper, ResourceStructure> implements IResourceStructureService {

    @Autowired
    private ResourceStructureMapper resourceStructureMapper;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Autowired
    private I18n i18n;

    @Value("${resource.structure.generate.uuid:false}")
    private boolean generateUUID;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ResourceStructure getStructureByID(Integer structureId) {
        if (structureId == null) {
            return null;
        }
        return getById(structureId);
    }

    @Override
    public ResourceStructure getRootStructure() {
        // Assuming root is identified by parentResourceStructureId being null or a specific value (e.g., 0)
        // Adjust this logic if root identification is different (e.g., a specific ID like 1)
        return getOne(new LambdaQueryWrapper<ResourceStructure>()
                .isNull(ResourceStructure::getParentResourceStructureId) // Or .eq(ResourceStructure::getParentResourceStructureId, 0) if that's the convention
                .last("LIMIT 1")); // To ensure only one root is returned if multiple qualify
    }

    @Override
    public List<ResourceStructure> getAllStructures() {
        return list(); // Simply returns all records from the table
    }

    @Override
    public List<ResourceStructure> getChildrenByParentId(Integer parentId) {
        if (parentId == null) {
            return List.of(); // Or handle as an error/empty list depending on requirements
        }
        return list(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getParentResourceStructureId, parentId)
                .orderByAsc(ResourceStructure::getSortValue)); // Assuming children should be sorted
    }

    @Override
    public Integer getTreeRootId() {
        QueryWrapper<ResourceStructure> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parentresourcestructureid", 0);
        queryWrapper.select("resourcestructureid");
        try {
            ResourceStructure resourceStructure = baseMapper.selectOne(queryWrapper);
            return resourceStructure.getResourceStructureId();
        } catch (Exception e) {
            log.error("Query resource structure tree root error: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public ResourceStructure findByOriginIdAndStructureType(Integer originId, Integer structureType) {
        if (originId == null || structureType == null) {
            return null;
        }

        return getOne(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getStructureTypeId, structureType)
                .last("LIMIT 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByID(Integer resourceStructureId) {
        if (resourceStructureId == null) {
            log.warn("Cannot delete resource structure: ID is null");
            return false;
        }

        try {
            // First check if the resource structure exists
            ResourceStructure resourceStructure = getById(resourceStructureId);
            if (resourceStructure == null) {
                log.warn("Resource structure not found with ID: {}", resourceStructureId);
                return false;
            }

            // Delete the resource structure
            boolean result = removeById(resourceStructureId);
            if (result) {
                log.info("Resource structure deleted successfully: {}", resourceStructureId);
            } else {
                log.warn("Failed to delete resource structure: {}", resourceStructureId);
            }

            return result;
        } catch (Exception e) {
            log.error("Error deleting resource structure: {}", e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ResourceStructure resourceStructure) {
        if (resourceStructure == null || resourceStructure.getResourceStructureId() == null) {
            log.warn("Cannot update resource structure: entity or ID is null");
            return false;
        }

        try {
            // First check if the resource structure exists
            ResourceStructure existingStructure = getById(resourceStructure.getResourceStructureId());
            if (existingStructure == null) {
                log.warn("Resource structure not found with ID: {}", resourceStructure.getResourceStructureId());
                return false;
            }

            // Update the resource structure
            boolean result = updateById(resourceStructure);
            if (result) {
                log.info("Resource structure updated successfully: {}", resourceStructure.getResourceStructureId());
            } else {
                log.warn("Failed to update resource structure: {}", resourceStructure.getResourceStructureId());
            }

            return result;
        } catch (Exception e) {
            log.error("Error updating resource structure: {}", e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        }
    }

    @Override
    public List<ResourceStructure> findByParentResourceStructureId(Integer parentResourceStructureId) {
        if (parentResourceStructureId == null) {
            log.warn("Cannot find resource structures: parent resource structure ID is null");
            return List.of();
        }

        return list(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getParentResourceStructureId, parentResourceStructureId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(ResourceStructure resourceStructure) {
        try {
            // 如果 resourceStructureId 为 null 或 0，则生成一个新的 ID
            if (resourceStructure.getResourceStructureId() == null || resourceStructure.getResourceStructureId() == 0) {
                Integer resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
                resourceStructure.setResourceStructureId(resourceStructureId);
            }

            // 主动清空 sceneId
            resourceStructure.setSceneId(null);

            // 设置层级路径
            if (resourceStructure.getLevelOfPath() == null) {
                resourceStructure.setLevelOfPath(resourceStructure.getResourceStructureId().toString());
            } else {
                resourceStructure.setLevelOfPath(resourceStructure.getLevelOfPath() + "." + resourceStructure.getResourceStructureId());
            }

            // 如果 generateUUID 为 true，则设置 extendedField 字段
            // 设置guid
            if (generateUUID) {
                JsonNode extValue = resourceStructure.getExtendedField();
                ObjectMapper mapper = new ObjectMapper();

                ObjectNode newValue = mapper.createObjectNode();
                newValue.put("guid", UUID.randomUUID().toString());

                ArrayNode arrayNode;
                if (extValue != null && extValue.isArray()) {
                    arrayNode = (ArrayNode) extValue;
                } else {
                    arrayNode = mapper.createArrayNode();
                }

                arrayNode.add(newValue);

                resourceStructure.setExtendedField(arrayNode);
            }

            // 插入资源结构
            int result = resourceStructureMapper.insert(resourceStructure);

            if (result > 0) {
                // 发送创建事件
                changeEventService.sendCreate(resourceStructure);

                // 记录操作日志todo xsx 权限后面要测试
                operationDetailService.recordOperationLog(-1, resourceStructure.getResourceStructureId().toString(), OperationObjectTypeEnum.RESOURCE_STRUCTURE, i18n.T("resource.structure.name"), i18n.T("add"), "", resourceStructure.getResourceStructureName());

                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("Failed to create resource structure: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResourceStructure findResourceStructureByOriginIdAndParentIdAndStructureTypeId(Integer originId, Integer parentId, Integer structureTypeId) {
        return resourceStructureMapper.selectOne(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getOriginParentId, parentId)
                .eq(ResourceStructure::getStructureTypeId, structureTypeId));
    }

}
