package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Event log action entity
 */
@Data
@TableName("tbl_eventlogaction")
public class EventLogAction implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "LogActionId")
    private Integer logActionId;

    @TableField("ActionName")
    private String actionName;

    @TableField("StationId")
    private Integer stationId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("TriggerType")
    private Integer triggerType;

    @TableField("StartExpression")
    private String startExpression;

    @TableField("SuppressExpression")
    private String suppressExpression;

    @TableField("InformMsg")
    private String informMsg;

    @TableField("Description")
    private String description;
}
