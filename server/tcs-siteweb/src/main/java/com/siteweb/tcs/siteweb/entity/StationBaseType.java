package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Station base type entity
 */
@Data
@TableName("tbl_stationbasetype")
public class StationBaseType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id")
    private Integer id;

    @TableField("Type")
    private String type;

    @TableField("StandardId")
    private Integer standardId;
}
