package com.siteweb.tcs.siteweb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.siteweb.common.ResponseHelper;
import com.siteweb.tcs.siteweb.common.ResponseResult;
import com.siteweb.tcs.siteweb.dto.ApplyStandardDTO;
import com.siteweb.tcs.siteweb.dto.BaseMapCountWarnDTO;
import com.siteweb.tcs.siteweb.dto.BaseStationMapTypeDTO;
import com.siteweb.tcs.siteweb.dto.ControlStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.dto.EqStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.dto.EventStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.dto.IdValueDTO;
import com.siteweb.tcs.siteweb.dto.SignalStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.entity.StationBaseType;
import com.siteweb.tcs.siteweb.service.IStationBaseTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Station Base Type Controller
 */
@Slf4j
@RestController
@RequestMapping("/configstation-api/v1/station-base-types")
public class StationBaseTypeController {

    @Autowired
    private IStationBaseTypeService stationBaseTypeService;

    /**
     * 获取站点基类型列表（分页）
     */
    @GetMapping
    public ResponseEntity<ResponseResult> getStationBaseType() {
        return ResponseHelper.successful(stationBaseTypeService.findCurrentStationBaseType());
    }

    @GetMapping(value = "/basestationmap")
    public ResponseEntity<ResponseResult> getBaseStationMapType(Integer id) {
        return ResponseHelper.successful(stationBaseTypeService.getBaseStationMapType(id));
    }

    @PostMapping(value = "/basestationmap")
    public ResponseEntity<ResponseResult> saveBaseStationMapType(@RequestBody BaseStationMapTypeDTO baseStationMapTypeDTO) {
        stationBaseTypeService.saveBaseStationMapType(baseStationMapTypeDTO);
        return ResponseHelper.successful();
    }

    @GetMapping(value = "/equipmenttemplate")
    public ResponseEntity<ResponseResult> getEquipmentTemplateStationBaseType() {
        return ResponseHelper.successful(stationBaseTypeService.getEquipmentTemplateStationBaseType());
    }

    @GetMapping(value = "/signal")
    public ResponseEntity<ResponseResult> getSignalStationBaseType() {
        return ResponseHelper.successful(stationBaseTypeService.getSignalStationBaseType());
    }

    @PostMapping(value = "/signalbasemap")
    public ResponseEntity<ResponseResult> saveSignalBaseMap(@RequestBody SignalStationBaseTypeDTO signalStationBaseTypeDTO) {
        stationBaseTypeService.saveSignalBaseMap(signalStationBaseTypeDTO);
        return ResponseHelper.successful();
    }

    @GetMapping(value = "/event")
    public ResponseEntity<ResponseResult> getEventStationBaseType() {
        return ResponseHelper.successful(stationBaseTypeService.getEventStationBaseType());
    }

    @PostMapping(value = "/eventbasemap")
    public ResponseEntity<ResponseResult> saveEventBaseMap(@RequestBody EventStationBaseTypeDTO eventStationBaseTypeDTO) {
        stationBaseTypeService.saveEventBaseMap(eventStationBaseTypeDTO);
        return ResponseHelper.successful();
    }

    @GetMapping(value = "/control")
    public ResponseEntity<ResponseResult> getControlStationBaseType() {
        return ResponseHelper.successful(stationBaseTypeService.getControlStationBaseType());
    }

    @PostMapping(value = "/controlbasemap")
    public ResponseEntity<ResponseResult> saveControlBaseMap(@RequestBody ControlStationBaseTypeDTO controlStationBaseTypeDTO) {
        stationBaseTypeService.saveControlBaseMap(controlStationBaseTypeDTO);
        return ResponseHelper.successful();
    }

    @GetMapping(value = "/basemapwarn")
    public ResponseEntity<ResponseResult> getWarnCountsInBaseMap(@RequestParam Integer standardDicId, 
                                                                @RequestParam Integer stationBaseType,
                                                                @RequestParam List<Long> baseTypeId, 
                                                                @RequestParam Integer baseType) {
        BaseMapCountWarnDTO result = stationBaseTypeService.getWarnCountsInBaseMap(standardDicId, stationBaseType, baseTypeId, baseType);
        return ResponseHelper.successful(result);
    }

    @PostMapping(value = "/applystandard")
    public ResponseEntity<ResponseResult> applyStandard(@RequestBody ApplyStandardDTO applyStandardDTO) {
        Long result = stationBaseTypeService.applyStandard(applyStandardDTO);
        return ResponseHelper.successful(result);
    }
}
