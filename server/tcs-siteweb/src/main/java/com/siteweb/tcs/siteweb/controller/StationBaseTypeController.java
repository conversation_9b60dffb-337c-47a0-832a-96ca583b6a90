package com.siteweb.tcs.siteweb.controller;

import com.siteweb.tcs.siteweb.common.ResponseHelper;
import com.siteweb.tcs.siteweb.common.ResponseResult;
import com.siteweb.tcs.siteweb.service.IStationBaseTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/stationbasetype")
public class StationBaseTypeController {
    
    @Autowired
    private IStationBaseTypeService stationBaseTypeService;

    @GetMapping
    public ResponseEntity<ResponseResult> getStationBaseType() {
        return ResponseHelper.successful(stationBaseTypeService.findCurrentStationBaseType());
    }

    @GetMapping(value = "/basestationmap")
    public ResponseEntity<ResponseResult> getBaseStationMapType(Integer id) {
        return ResponseHelper.successful(stationBaseTypeService.getBaseStationMapType(id));
    }
} 