package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;
import com.siteweb.tcs.siteweb.entity.Control;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import com.siteweb.tcs.siteweb.mapper.ControlMapper;
import com.siteweb.tcs.siteweb.service.IControlMeaningsService;
import com.siteweb.tcs.siteweb.service.IControlService;
import com.siteweb.tcs.siteweb.util.I18n;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.*;

/**
 * Control Service Implementation
 */
@Slf4j
@Service
public class ControlServiceImpl extends ServiceImpl<ControlMapper, Control> implements IControlService {

    private static final int GENERATE_CONTROL_ID_FLAG = -1;

    @Autowired
    private ControlMapper controlMapper;

    @Autowired
    private IControlMeaningsService controlMeaningsService;

    @Autowired
    private I18n i18n;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createControl(ControlConfigItem controlConfigItem) {
        Control control = new Control();
        BeanUtils.copyProperties(controlConfigItem, control, "id", "controlId");

        if (Objects.isNull(control.getControlId()) || control.getControlId() == GENERATE_CONTROL_ID_FLAG) {
            Integer controlId = findMaxControlIdByEquipmentTemplateId(control.getEquipmentTemplateId());
            control.setControlId(controlId);
        }
        if (Objects.isNull(control.getDisplayIndex())) {
            control.setDisplayIndex(findMaxDisplayIndexByEquipmentTemplateId(control.getEquipmentTemplateId()) + 1);
        }

        controlMapper.insertControl(control); // Use custom insert due to potential keyword issues

        if (!CollectionUtils.isEmpty(controlConfigItem.getControlMeaningsList())) {
            controlConfigItem.getControlMeaningsList().forEach(e -> {
                e.setEquipmentTemplateId(control.getEquipmentTemplateId());
                e.setControlId(control.getControlId());
            });
            controlMeaningsService.saveBatch(controlConfigItem.getControlMeaningsList());
        }
        controlConfigItem.setId(control.getId());
        controlConfigItem.setControlId(control.getControlId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateControlByControl(ControlConfigItem controlConfigItem) {
        Control control = new Control();
        BeanUtils.copyProperties(controlConfigItem, control);
        control.setId(null); // Ensure ID is not used for update by equipmentTemplateId and controlId

        controlMapper.updateControl(control); // Use custom update due to potential keyword issues

        if (controlConfigItem.getControlMeaningsList() != null) {
            controlMeaningsService.batchUpdateControlMeanings(controlConfigItem.getControlMeaningsList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteControl(int equipmentTemplateId, int controlId) {
        controlMapper.deleteControl(equipmentTemplateId, controlId);
        controlMeaningsService.deleteByEquipmentTemplateIdAndControlId(equipmentTemplateId, controlId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteControl(int equipmentTemplateId, List<Integer> controlIds) {
        if (CollectionUtils.isEmpty(controlIds)) {
            return;
        }
        for (Integer controlId : controlIds) {
            controlMapper.deleteControl(equipmentTemplateId, controlId);
        }
    }

    @Override
    public List<ControlConfigItem> findItemByEquipmentTemplateId(Integer equipmentTemplateId) {
        return Optional.ofNullable(controlMapper.findControlItemByEquipmentTemplateId(equipmentTemplateId)).orElseGet(List::of)
                .stream()
                .sorted(Comparator.comparingInt(ControlConfigItem::getDisplayIndex))
                .toList();
    }

    @Override
    public ControlConfigItem getControlInfo(Integer equipmentTemplateId, Integer controlId) {
        return controlMapper.findByEquipmentTemplateIdAndControlId(equipmentTemplateId, controlId);
    }

    @Override
    public Integer findMaxControlIdByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxId = controlMapper.findMaxControlIdByEquipmentTemplateId(equipmentTemplateId);
        return maxId == null ? 1 : maxId + 1;
    }

    @Override
    public int findMaxDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxIndex = controlMapper.findMaxDisplayIndexByEquipmentTemplateId(equipmentTemplateId);
        return maxIndex == null ? 0 : maxIndex; // Default to 0 if no records exist, so next is 1
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongControls() {
        try {
            List<Control> controls = new ArrayList<>();

            // 创建联通控制
            // 这里需要实现批量保存联通控制的逻辑
            // 由于没有具体的数据，这里只提供一个空实现
            log.warn("batchsaveLianTongControls method is not fully implemented");
            return true;
        } catch (Exception e) {
            log.error("Failed to batch save LianTong controls", e);
            return false;
        }
    }

    @Override
    public List<Long> findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(Integer equipmentTemplateId) {
        return controlMapper.findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    public int deleteControl(Integer equipmentTemplateId, Integer controlId) {
        return controlMapper.deleteControl(equipmentTemplateId, controlId);
    }

    @Override
    public Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds) {
        // TODO: 实现控制标准化应用逻辑
        // 这里应该根据标准ID和设备模板ID列表来应用控制标准化
        // 暂时返回0，实际实现需要根据业务逻辑来完成
        return 0L;
    }
}
