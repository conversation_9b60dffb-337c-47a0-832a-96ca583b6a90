package com.siteweb.tcs.siteweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * REST API response codes
 */
@Getter
@AllArgsConstructor
public enum RestfulCode {
    
    // Common codes
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    
    // Monitor Unit related codes
    MonitorUnitNotFound(1001, "监控单元不存在"),
    MonitorUnitHasEquipment(1002, "监控单元下存在设备"),
    MonitorUnitNameDuplicate(1003, "监控单元名称重复"),
    MonitorUnitNameInvalid(1004, "监控单元名称无效"),
    MonitorUnitCategoryInvalid(1005, "监控单元类型无效"),
    
    // Equipment related codes
    EquipmentNotFound(2001, "设备不存在"),
    EquipmentTemplateNotFound(2002, "设备模板不存在"),
    EquipmentNameDuplicate(2003, "设备名称重复"),
    
    // Sampler Unit related codes
    SamplerUnitNotFound(3001, "采集单元不存在"),
    SamplerUnitDeleteFailed(3002, "采集单元删除失败"),
    
    // Port related codes
    PortNotFound(4001, "端口不存在"),
    PortDeleteFailed(4002, "端口删除失败");
    
    private final int code;
    private final String message;
    
    /**
     * Get integer value of the enum
     * 
     * @return Integer code
     */
    public Integer IntValue() {
        return this.code;
    }
} 