package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Channel map entity
 */
@Data
@TableName("tsl_channelmap")
public class ChannelMap implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "SamplerUnitId")
    private Integer samplerUnitId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("OriginalChannelNo")
    private Integer originalChannelNo;

    @TableField("StandardChannelNo")
    private Integer standardChannelNo;
}
