package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Station category entity
 */
@Data
@TableName("tbl_stationcategory")
public class StationCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StationCategoryId")
    private Integer stationCategoryId;

    @TableField("StationCategoryName")
    private String stationCategoryName;

    @TableField("Description")
    private String description;
}
