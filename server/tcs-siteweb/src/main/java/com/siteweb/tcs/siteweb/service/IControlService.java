package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;
import com.siteweb.tcs.siteweb.entity.Control;

import java.util.List;

/**
 * Control Service Interface
 */
public interface IControlService extends IService<Control> {

    void createControl(ControlConfigItem controlConfigItem);

    void updateControlByControl(ControlConfigItem controlConfigItem);

    void deleteControl(int equipmentTemplateId, int controlId);

    void batchDeleteControl(int equipmentTemplateId, List<Integer> controlIds);

    List<ControlConfigItem> findItemByEquipmentTemplateId(Integer equipmentTemplateId);

    ControlConfigItem getControlInfo(Integer equipmentTemplateId, Integer controlId);

    Integer findMaxControlIdByEquipmentTemplateId(Integer equipmentTemplateId);

    int findMaxDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 批量保存联通控制
     *
     * @return 是否保存成功
     */
    boolean batchsaveLianTongControls();

    /**
     * Find base type IDs not in control base dictionary for equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of base type IDs
     */
    List<Long> findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 删除控制命令
     *
     * @param equipmentTemplateId 设备模板ID
     * @param controlId 控制ID
     * @return 删除数量
     */
    int deleteControl(Integer equipmentTemplateId, Integer controlId);

    /**
     * 应用控制标准化
     *
     * @param standardId 标准ID
     * @param equipmentTemplateIds 设备模板ID列表
     * @return 应用的数量
     */
    Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds);
}
