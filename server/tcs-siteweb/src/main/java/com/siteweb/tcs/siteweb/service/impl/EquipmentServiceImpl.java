package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.StructureTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.EquipmentMapper;
import com.siteweb.tcs.siteweb.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Implementation of Equipment Service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EquipmentServiceImpl extends ServiceImpl<EquipmentMapper, Equipment> implements IEquipmentService {

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private EquipmentMapper equipmentMapper;

    @Autowired(required = false)
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired(required = false)
    private IOperationDetailService operationDetailService;

    @Autowired
    private IResourceStructureService resourceStructureService;

    @Autowired
    private IChangeEventService changeEventService;

    @Override
    public boolean existsByMonitorUnitIdAndEquipmentName(Integer equipmentId, Integer monitorUnitId, String equipmentName) {
        LambdaQueryWrapper<Equipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Equipment::getMonitorUnitId, monitorUnitId)
                .eq(Equipment::getEquipmentName, equipmentName);

        if (equipmentId != null) {
            queryWrapper.ne(Equipment::getEquipmentId, equipmentId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Equipment createEquipment(Equipment equipment) {
        // 设置创建时间
        if (equipment.getUpdateTime() == null) {
            equipment.setUpdateTime(LocalDateTime.now());
        }

        // 尝试生成设备ID
        if (equipment.getEquipmentId() == null) {
            if (primaryKeyValueService != null) {
                // 如果有主键生成服务，尝试使用它
                try {
                    Integer equipmentId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT, 0);
                    if (equipmentId != null) {
                        equipment.setEquipmentId(equipmentId);
                        log.info("Generated equipment ID: {}", equipmentId);
                    }
                } catch (Exception e) {
                    log.error("Failed to generate equipment ID", e);
                }
            }

            // 如果没有主键生成服务或生成失败，让MyBatis-Plus处理
            if (equipment.getEquipmentId() == null) {
                log.warn("No primary key service available or ID generation failed, relying on MyBatis-Plus");
            }
        }

        // 使用MyBatis-Plus的save方法保存设备
        boolean success = save(equipment);

        if (!success) {
            log.error("Failed to save equipment");
            throw new RuntimeException("Failed to save equipment");
        }

        // 后置处理逻辑
        postCreateEquipment(equipment);

        // 记录操作日志
        if (operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        null, // 用户ID，这里使用null表示系统操作
                        equipment.getEquipmentId().toString(),
                        OperationObjectTypeEnum.EQUIPMENT,
                        "equipment.equipmentName",
                        "添加",
                        "",
                        equipment.getEquipmentName()
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        // 发送创建事件
        if (changeEventService != null) {
            changeEventService.sendCreate(equipment);
        }

        log.info("Created equipment: {}", equipment.getEquipmentId());
        return equipment;
    }

    /**
     * 设备创建后的后置处理逻辑
     *
     * @param equipment 设备实体
     */
    private void postCreateEquipment(Equipment equipment) {
        // 1. 可以在此处添加事件发布逻辑
        // eventPublisher.publishEvent(new EquipmentCreatedEvent(equipment));

        // 2. 可以添加缓存更新逻辑

        // 3. 可以添加日志记录逻辑
        log.info("Post-processing for equipment creation: {}", equipment.getEquipmentId());

        // 4. 其他业务逻辑...
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer equipmentInstance(Integer equipmentId) {
        // 获取设备信息
        Equipment equipment = getById(equipmentId);
        if (equipment == null) {
            log.error("Equipment not found: {}", equipmentId);
            return null;
        }

        // 获取原始模板
        Integer originalTemplateId = equipment.getEquipmentTemplateId();
        EquipmentTemplate originalTemplate = equipmentTemplateService.findById(originalTemplateId);
        if (originalTemplate == null) {
            log.error("Original template not found: {}", originalTemplateId);
            return originalTemplateId;
        }

        // 创建新模板
        EquipmentTemplate newTemplate = new EquipmentTemplate();
        newTemplate.setEquipmentTemplateName(equipment.getEquipmentName() + "_Template");
        newTemplate.setParentTemplateId(originalTemplateId);
        newTemplate.setMemo("Generated from equipment " + equipmentId);
        newTemplate.setProtocolCode(originalTemplate.getProtocolCode());
        newTemplate.setEquipmentCategory(originalTemplate.getEquipmentCategory());
        newTemplate.setEquipmentType(originalTemplate.getEquipmentType());
        newTemplate.setProperty(originalTemplate.getProperty());
        newTemplate.setDescription(originalTemplate.getDescription());
        newTemplate.setEquipmentStyle(originalTemplate.getEquipmentStyle());
        newTemplate.setUnit(originalTemplate.getUnit());
        newTemplate.setVendor(originalTemplate.getVendor());

        // 生成模板ID
        if (primaryKeyValueService != null) {
            try {
                Integer templateId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT_TEMPLATE, 0);
                if (templateId != null) {
                    newTemplate.setEquipmentTemplateId(templateId);
                }
            } catch (Exception e) {
                log.error("Failed to generate template ID", e);
            }
        }

        // 保存新模板
        equipmentTemplateService.save(newTemplate);

        // 更新设备的模板ID
        equipment.setEquipmentTemplateId(newTemplate.getEquipmentTemplateId());
        updateById(equipment);

        log.info("Created equipment template instance: {} based on equipment: {}", newTemplate.getEquipmentTemplateId(), equipmentId);

        return newTemplate.getEquipmentTemplateId();
    }

    @Override
    public List<Equipment> allEquipment() {
        return list(new LambdaQueryWrapper<Equipment>()
                .orderByAsc(Equipment::getDisplayIndex)
                .orderByAsc(Equipment::getEquipmentName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEquipment(Integer equipmentId) {
        Equipment equipment = getById(equipmentId);
        if (equipment != null) {
            boolean result = removeById(equipmentId);

            // 记录操作日志
            if (result && operationDetailService != null) {
                try {
                    operationDetailService.recordOperationLog(
                            null, // 用户ID，这里使用null表示系统操作
                            equipment.getEquipmentId().toString(),
                            OperationObjectTypeEnum.EQUIPMENT,
                            "equipment.equipmentName",
                            "删除",
                            equipment.getEquipmentName(),
                            ""
                    );
                } catch (Exception e) {
                    log.warn("Failed to record operation log", e);
                }
            }

            // 发送删除事件
            if (result && changeEventService != null) {
                changeEventService.sendDelete(equipment);
            }

            return result;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Equipment updateEquipment(EquipmentDetailDTO equipmentDetailDTO) {
        if (equipmentDetailDTO == null || equipmentDetailDTO.getEquipmentId() == null) {
            log.error("Invalid equipment detail DTO");
            throw new IllegalArgumentException("Equipment ID cannot be null");
        }

        Equipment oldEquipment = getById(equipmentDetailDTO.getEquipmentId());
        if (oldEquipment == null) {
            log.error("Equipment not found: {}", equipmentDetailDTO.getEquipmentId());
            throw new RuntimeException("Equipment not found");
        }

        // 创建新的设备对象，并从DTO中复制属性
        Equipment equipment = new Equipment();
        BeanUtils.copyProperties(equipmentDetailDTO, equipment);

        // 设置更新时间
        equipment.setUpdateTime(LocalDateTime.now());

        // 记录操作日志
        if (operationDetailService != null) {
            try {
                operationDetailService.compareEntitiesRecordLog(
                        null, // 用户ID，这里使用null表示系统操作
                        oldEquipment,
                        equipment
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log", e);
            }
        }

        // 更新设备
        boolean success = updateById(equipment);
        if (!success) {
            log.error("Failed to update equipment");
            throw new RuntimeException("Failed to update equipment");
        }

        // 发送更新事件
        if (changeEventService != null) {
            changeEventService.sendUpdate(equipment);
        }

        // 处理设备工程信息
        if (equipmentDetailDTO.getEquipmentProjectInfo() != null) {
            // 这里可以添加对设备工程信息的处理，如果有相应的表和服务
            log.info("Equipment project info update is not implemented yet");
        }

        return getById(equipment.getEquipmentId());
    }

    @Override
    public Equipment findEquipmentById(Integer equipmentId) {
        return getById(equipmentId);
    }

    @Override
    public List<Equipment> findByMonitorUnitId(Integer monitorUnitId) {
        return list(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getMonitorUnitId, monitorUnitId)
                .orderByAsc(Equipment::getDisplayIndex)
                .orderByAsc(Equipment::getEquipmentName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMonitorUnitId(Integer monitorUnitId) {
        // Get all equipment associated with the monitor unit
        List<Equipment> equipmentList = findByMonitorUnitId(monitorUnitId);

        // Delete all equipment by monitor unit ID
        LambdaQueryWrapper<Equipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Equipment::getMonitorUnitId, monitorUnitId);

        int deleteCount = equipmentMapper.delete(queryWrapper);

        if (deleteCount > 0) {
            log.info("Deleted {} equipment for monitor unit ID {}", deleteCount, monitorUnitId);

            // Log the deletion of each equipment
            for (Equipment equipment : equipmentList) {
                log.debug("Deleted equipment: ID={}, Name={}", equipment.getEquipmentId(), equipment.getEquipmentName());

                // Record operation log if available
                if (operationDetailService != null) {
                    try {
                        operationDetailService.recordOperationLog(
                                null, // User ID
                                equipment.getEquipmentId().toString(),
                                OperationObjectTypeEnum.EQUIPMENT,
                                "equipment.equipmentName",
                                "删除",
                                equipment.getEquipmentName(),
                                ""
                        );
                    } catch (Exception e) {
                        log.warn("Failed to record operation log for equipment deletion: {}", equipment.getEquipmentId(), e);
                    }
                }

                // 发送删除事件
                if (changeEventService != null) {
                    changeEventService.sendDelete(equipment);
                }
            }
        } else {
            log.info("No equipment found for monitor unit ID {}", monitorUnitId);
        }
    }

    @Override
    public List<Equipment> getEquipmentsByResourceStructureId(Integer resourceStructureId) {
        if (resourceStructureId == null) {
            return List.of();
        }
        // Assuming Equipment entity has a 'resourceStructureId' field
        return list(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getResourceStructureId, resourceStructureId)
                .orderByAsc(Equipment::getDisplayIndex)
                .orderByAsc(Equipment::getEquipmentName));

        // If returning List<EquipmentVO>:
        // List<Equipment> equipments = list(new LambdaQueryWrapper<Equipment>()
        //         .eq(Equipment::getResourceStructureId, resourceStructureId)
        //         .orderByAsc(Equipment::getDisplayIndex)
        //         .orderByAsc(Equipment::getEquipmentName));
        // return equipments.stream().map(EquipmentVO::fromEntity).collect(Collectors.toList()); // Assuming EquipmentVO has a fromEntity static method
    }

    @Override
    public boolean existsByEquipmentTemplateId(Integer equipmentTemplateId) {
        return !equipmentMapper.selectList(Wrappers.lambdaQuery(Equipment.class)
                .eq(Equipment::getEquipmentTemplateId, equipmentTemplateId)).isEmpty();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncEquipmentStructure(Equipment equipment) {
        if (equipment == null || equipment.getEquipmentId() == null) {
            log.warn("Cannot sync equipment structure: equipment or equipment ID is null");
            return;
        }

        // 获取设备的资源结构ID
        Integer resourceStructureId = equipment.getResourceStructureId();
        if (resourceStructureId == null) {
            // 如果设备没有关联资源结构，尝试查找或创建一个
            resourceStructureId = findOrCreateResourceStructure(equipment);
            if (resourceStructureId != null) {
                // 更新设备的资源结构ID
                equipment.setResourceStructureId(resourceStructureId);
                updateById(equipment);
                log.info("Updated equipment {} with resource structure ID {}", equipment.getEquipmentId(), resourceStructureId);

                // 发送更新事件
                if (changeEventService != null) {
                    changeEventService.sendUpdate(equipment);
                }
            } else {
                log.warn("Failed to find or create resource structure for equipment {}", equipment.getEquipmentId());
            }
        } else {
            // 验证资源结构是否存在
            ResourceStructure structure = resourceStructureService.getStructureByID(resourceStructureId);
            if (structure == null) {
                // 如果不存在，创建一个新的
                resourceStructureId = findOrCreateResourceStructure(equipment);
                if (resourceStructureId != null) {
                    // 更新设备的资源结构ID
                    equipment.setResourceStructureId(resourceStructureId);
                    updateById(equipment);
                    log.info("Updated equipment {} with new resource structure ID {}", equipment.getEquipmentId(), resourceStructureId);

                    // 发送更新事件
                    if (changeEventService != null) {
                        changeEventService.sendUpdate(equipment);
                    }
                }
            } else {
                log.info("Equipment {} already has valid resource structure ID {}", equipment.getEquipmentId(), resourceStructureId);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncSwatchEquipment(Equipment equipment) {
        if (equipment == null || equipment.getEquipmentId() == null) {
            log.warn("Cannot sync swatch equipment: equipment or equipment ID is null");
            return;
        }

        // 获取设备的资源结构ID
        Integer resourceStructureId = equipment.getResourceStructureId();
        if (resourceStructureId == null) {
            log.warn("Cannot sync swatch equipment: resource structure ID is null for equipment {}", equipment.getEquipmentId());
            return;
        }

        // 验证资源结构是否存在
        ResourceStructure structure = resourceStructureService.getStructureByID(resourceStructureId);
        if (structure == null) {
            log.warn("Cannot sync swatch equipment: resource structure {} not found for equipment {}",
                    resourceStructureId, equipment.getEquipmentId());
            return;
        }

        // 更新设备信息
        Equipment existingEquipment = getById(equipment.getEquipmentId());
        if (existingEquipment != null) {
            // 保留原有的一些属性，只更新需要同步的属性
            equipment.setUpdateTime(LocalDateTime.now());
            // 更新设备
            updateById(equipment);
            log.info("Synchronized swatch equipment: {}", equipment.getEquipmentId());

            // 发送更新事件
            if (changeEventService != null) {
                changeEventService.sendUpdate(equipment);
            }
        } else {
            log.warn("Cannot sync swatch equipment: equipment {} not found", equipment.getEquipmentId());
        }
    }

    /**
     * 查找或创建设备的资源结构
     *
     * @param equipment 设备实体
     * @return 资源结构ID，如果创建失败则返回null
     */
    private Integer findOrCreateResourceStructure(Equipment equipment) {
        if (equipment == null) {
            return null;
        }

        // 首先尝试查找设备所在站点的资源结构
        Integer stationId = equipment.getStationId();
        Integer houseId = equipment.getHouseId();

        // 获取根资源结构
        Integer rootId = resourceStructureService.getTreeRootId();
        if (rootId == null) {
            log.error("Root resource structure not found");
            return null;
        }

        // 查找站点资源结构
        ResourceStructure stationStructure = null;
        if (stationId != null) {
            // 在根下查找站点结构
            List<ResourceStructure> children = resourceStructureService.getChildrenByParentId(rootId);
            for (ResourceStructure child : children) {
                if (child.getOriginId() != null && child.getOriginId().equals(stationId)) {
                    stationStructure = child;
                    break;
                }
            }

            // 如果没找到，创建站点结构
            if (stationStructure == null) {
                stationStructure = new ResourceStructure();
                stationStructure.setResourceStructureName("Station " + stationId);
                stationStructure.setParentResourceStructureId(rootId);
                stationStructure.setStructureTypeId(StructureTypeEnum.STATION.getValue());
                stationStructure.setOriginId(stationId);
                stationStructure.setDisplay(Boolean.TRUE); // 显示
                stationStructure.setSortValue(0); // 排序值

                // 保存站点结构
                resourceStructureService.save(stationStructure);
                log.info("Created station resource structure: {}", stationStructure.getResourceStructureId());
            }
        }

        // 如果没有站点结构，直接使用根结构
        Integer parentId = (stationStructure != null) ? stationStructure.getResourceStructureId() : rootId;

        // 查找或创建房间结构（如果有房间ID）
        ResourceStructure roomStructure = null;
        if (houseId != null) {
            // 在站点下查找房间结构
            List<ResourceStructure> children = resourceStructureService.getChildrenByParentId(parentId);
            for (ResourceStructure child : children) {
                if (child.getOriginId() != null && child.getOriginId().equals(houseId)) {
                    roomStructure = child;
                    break;
                }
            }

            // 如果没找到，创建房间结构
            if (roomStructure == null) {
                roomStructure = new ResourceStructure();
                roomStructure.setResourceStructureName("Room " + houseId);
                roomStructure.setParentResourceStructureId(parentId);
                roomStructure.setStructureTypeId(StructureTypeEnum.ROOM.getValue());
                roomStructure.setOriginId(houseId);
                roomStructure.setDisplay(Boolean.TRUE); // 显示
                roomStructure.setSortValue(0); // 排序值

                // 保存房间结构
                resourceStructureService.save(roomStructure);
                log.info("Created room resource structure: {}", roomStructure.getResourceStructureId());
            }

            parentId = roomStructure.getResourceStructureId();
        }

        // 创建设备结构
        ResourceStructure equipmentStructure = new ResourceStructure();
        equipmentStructure.setResourceStructureName(equipment.getEquipmentName());
        equipmentStructure.setParentResourceStructureId(parentId);
        equipmentStructure.setStructureTypeId(StructureTypeEnum.EQUIPMENT.getValue());
        equipmentStructure.setOriginId(equipment.getEquipmentId());
        equipmentStructure.setDisplay(Boolean.TRUE); // 显示
        equipmentStructure.setSortValue(equipment.getDisplayIndex() != null ? equipment.getDisplayIndex() : 0); // 排序值

        // 保存设备结构
        resourceStructureService.save(equipmentStructure);
        log.info("Created equipment resource structure: {}", equipmentStructure.getResourceStructureId());

        return equipmentStructure.getResourceStructureId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByStationId(Integer stationId) {
        if (stationId == null) {
            log.warn("Cannot delete equipment by station ID: station ID is null");
            return false;
        }

        // 查询该站点下的所有设备
        List<Equipment> equipmentList = list(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getStationId, stationId));

        if (equipmentList.isEmpty()) {
            log.info("No equipment found for station ID {}", stationId);
            return false;
        }

        // 删除设备
        int deleteCount = equipmentMapper.delete(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getStationId, stationId));
        log.info("Deleted {} equipment for station ID {}", deleteCount, stationId);

        // 记录操作日志
        if (operationDetailService != null) {
            for (Equipment equipment : equipmentList) {
                try {
                    operationDetailService.recordOperationLog(
                            null, // 用户ID
                            equipment.getEquipmentId().toString(),
                            OperationObjectTypeEnum.EQUIPMENT,
                            "equipment.equipmentName",
                            "删除",
                            equipment.getEquipmentName(),
                            ""
                    );
                } catch (Exception e) {
                    log.warn("Failed to record operation log for equipment deletion: {}", equipment.getEquipmentId(), e);
                }
            }
        }
        return deleteCount > 0;
    }

    @Override
    public List<Equipment> findBySamplerUnitId(Integer samplerUnitId) {
        if (samplerUnitId == null) {
            return List.of();
        }
        return list(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getSamplerUnitId, samplerUnitId)
                .orderByAsc(Equipment::getDisplayIndex)
                .orderByAsc(Equipment::getEquipmentName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByHouseId(Integer houseId) {
        if (houseId == null) {
            log.warn("Cannot delete equipment by house ID: house ID is null");
            return;
        }

        // 查询该房间下的所有设备
        List<Equipment> equipmentList = list(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getHouseId, houseId));

        if (equipmentList.isEmpty()) {
            log.info("No equipment found for house ID {}", houseId);
            return;
        }

        // 删除设备
        int deleteCount = equipmentMapper.delete(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getHouseId, houseId));

        log.info("Deleted {} equipment for house ID {}", deleteCount, houseId);

        // 记录操作日志
        if (operationDetailService != null) {
            for (Equipment equipment : equipmentList) {
                try {
                    operationDetailService.recordOperationLog(
                            null, // 用户ID
                            equipment.getEquipmentId().toString(),
                            OperationObjectTypeEnum.EQUIPMENT,
                            "equipment.equipmentName",
                            "删除",
                            equipment.getEquipmentName(),
                            ""
                    );
                } catch (Exception e) {
                    log.warn("Failed to record operation log for equipment deletion: {}", equipment.getEquipmentId(), e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySamplerUnitId(Integer samplerUnitId) {
        if (samplerUnitId == null) {
            log.warn("Cannot delete equipment by sampler unit ID: sampler unit ID is null");
            return;
        }

        // 查询该采集单元下的所有设备
        List<Equipment> equipmentList = findBySamplerUnitId(samplerUnitId);

        if (equipmentList.isEmpty()) {
            log.info("No equipment found for sampler unit ID {}", samplerUnitId);
            return;
        }

        // 删除设备
        int deleteCount = equipmentMapper.delete(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getSamplerUnitId, samplerUnitId));

        log.info("Deleted {} equipment for sampler unit ID {}", deleteCount, samplerUnitId);

        // 记录操作日志
        if (operationDetailService != null) {
            for (Equipment equipment : equipmentList) {
                try {
                    operationDetailService.recordOperationLog(
                            null, // 用户ID
                            equipment.getEquipmentId().toString(),
                            OperationObjectTypeEnum.EQUIPMENT,
                            "equipment.equipmentName",
                            "删除",
                            equipment.getEquipmentName(),
                            ""
                    );
                } catch (Exception e) {
                    log.warn("Failed to record operation log for equipment deletion: {}", equipment.getEquipmentId(), e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearEquipmentResourceStructureId(Integer resourceStructureId) {
        if (resourceStructureId == null) {
            log.warn("Cannot clear equipment resource structure ID: resource structure ID is null");
            return false;
        }

        try {
            // 查询该资源结构下的所有设备
            List<Equipment> equipmentList = getEquipmentsByResourceStructureId(resourceStructureId);

            if (equipmentList.isEmpty()) {
                log.info("No equipment found for resource structure ID {}", resourceStructureId);
                return true;
            }

            // 清除设备的资源结构ID
            for (Equipment equipment : equipmentList) {
                equipment.setResourceStructureId(0);
                updateById(equipment);
            }
            changeEventService.sendBatchUpdate(equipmentList);
            log.info("Cleared resource structure ID for {} equipment", equipmentList.size());
            return true;
        } catch (Exception e) {
            log.error("Error clearing equipment resource structure ID: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEquipmentCategoryByCategoryIdMap(Integer businessId, Integer categoryTypeId) {
        if (businessId == null || categoryTypeId == null) {
            log.warn("Cannot update equipment category: businessId or categoryTypeId is null");
            return false;
        }

        try {
            // 获取所有设备
            List<Equipment> equipmentList = list();
            if (equipmentList.isEmpty()) {
                log.info("No equipment found to update category");
                return true;
            }

            // 更新设备分类
            int updateCount = 0;
            for (Equipment equipment : equipmentList) {
                // 根据业务ID和分类类型ID更新设备分类
                // 这里需要根据实际业务逻辑实现，可能需要调用mapper的自定义方法
                // 或者通过其他服务获取映射关系后更新
                boolean updated = equipmentMapper.updateEquipmentCategoryByCategoryIdMap(
                        equipment.getEquipmentId(), businessId, categoryTypeId);
                if (updated) {
                    updateCount++;
                    // 发送更新事件
                    if (changeEventService != null) {
                        changeEventService.sendUpdate(equipment);
                    }
                }
            }

            log.info("Updated category for {} equipment with businessId {} and categoryTypeId {}",
                    updateCount, businessId, categoryTypeId);
            return true;
        } catch (Exception e) {
            log.error("Error updating equipment category: {}", e.getMessage(), e);
            return false;
        }
    }
}
