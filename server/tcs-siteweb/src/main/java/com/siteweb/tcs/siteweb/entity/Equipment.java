package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Equipment entity
 */
@Data
@TableName("tbl_equipment")
@ChangeSource(channel = "tcs", product = "siteweb", source = "equipment")
public class Equipment implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StationId")
    private Integer stationId;

    @TableId(value = "EquipmentId")
    private Integer equipmentId;

    @TableField("EquipmentName")
    private String equipmentName;

    @TableField("EquipmentNo")
    private String equipmentNo;

    @TableField("EquipmentModule")
    private String equipmentModule;

    @TableField("EquipmentStyle")
    private String equipmentStyle;

    @TableField("AssetState")
    private Integer assetState;

    @TableField("Price")
    private Double price;

    @TableField("UsedLimit")
    private Double usedLimit;

    @TableField("UsedDate")
    private LocalDateTime usedDate;

    @TableField("BuyDate")
    private LocalDateTime buyDate;

    @TableField("Vendor")
    private String vendor;

    @TableField("Unit")
    private String unit;

    @TableField("EquipmentCategory")
    private Integer equipmentCategory;

    @TableField("EquipmentType")
    private Integer equipmentType;

    @TableField("EquipmentClass")
    private Integer equipmentClass;

    @TableField("EquipmentState")
    private Integer equipmentState;

    @TableField("EventExpression")
    private String eventExpression;

    @TableField("StartDelay")
    private Double startDelay;

    @TableField("EndDelay")
    private Double endDelay;

    @TableField("Property")
    private String property;

    @TableField("Description")
    private String description;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("HouseId")
    private Integer houseId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("WorkStationId")
    private Integer workStationId;

    @TableField("SamplerUnitId")
    private Integer samplerUnitId;

    @TableField("DisplayIndex")
    private Integer displayIndex;

    @TableField("ConnectState")
    private Integer connectState;

    @TableField("UpdateTime")
    private LocalDateTime updateTime;

    @TableField("ParentEquipmentId")
    private String parentEquipmentId;

    @TableField("RatedCapacity")
    private String ratedCapacity;

    @TableField("InstalledModule")
    private String installedModule;

    @TableField("ProjectName")
    private String projectName;

    @TableField("ContractNo")
    private String contractNo;

    @TableField("InstallTime")
    private LocalDateTime installTime;

    @TableField("EquipmentSN")
    private String equipmentSN;

    @TableField("SO")
    private String so;

    @TableField("ResourceStructureId")
    private Integer resourceStructureId;

    @TableField("ExtValue")
    private String extValue;

    @TableField("photo")
    private String photo;

    /**
     * 变更类型，用于标识设备变更的来源
     * 0: 样板站同步过来的设备
     */
    @TableField(exist = false)
    private Integer changeType;
}
