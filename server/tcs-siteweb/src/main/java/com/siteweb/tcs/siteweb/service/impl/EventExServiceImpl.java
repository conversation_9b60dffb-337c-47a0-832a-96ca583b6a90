package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.EventEx;
import com.siteweb.tcs.siteweb.mapper.EventExMapper;
import com.siteweb.tcs.siteweb.service.IEventExService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Event Extension Service Implementation
 */
@Slf4j
@Service
public class EventExServiceImpl extends ServiceImpl<EventExMapper, EventEx> implements IEventExService {

    @Autowired
    private EventExMapper eventExMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByEvent(Integer equipmentTemplateId, Integer eventId) {
        eventExMapper.deleteByEvent(equipmentTemplateId, eventId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEventx(Integer equipmentTemplateId, Integer eventId, Integer turnover) {
        // 查询是否已存在
        LambdaQueryWrapper<EventEx> queryWrapper = new LambdaQueryWrapper<EventEx>()
                .eq(EventEx::getEquipmentTemplateId, equipmentTemplateId)
                .eq(EventEx::getEventId, eventId);
        
        EventEx eventEx = getOne(queryWrapper);
        
        if (eventEx == null) {
            // 不存在则创建
            eventEx = new EventEx();
            eventEx.setEquipmentTemplateId(equipmentTemplateId);
            eventEx.setEventId(eventId);
            eventEx.setTurnover(turnover);
            save(eventEx);
        } else {
            // 存在则更新
            eventEx.setTurnover(turnover);
            updateById(eventEx);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<EventEx> eventExList) {
        if (CollectionUtils.isEmpty(eventExList)) {
            return;
        }
        
        for (EventEx eventEx : eventExList) {
            // 查询是否已存在
            LambdaQueryWrapper<EventEx> queryWrapper = new LambdaQueryWrapper<EventEx>()
                    .eq(EventEx::getEquipmentTemplateId, eventEx.getEquipmentTemplateId())
                    .eq(EventEx::getEventId, eventEx.getEventId());
            
            EventEx existEventEx = getOne(queryWrapper);
            
            if (existEventEx == null) {
                // 不存在则创建
                save(eventEx);
            } else {
                // 存在则更新
                existEventEx.setTurnover(eventEx.getTurnover());
                updateById(existEventEx);
            }
        }
    }
}
