package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.CategoryIdMap;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.mapper.EquipmentTemplateMapper;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Equipment Template Service Implementation
 */
@Slf4j
@Service
public class EquipmentTemplateServiceImpl extends ServiceImpl<EquipmentTemplateMapper, EquipmentTemplate> implements IEquipmentTemplateService {

    @Autowired
    private EquipmentTemplateMapper equipmentTemplateMapper;

    // Assuming these services will be available or implemented later
    @Autowired(required = false) // Marked as not required for now to avoid startup issues if not present
    private ISignalService signalService;

    @Autowired(required = false)
    private IEventService eventService;

    @Autowired(required = false)
    private IControlService controlService;

    @Autowired(required = false)
    private ICategoryIdMapService categoryIdMapService;

    @Autowired(required = false)
    private ISamplerService samplerService;

    @Autowired(required = false)
    private ISignalMeaningsService signalMeaningsService;

    @Autowired(required = false)
    private ISignalPropertyService signalPropertyService;

    @Autowired(required = false)
    private IEventConditionService eventConditionService;

    @Autowired(required = false)
    private IControlMeaningsService controlMeaningsService;


    // From siteweb6, the B interface root template has a specific name or property.
    // This might need to be a configurable value or a more robust query.
    // For now, using a placeholder name.
    private static final String B_INTERFACE_ROOT_TEMPLATE_NAME = "B_INTERFACE_ROOT";
    private static final String B_INTERFACE_PROTOCOL_CODE = "BInterfaceProtocol"; // Example protocol code

    @Override
    public Integer getBInterfaceDeviceTemplateRootId() {
        // Attempt to find by a specific known name or protocol code for B-Interface root templates.
        // This logic needs to match how it's identified in siteweb6-config-server.
        // Option 1: By a specific name if it's unique and known.
        // EquipmentTemplate rootTemplate = getOne(Wrappers.<EquipmentTemplate>lambdaQuery()
        //        .eq(EquipmentTemplate::getEquipmentTemplateName, B_INTERFACE_ROOT_TEMPLATE_NAME)
        //        .isNull(EquipmentTemplate::getParentTemplateId));
        // Option 2: By protocol code and being a root template
        EquipmentTemplate rootTemplate = getOne(Wrappers.<EquipmentTemplate>lambdaQuery()
                .eq(EquipmentTemplate::getProtocolCode, B_INTERFACE_PROTOCOL_CODE) // Assuming a specific protocol code
                .isNull(EquipmentTemplate::getParentTemplateId));

        if (rootTemplate != null) {
            return rootTemplate.getEquipmentTemplateId();
        }
        // Fallback or more complex query if needed based on original logic
        log.warn("BInterfaceDeviceTemplateRootId could not be found with current criteria.");
        return equipmentTemplateMapper.getBInterfaceDeviceTemplateRootId(); // Fallback to a direct mapper call if it has specific logic
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplateById(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            return false;
        }
        // Cascade delete related entities
        // This order is generally safe, but dependencies might require adjustments
        if (signalService != null) {
            // signalService.deleteByEquipmentTemplateId(equipmentTemplateId); // Placeholder
            log.warn("SignalService.deleteByEquipmentTemplateId not fully implemented for cascading delete.");
        }
        if (eventService != null) {
            // eventService.deleteByEquipmentTemplateId(equipmentTemplateId); // Placeholder
            log.warn("EventService.deleteByEquipmentTemplateId not fully implemented for cascading delete.");
        }
        if (controlService != null) {
            // controlService.deleteByEquipmentTemplateId(equipmentTemplateId); // Placeholder
            log.warn("ControlService.deleteByEquipmentTemplateId not fully implemented for cascading delete.");
        }

        // Finally, delete the template itself
        return removeById(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EquipmentTemplate createEquipmentTemplate(EquipmentTemplate equipmentTemplate) {
        // Ensure equipmentTemplateId is null for auto-generation or assigned if input type.
        // Based on TblEquipmentTemplate, ID is IdType.INPUT, so it should be provided or handled by a sequence/generator if not.
        // For simplicity, if it's null, we'll assume we need to generate it or that the DB handles it.
        // If it's meant to be manually assigned, the caller should set it.

        // Check for unique name if required by business logic (not explicitly shown in controller snippet but good practice)
        // if (existsByEquipmentTemplateName(equipmentTemplate.getEquipmentTemplateName())) {
        //    throw new RuntimeException("Equipment template name already exists: " + equipmentTemplate.getEquipmentTemplateName());
        // }

        boolean result = save(equipmentTemplate);
        if (result) {
            return equipmentTemplate; // Contains the ID if auto-generated and supported by MyBatis-Plus config
        } else {
            return null;
        }
    }

    @Override
    public List<EquipmentTemplate> queryTemplate(EquipmentTemplateVO equipmentTemplateVO) {
        return equipmentTemplateMapper.queryTemplateByVO(equipmentTemplateVO);
    }

    @Override
    public boolean existsChildTemplate(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) return false;
        return equipmentTemplateMapper.countByParentTemplateId(equipmentTemplateId) > 0;
    }

    @Override
    public EquipmentTemplate findById(Integer equipmentTemplateId) {
        return equipmentTemplateMapper.selectById(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEquipmentTemplateCategoryByCategoryIdMap(Integer businessId, Integer categoryTypeId) {
        if (businessId == null || categoryTypeId == null) {
            log.warn("Cannot update equipment template category: businessId or categoryTypeId is null");
            return false;
        }

        try {
            // 获取所有设备模板
            List<EquipmentTemplate> templateList = list();
            if (templateList.isEmpty()) {
                log.info("No equipment template found to update category");
                return true;
            }

            // 获取分类映射
            List<CategoryIdMap> categoryIdMaps = categoryIdMapService.list(new LambdaQueryWrapper<CategoryIdMap>()
                    .eq(CategoryIdMap::getBusinessId, businessId)
                    .eq(CategoryIdMap::getCategoryTypeId, categoryTypeId));
            if (categoryIdMaps.isEmpty()) {
                log.warn("No category ID maps found for businessId {} and categoryTypeId {}", businessId, categoryTypeId);
                return false;
            }

            // 更新设备模板分类
            int updateCount = 0;
            for (EquipmentTemplate template : templateList) {
                Integer originalCategoryId = template.getEquipmentCategory();
                if (originalCategoryId == null) {
                    continue;
                }

                // 查找对应的分类映射
                for (CategoryIdMap categoryIdMap : categoryIdMaps) {
                    if (categoryIdMap.getOriginalCategoryId().equals(originalCategoryId)) {
                        template.setEquipmentCategory(categoryIdMap.getBusinessCategoryId());
                        updateById(template);
                        updateCount++;
                        break;
                    }
                }
            }

            log.info("Updated category for {} equipment templates with businessId {} and categoryTypeId {}",
                    updateCount, businessId, categoryTypeId);
            return true;
        } catch (Exception e) {
            log.error("Error updating equipment template category: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEquipmentBaseTypeToNull() {
        return equipmentTemplateMapper.update(Wrappers.lambdaUpdate(EquipmentTemplate.class)
                .set(EquipmentTemplate::getEquipmentBaseType, null))>0;
    }

    @Override
    public List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(Integer equipmentTemplateIdDiv) {
        if (equipmentTemplateIdDiv == null) {
            return new ArrayList<>();
        }

        // 查询符合条件的设备模板ID
        List<EquipmentTemplate> templateList = list(new LambdaQueryWrapper<EquipmentTemplate>()
                .eq(EquipmentTemplate::getEquipmentTemplateId, equipmentTemplateIdDiv));

        // 提取设备模板ID
        List<Integer> templateIds = new ArrayList<>();
        for (EquipmentTemplate template : templateList) {
            templateIds.add(template.getEquipmentTemplateId());
        }

        return templateIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongEquipmentTemplate() {
        try {
            // 这里需要实现批量保存联通标准的设备模板的逻辑
            // 由于没有具体的数据，这里只提供一个空实现
            log.warn("batchsaveLianTongEquipmentTemplate method is not fully implemented");
            return true;
        } catch (Exception e) {
            log.error("Error batch saving LianTong equipment templates: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public EquipmentTemplate findByName(String templateName) {
        if (!StringUtils.hasText(templateName)) {
            log.warn("Template name is empty");
            return null;
        }

        try {
            LambdaQueryWrapper<EquipmentTemplate> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EquipmentTemplate::getEquipmentTemplateName, templateName);

            EquipmentTemplate template = getOne(queryWrapper);

            if (template == null) {
                log.warn("No equipment template found for name: {}", templateName);
            } else {
                log.debug("Found equipment template with ID {} for name: {}", template.getEquipmentTemplateId(), templateName);
            }

            return template;
        } catch (Exception e) {
            log.error("Error finding equipment template by name: {}", templateName, e);
            return null;
        }
    }
}