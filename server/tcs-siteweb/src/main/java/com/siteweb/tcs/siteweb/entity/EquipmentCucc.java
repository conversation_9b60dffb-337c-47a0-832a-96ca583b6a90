package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 设备实体类 (CUCC)
 */
@Data
@TableName("tbl_equipmentcucc")
public class EquipmentCucc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID (复合主键)
     */
    @TableField("StationId")
    private Integer stationId;

    /**
     * 监控单元ID
     */
    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    /**
     * 设备ID (复合主键)
     */
    @TableField("EquipmentId")
    private Integer equipmentId;

    @TableField("DeviceID")
    private String deviceId;

    @TableField("DeviceName")
    private String deviceName;

    @TableField("DeviceRID")
    private String deviceRid;

    @TableField("SUID")
    private String suid;

    @TableField("DeviceVender")
    private String deviceVender;

    @TableField("DeviceType")
    private String deviceType;

    @TableField("MFD")
    private String mfd;

    @TableField("ControllerType")
    private String controllerType;

    @TableField("SoftwareVersion")
    private String softwareVersion;

    @TableField("BatchNo")
    private String batchNo;

    @TableField("Password")
    private String password;

    @TableField("ExtendField1")
    private String extendField1;

    @TableField("ExtendField2")
    private String extendField2;
}