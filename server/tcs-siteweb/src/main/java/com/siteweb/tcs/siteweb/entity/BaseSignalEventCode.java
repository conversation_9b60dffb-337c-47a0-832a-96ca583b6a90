package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 基础信号事件代码实体类
 */
@Data
@TableName("tbl_basesignaleventcode")
public class BaseSignalEventCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代码ID
     */
    @TableId(value = "CodeId", type = IdType.INPUT)
    private Integer codeId;

    /**
     * 分类
     */
    @TableField("Category")
    private String category;

    /**
     * 信号
     */
    @TableField("Signal")
    private String signal;

    /**
     * 事件
     */
    @TableField("EVENT")
    private String event;

    /**
     * 描述
     */
    @TableField("Description")
    private String description;
}