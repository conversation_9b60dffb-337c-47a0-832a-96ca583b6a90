package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.dto.BaseStationMapTypeDTO;
import com.siteweb.tcs.siteweb.entity.StationBaseType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Station Base Type Mapper
 */
@Mapper
@Repository
public interface StationBaseTypeMapper extends BaseMapper<StationBaseType> {

    /**
     * 获取基站映射类型
     *
     * @param stationBaseTypeId 站点基类型ID
     * @param standardType 标准类型
     * @return 基站映射类型列表
     */
    List<BaseStationMapTypeDTO> getBaseStationMapType(@Param("stationBaseTypeId") Integer stationBaseTypeId,
                                                       @Param("standardType") Integer standardType);
}
