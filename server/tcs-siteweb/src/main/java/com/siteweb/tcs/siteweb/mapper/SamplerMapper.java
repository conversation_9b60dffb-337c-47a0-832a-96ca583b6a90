package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.Sampler;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Sampler Mapper
 */
@Mapper
@Repository
public interface SamplerMapper extends BaseMapper<Sampler> {
    
    /**
     * Find a sampler by protocol code
     *
     * @param protocolCode Protocol code
     * @return Sampler entity or null if not found
     */
    Sampler findByProtocolCode(@Param("protocolCode") String protocolCode);
}
