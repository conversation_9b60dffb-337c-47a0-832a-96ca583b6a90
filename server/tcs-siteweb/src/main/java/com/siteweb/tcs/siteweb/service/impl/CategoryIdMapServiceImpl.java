package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.CategoryIdMap;
import com.siteweb.tcs.siteweb.entity.OriginBusinessCategoryMap;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.enums.StandardCategoryEnum;
import com.siteweb.tcs.siteweb.mapper.CategoryIdMapMapper;
import com.siteweb.tcs.siteweb.service.ICategoryIdMapService;
import com.siteweb.tcs.siteweb.service.IDataItemService;
import com.siteweb.tcs.siteweb.service.IOriginBusinessCategoryMapService;
import com.siteweb.tcs.siteweb.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Category ID Map Service Implementation
 */
@Slf4j
@Service
public class CategoryIdMapServiceImpl extends ServiceImpl<CategoryIdMapMapper, CategoryIdMap> implements ICategoryIdMapService {

    @Autowired
    private IOriginBusinessCategoryMapService originBusinessCategoryMapService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IDataItemService dataItemService;

    @Autowired
    private CategoryIdMapMapper categoryIdMapMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean dianXinBusinessCategoryFromOriginCategory() {
        try {
            // 先删除现有的分类映射
            remove(new LambdaQueryWrapper<>());

            // 获取原始分类映射
            List<OriginBusinessCategoryMap> originMaps = originBusinessCategoryMapService.list();
            if (originMaps == null || originMaps.isEmpty()) {
                log.warn("No origin business category maps found");
                return true; // 没有数据也算成功
            }

            // 创建新的分类映射
            List<CategoryIdMap> categoryIdMaps = new ArrayList<>();
            for (OriginBusinessCategoryMap originMap : originMaps) {
                CategoryIdMap categoryIdMap = new CategoryIdMap();
                categoryIdMap.setBusinessId(2); // 电信标准的业务ID为2
                categoryIdMap.setCategoryTypeId(7); // 设备分类类型为7
                categoryIdMap.setOriginalCategoryId(originMap.getOriginCategory());
                categoryIdMap.setBusinessCategoryId(originMap.getOriginCategory() + 1000); // 电信标准的分类从1001开始
                categoryIdMaps.add(categoryIdMap);
            }

            // 批量保存
            return saveBatch(categoryIdMaps);
        } catch (Exception e) {
            log.error("Failed to convert DianXin business category from origin category", e);
            return false;
        }
    }

    @Override
    public Map<Integer, String> findBusinessCategoryFromOriginCategory(Integer originCategoryKey) {
        StandardCategoryEnum standardCategoryEnum = sysConfigService.findStandardCategoryEnum();
        //维谛标准直接返回对应的设备种类
        if (Objects.equals(standardCategoryEnum, StandardCategoryEnum.EMR)) {
            return dataItemService.findMapByEntryIdAndItemId(DataEntryEnum.EQUIPMENT_CATEGORY, originCategoryKey);
        }
        LambdaQueryWrapper<CategoryIdMap> queryWrapper = Wrappers.lambdaQuery(CategoryIdMap.class)
                .eq(CategoryIdMap::getBusinessId, standardCategoryEnum.getValue())
                .eq(CategoryIdMap::getCategoryTypeId, 2)
                .eq(CategoryIdMap::getOriginalCategoryId, originCategoryKey);
        Map<Integer, String> dataItemMap = dataItemService.findMapByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY);
        List<CategoryIdMap> categoryIdMapList = categoryIdMapMapper.selectList(queryWrapper);
        return categoryIdMapList.stream()
                .collect(Collectors.toMap(CategoryIdMap::getBusinessCategoryId, e -> dataItemMap.getOrDefault(e.getBusinessCategoryId(),""), (v1, v2) -> v2));
    }
}
