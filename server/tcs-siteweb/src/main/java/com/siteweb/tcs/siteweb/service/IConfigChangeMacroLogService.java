package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.ConfigChangeMacroLog;
import com.siteweb.tcs.siteweb.enums.ChangeOperatorEnum;

/**
 * 配置变更宏观日志服务接口
 */
public interface IConfigChangeMacroLogService extends IService<ConfigChangeMacroLog> {
    
    /**
     * 记录配置变更日志
     *
     * @param objectId 对象ID
     * @param configId 配置ID
     * @param changeOperatorEnum 变更操作枚举
     * @return 是否记录成功
     */
    boolean configChangeLog(String objectId, Integer configId, ChangeOperatorEnum changeOperatorEnum);
}
