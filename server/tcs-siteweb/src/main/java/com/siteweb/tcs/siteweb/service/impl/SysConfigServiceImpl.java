package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.SysConfig;
import com.siteweb.tcs.siteweb.enums.SysConfigEnum;
import com.siteweb.tcs.siteweb.mapper.SysConfigMapper;
import com.siteweb.tcs.siteweb.service.ISysConfigService;
import org.springframework.stereotype.Service;

/**
 * 系统配置服务实现类
 */
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements ISysConfigService {

    @Override
    public SysConfig findByKey(SysConfigEnum sysConfigEnum) {
        LambdaQueryWrapper<SysConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysConfig::getConfigKey, sysConfigEnum.getConfigKey());
        return getOne(wrapper);
    }

    @Override
    public boolean createSysConfig(SysConfig sysConfig) {
        return save(sysConfig);
    }

    @Override
    public boolean updateSysConfig(SysConfig sysConfig) {
        return updateById(sysConfig);
    }
}
