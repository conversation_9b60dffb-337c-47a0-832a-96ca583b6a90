package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Sampler Unit Mapper
 */
@Mapper
@Repository
public interface SamplerUnitMapper extends BaseMapper<SamplerUnit> {
    
    /**
     * Find referenced sampler names by sampler ID list
     *
     * @param samplerIdList List of sampler IDs
     * @return Set of referenced sampler names
     */
    Set<String> findReferenceSamplerNameBySamplerIdList(@Param("samplerIdList") List<Integer> samplerIdList);
    
    /**
     * Select sampler units with port information for a specific monitor unit
     *
     * @param monitorUnitId Monitor unit ID
     * @return List of sampler units
     */
    List<SamplerUnit> selectSamplerUnitWithPort(@Param("monitorUnitId") Integer monitorUnitId);
    
    /**
     * Find a sampler unit by monitor unit ID, sampler unit name, and port name
     *
     * @param monitorUnitId Monitor unit ID
     * @param samplerUnitName Sampler unit name
     * @param portName Port name
     * @return Sampler unit
     */
    SamplerUnit findSamplerUnit(
            @Param("monitorUnitId") Integer monitorUnitId, 
            @Param("samplerUnitName") String samplerUnitName, 
            @Param("portName") String portName);
}
