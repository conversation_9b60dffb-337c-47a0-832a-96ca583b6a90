package com.siteweb.tcs.siteweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WorkStationTypeEnum {
    APPLICATION_SERVER(1, "应用服务器(AS)"),
    DATA_SERVER(2, "数据服务器（DS）"),
    APPLICATION_SERVER_II(31, "应用服务器(AS)2"),
    BUSINESS_SERVER(20, "业务服务器(BS)"),
    BUSINESS_SERVER_II(32, "业务服务器(BS)2"),
    DATABASE_SERVER(6, "数据库服务器"),
    NOTIFICATION_SERVER(16, "通知服务器"),
    REALTIME_DATA_SERVER(23, "实时数据服务器"),
    MOBILE_INTERFACE_SERVER(24, "手机接口服务器"),
    RMU(8, "RMU"),
    BACKGROUD_SERVER(22, "后台服务器");
    private final Integer value;
    private final String describe;
}
