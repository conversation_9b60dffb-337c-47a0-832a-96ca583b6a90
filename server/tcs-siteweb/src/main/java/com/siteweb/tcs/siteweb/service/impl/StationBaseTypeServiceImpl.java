package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.StationBaseType;
import com.siteweb.tcs.siteweb.entity.SysConfig;
import com.siteweb.tcs.siteweb.entity.StandardType;
import com.siteweb.tcs.siteweb.mapper.StationBaseTypeMapper;
import com.siteweb.tcs.siteweb.service.IStationBaseTypeService;
import com.siteweb.tcs.siteweb.service.IStandardTypeService;
import com.siteweb.tcs.siteweb.service.ISysConfigService;
import com.siteweb.tcs.siteweb.dto.BaseStationMapTypeDTO;
import com.siteweb.tcs.siteweb.dto.IdValueDTO;
import com.siteweb.tcs.siteweb.enums.StandardCategoryEnum;
import com.siteweb.tcs.siteweb.enums.SysConfigEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Station Base Type Service Implementation
 */
@Service
public class StationBaseTypeServiceImpl extends ServiceImpl<StationBaseTypeMapper, StationBaseType> implements IStationBaseTypeService {

    @Autowired
    private IStandardTypeService standardTypeService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public int deleteByStandardId(int standardId) {
        return baseMapper.delete(new QueryWrapper<StationBaseType>().eq("StandardId", standardId));
    }

    @Override
    public List<IdValueDTO<Integer, String>> findCurrentStationBaseType() {
        SysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDARD_VER);
        if (Objects.isNull(sysConfig) || CharSequenceUtil.isBlank(sysConfig.getConfigValue())) {
            //没有设置标准类型，返回空列表
            return list().stream()
                    .map(item -> new IdValueDTO<>(item.getId(), item.getType()))
                    .collect(Collectors.toList());
        }
        StandardType standardType = standardTypeService.findByStandardAlias(sysConfig.getConfigValue());
        if (Objects.isNull(standardType) || Objects.isNull(standardType.getStandardId())) {
            //没有设置标准类型，返回空列表
            return list().stream()
                    .map(item -> new IdValueDTO<>(item.getId(), item.getType()))
                    .collect(Collectors.toList());
        }

        // 根据标准ID查找对应的站点基类型
        return list(Wrappers.lambdaQuery(StationBaseType.class)
                .eq(StationBaseType::getStandardId, standardType.getStandardId()))
                .stream()
                .map(item -> new IdValueDTO<>(item.getId(), item.getType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<BaseStationMapTypeDTO> getBaseStationMapType(Integer stationBaseTypeId) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        return baseMapper.getBaseStationMapType(stationBaseTypeId, currentStandardType.getValue());
    }
}
