package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.StationBaseType;
import com.siteweb.tcs.siteweb.mapper.StationBaseTypeMapper;
import com.siteweb.tcs.siteweb.service.IStationBaseTypeService;
import org.springframework.stereotype.Service;

/**
 * Station Base Type Service Implementation
 */
@Service
public class StationBaseTypeServiceImpl extends ServiceImpl<StationBaseTypeMapper, StationBaseType> implements IStationBaseTypeService {


    @Override
    public int deleteByStandardId(int standardId) {
        return baseMapper.delete(new QueryWrapper<StationBaseType>().eq("StandardId", standardId));
    }
}
