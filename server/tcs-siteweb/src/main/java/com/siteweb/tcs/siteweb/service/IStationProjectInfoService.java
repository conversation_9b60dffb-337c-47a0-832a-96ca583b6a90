package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.StationProjectInfo;

/**
 * Station Project Info Service Interface
 */
public interface IStationProjectInfoService extends IService<StationProjectInfo> {

    /**
     * 查找或创建站点项目信息
     *
     * @param stationId 站点ID
     * @param projectName 项目名称
     * @param contractNo 合同编号
     * @return 站点项目信息
     */
    StationProjectInfo findOrCreateStationProjectInfo(Integer stationId, String projectName, String contractNo);
}
