package com.siteweb.tcs.siteweb.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.siteweb.tcs.siteweb.util.RestfulView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult {
    /**
     * 执行结果
     */
    @JsonView(RestfulView.Default.class)
    private int code;
    /**
     * 错误消息
     */
    @JsonProperty("msg")
    @JsonView(RestfulView.Default.class)
    private String message;
    /**
     * 執行時間
     */
    @JsonView(RestfulView.Default.class)
    private long timestamp;

    /**
     * 返回内容
     */
    @JsonView(RestfulView.Default.class)
    private Object data;
}
