package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.siteweb.dto.OperationDetailDTO;
import com.siteweb.tcs.siteweb.entity.OperationDetail;
import com.siteweb.tcs.siteweb.vo.OperationDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Operation Detail Mapper
 */
@Mapper
@Repository
public interface OperationDetailMapper extends BaseMapper<OperationDetail> {
    
    /**
     * 批量插入操作详情记录
     *
     * @param list 操作详情列表
     * @return 插入记录数
     */
    int insertBatchSomeColumn(List<OperationDetail> list);
    
    /**
     * 分页查询操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findPage(@Param("page") Page<OperationDetail> page, @Param("operationDetailDTO") OperationDetailDTO operationDetailDTO);

    /**
     * 查询设备模板相关的操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentTemplateLogPage(@Param("page") Page<OperationDetail> page, @Param("operationDetailDTO") OperationDetailDTO operationDetailDTO);

    /**
     * 查询设备相关的操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @param equipmentTemplateId 设备模板ID
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentLogPage(@Param("page") Page<OperationDetail> page, @Param("operationDetailDTO") OperationDetailDTO operationDetailDTO, @Param("equipmentTemplateId") Integer equipmentTemplateId);
}
