package com.siteweb.tcs.siteweb.service.impl;

import com.siteweb.tcs.siteweb.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.IntFunction;

@Service
public class BaseDicServiceImpl implements IBaseDicService {
    
    @Autowired
    private ISignalService signalService;
    
    @Autowired
    private IEventService eventService;
    
    @Autowired
    private IControlService controlService;
    
    @Autowired
    private ISignalBaseDicService signalBaseDicService;
    
    @Autowired
    private IEventBaseDicService eventBaseDicService;
    
    @Autowired
    private ICommandBaseDicService commandBaseDicService;

    @Override
    public void updateBaseClassStandardDictionary(Integer equipmentTemplateId) {
        // 先调用存储过程PCT_GenerateBaseDic，按照协议库中包含的基类ID补充基类标准化字典
        processBaseTypeIdList(signalService::findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate, signalBaseDicService, equipmentTemplateId);
        processBaseTypeIdList(eventService::findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate, eventBaseDicService, equipmentTemplateId);
        processBaseTypeIdList(controlService::findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate, commandBaseDicService, equipmentTemplateId);
    }

    @Override
    public boolean existsByBaseTypeId(Long baseTypeId) {
        return false; // 默认实现，子类应该重写
    }

    @Override
    public void generateBaseDic(Long baseTypeId, Long sourceId) {
        // 默认实现，子类应该重写
    }

    @Override
    public void processBaseTypeIdList(IntFunction<List<Long>> findBaseTypeIdsFunction, IBaseDicService baseDicService, Integer equipmentTemplateId) {
        List<Long> baseTypeIdList = findBaseTypeIdsFunction.apply(equipmentTemplateId);
        for (Long baseTypeId : baseTypeIdList) {
            Long sourceId = ((baseTypeId / 1000L) * 1000L) + 1L;
            boolean existsForSourceId = baseDicService.existsByBaseTypeId(sourceId);
            boolean existsByBaseTypeId = baseDicService.existsByBaseTypeId(baseTypeId);
            if (existsForSourceId && !existsByBaseTypeId) {
                baseDicService.generateBaseDic(baseTypeId, sourceId);
            }
        }
    }
} 