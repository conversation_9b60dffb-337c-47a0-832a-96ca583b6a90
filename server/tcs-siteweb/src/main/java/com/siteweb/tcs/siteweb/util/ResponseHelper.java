package com.siteweb.tcs.siteweb.util;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * Response Helper
 */
public class ResponseHelper {

    /**
     * 创建成功响应
     * @return 响应实体
     */
    public static ResponseEntity<ResponseResult> successful() {
        return ResponseEntity.ok(ResponseResult.success());
    }

    /**
     * 创建成功响应
     * @param data 响应数据
     * @return 响应实体
     */
    public static ResponseEntity<ResponseResult> successful(Object data) {
        return ResponseEntity.ok(ResponseResult.success(data));
    }

    /**
     * 创建失败响应
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<ResponseResult> failed(String message) {
        return ResponseEntity.status(HttpStatus.OK).body(ResponseResult.fail(message));
    }

    /**
     * 创建失败响应
     * @param code 错误代码
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<ResponseResult> failed(int code, String message) {
        return ResponseEntity.status(HttpStatus.OK).body(ResponseResult.fail(code, message));
    }
} 