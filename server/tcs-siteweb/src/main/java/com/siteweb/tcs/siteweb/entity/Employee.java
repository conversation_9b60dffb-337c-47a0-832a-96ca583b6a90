package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Employee entity
 */
@Data
@TableName("tbl_employee")
public class Employee implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "EmployeeId")
    private Integer employeeId;

    @TableField("DepartmentId")
    private Integer departmentId;

    @TableField("EmployeeName")
    private String employeeName;

    @TableField("EmployeeType")
    private Integer employeeType;

    @TableField("EmployeeTitle")
    private Integer employeeTitle;

    @TableField("JobNumber")
    private String jobNumber;

    @TableField("Gender")
    private Integer gender;

    @TableField("Mobile")
    private String mobile;

    @TableField("Phone")
    private String phone;

    @TableField("Email")
    private String email;

    @TableField("Address")
    private String address;

    @TableField("PostAddress")
    private String postAddress;

    @TableField("Enable")
    private Boolean enable;

    @TableField("Description")
    private String description;

    @TableField("IsAddTempUser")
    private Boolean isAddTempUser;

    @TableField("UserValidTime")
    private Integer userValidTime;
}
