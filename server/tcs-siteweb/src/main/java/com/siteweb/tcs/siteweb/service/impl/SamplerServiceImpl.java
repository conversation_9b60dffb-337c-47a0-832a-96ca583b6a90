package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.SamplerMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.ISamplerService;
import com.siteweb.tcs.siteweb.util.FileNameUtil;
import com.siteweb.tcs.siteweb.util.FileSuffixUtil;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.util.TokenUserSiteWebUtil;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Sampler Service Implementation
 */
@Slf4j
@Service
public class SamplerServiceImpl extends ServiceImpl<SamplerMapper, Sampler> implements ISamplerService {

    @Autowired
    private SamplerMapper samplerMapper;

    @Autowired
    private I18n i18n;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    IChangeEventService changeEventService;

    @Autowired
    IOperationDetailService operationDetailService;

    @Override
    public Sampler getSamplerByProtocolCode(String protocolCode) {
        if (!StringUtils.hasText(protocolCode)) {
            log.warn("Protocol code is empty");
            return null;
        }

        try {
            // Use the custom mapper method for better performance and readability
            Sampler sampler = samplerMapper.findByProtocolCode(protocolCode);

            if (sampler == null) {
                log.warn("No sampler found for protocol code: {}", protocolCode);
            } else {
                log.debug("Found sampler with ID {} for protocol code: {}", sampler.getSamplerId(), protocolCode);
            }

            return sampler;
        } catch (Exception e) {
            log.error("Error finding sampler by protocol code: {}", protocolCode, e);

            // Fallback to using LambdaQueryWrapper if custom method fails
            LambdaQueryWrapper<Sampler> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Sampler::getProtocolCode, protocolCode);

            return getOne(queryWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertLianTongSampler() {
        try {
            List<Sampler> samplers = new ArrayList<>();

            // 创建联通采样器
            samplers.add(createSampler(1, "CUCC_MODBUS", "CUCC_MODBUS", "CUCC_MODBUS", "CUCC_MODBUS", 1));
            samplers.add(createSampler(2, "CUCC_SNMP", "CUCC_SNMP", "CUCC_SNMP", "CUCC_SNMP", 2));
            samplers.add(createSampler(3, "CUCC_BACNET", "CUCC_BACNET", "CUCC_BACNET", "CUCC_BACNET", 3));
            samplers.add(createSampler(4, "CUCC_DAQM", "CUCC_DAQM", "CUCC_DAQM", "CUCC_DAQM", 4));
            samplers.add(createSampler(5, "CUCC_DCOM", "CUCC_DCOM", "CUCC_DCOM", "CUCC_DCOM", 5));
            samplers.add(createSampler(6, "CUCC_DOOR", "CUCC_DOOR", "CUCC_DOOR", "CUCC_DOOR", 6));
            samplers.add(createSampler(7, "CUCC_FACE", "CUCC_FACE", "CUCC_FACE", "CUCC_FACE", 7));
            samplers.add(createSampler(8, "CUCC_FINGER", "CUCC_FINGER", "CUCC_FINGER", "CUCC_FINGER", 8));
            samplers.add(createSampler(9, "CUCC_HUAWEI", "CUCC_HUAWEI", "CUCC_HUAWEI", "CUCC_HUAWEI", 9));
            samplers.add(createSampler(10, "CUCC_HUAWEI_UPS", "CUCC_HUAWEI_UPS", "CUCC_HUAWEI_UPS", "CUCC_HUAWEI_UPS", 10));
            samplers.add(createSampler(11, "CUCC_HUAWEI_AIRCON", "CUCC_HUAWEI_AIRCON", "CUCC_HUAWEI_AIRCON", "CUCC_HUAWEI_AIRCON", 11));
            samplers.add(createSampler(12, "CUCC_HUAWEI_POWER", "CUCC_HUAWEI_POWER", "CUCC_HUAWEI_POWER", "CUCC_HUAWEI_POWER", 12));
            samplers.add(createSampler(13, "CUCC_HUAWEI_BATTERY", "CUCC_HUAWEI_BATTERY", "CUCC_HUAWEI_BATTERY", "CUCC_HUAWEI_BATTERY", 13));
            samplers.add(createSampler(14, "CUCC_HUAWEI_DIESEL", "CUCC_HUAWEI_DIESEL", "CUCC_HUAWEI_DIESEL", "CUCC_HUAWEI_DIESEL", 14));
            samplers.add(createSampler(15, "CUCC_HUAWEI_DISTRIBUTION", "CUCC_HUAWEI_DISTRIBUTION", "CUCC_HUAWEI_DISTRIBUTION", "CUCC_HUAWEI_DISTRIBUTION", 15));
            samplers.add(createSampler(16, "CUCC_HUAWEI_ENVIRONMENT", "CUCC_HUAWEI_ENVIRONMENT", "CUCC_HUAWEI_ENVIRONMENT", "CUCC_HUAWEI_ENVIRONMENT", 16));
            samplers.add(createSampler(17, "CUCC_HUAWEI_TRANSFORMER", "CUCC_HUAWEI_TRANSFORMER", "CUCC_HUAWEI_TRANSFORMER", "CUCC_HUAWEI_TRANSFORMER", 17));
            samplers.add(createSampler(18, "CUCC_HUAWEI_SWITCH", "CUCC_HUAWEI_SWITCH", "CUCC_HUAWEI_SWITCH", "CUCC_HUAWEI_SWITCH", 18));
            samplers.add(createSampler(19, "CUCC_HUAWEI_RECTIFIER", "CUCC_HUAWEI_RECTIFIER", "CUCC_HUAWEI_RECTIFIER", "CUCC_HUAWEI_RECTIFIER", 19));
            samplers.add(createSampler(20, "CUCC_HUAWEI_INVERTER", "CUCC_HUAWEI_INVERTER", "CUCC_HUAWEI_INVERTER", "CUCC_HUAWEI_INVERTER", 20));

            // 批量保存
            return saveBatch(samplers);
        } catch (Exception e) {
            log.error("Failed to batch save LianTong samplers", e);
            return false;
        }
    }

    @Override
    public Sampler findBySamplerType(int samplerType) {
        return samplerMapper.selectOne(new QueryWrapper<Sampler>().eq("SamplerType", samplerType));
    }

    @Override
    public Sampler findByNameAndDllPath(String samplerName, String dllPath) {
        if (!StringUtils.hasText(samplerName) || !StringUtils.hasText(dllPath)) {
            log.warn("Sampler name or dll path is empty");
            return null;
        }

        try {
            LambdaQueryWrapper<Sampler> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Sampler::getSamplerName, samplerName)
                       .eq(Sampler::getDllPath, dllPath);

            Sampler sampler = getOne(queryWrapper);

            if (sampler == null) {
                log.warn("No sampler found for name: {} and dll path: {}", samplerName, dllPath);
            } else {
                log.debug("Found sampler with ID {} for name: {} and dll path: {}", sampler.getSamplerId(), samplerName, dllPath);
            }

            return sampler;
        } catch (Exception e) {
            log.error("Error finding sampler by name: {} and dll path: {}", samplerName, dllPath, e);
            return null;
        }
    }

    /**
     * 创建采样器对象
     *
     * @param samplerId 采样器ID
     * @param samplerName 采样器名称
     * @param samplerNameEn 采样器英文名称
     * @param protocolCode 协议码
     * @param dllPath DLL路径
     * @param samplerType 采样器类型
     * @return 采样器对象
     */
    private Sampler createSampler(Integer samplerId, String samplerName, String samplerNameEn, String protocolCode, String dllPath, Integer samplerType) {
        Sampler sampler = new Sampler();
        sampler.setSamplerId(samplerId);
        sampler.setSamplerName(samplerName);
        sampler.setProtocolCode(protocolCode);
        sampler.setDllPath(dllPath);
        sampler.setSamplerType(samplerType.shortValue());
        return sampler;
    }

    @Override
    public void createSamplersFromXml(EquipmentTemplate EquipmentTemplate, Element samplersElementList) {
        if (Objects.isNull(EquipmentTemplate) || Objects.isNull(samplersElementList)) {
            return;
        }

        String muHOSTEquipment = i18n.T("equipment.MUHOST.equipment");
        // 如果是RMU-MUHOST设备则无需生成Sampler
        if (Objects.equals(EquipmentTemplate.getEquipmentTemplateName(), muHOSTEquipment)) {
            return;
        }

        List<Element> samplerElementList = samplersElementList.elements("Sampler");
        if (CollUtil.isEmpty(samplerElementList)) {
            log.warn("没有采集器存在{}", EquipmentTemplate);
            return;
        }

        for (Element samplerElement : samplerElementList) {
            Sampler sampler = parseSamplerXml(samplerElement);
            // 发现协议组发到现场的xml中协议编码和设备模板的协议编码不一致，导致网页版配置工具列表找不到sampler
            // 这边统一以设备模板的协议编码为准
            sampler.setProtocolCode(EquipmentTemplate.getProtocolCode());
            create(sampler);
        }
    }

    private Sampler parseSamplerXml(Element samplerElement) {
        String dllPath = samplerElement.attributeValue("DllPath");
        return Sampler.builder()
                .samplerName(samplerElement.attributeValue("SamplerName"))
                .samplerType(Short.valueOf(samplerElement.attributeValue("SamplerType")))
                .protocolCode(samplerElement.attributeValue("ProtocolCode"))
                .dllCode(samplerElement.attributeValue("DLLCode"))
                .dllVersion(samplerElement.attributeValue("DLLVersion"))
                .protocolFilePath(samplerElement.attributeValue("ProtocolFilePath"))
                .dllFilePath(samplerElement.attributeValue("DLLFilePath"))
                .dllPath(FileNameUtil.toLowerCaseExtension(dllPath))
                .setting(samplerElement.attributeValue("Setting"))
                .description(samplerElement.attributeValue("Description"))
                .build();
    }

    @Override
    public void create(Sampler sampler) {
        if (isExists(sampler.getProtocolCode())) {
            log.warn("采集器protocolCode:{}，已经存在", sampler.getProtocolCode());
            return;
        }
        int samplerId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_SAMPLER, 0);
        sampler.setSamplerId(samplerId);
        // 如果dellcode和dellversion为null，则设置为空字符串
        if (Objects.isNull(sampler.getDllCode())) {
            sampler.setDllCode("");
        }
        if (Objects.isNull(sampler.getDllVersion())) {
            sampler.setDllVersion("");
        }
        if (Objects.isNull(sampler.getProtocolFilePath())) {
            sampler.setProtocolFilePath("");
        }
        if (Objects.isNull(sampler.getDllFilePath())) {
            sampler.setDllFilePath("");
        }
        if (Objects.isNull(sampler.getSoCode())) {
            sampler.setSoCode("");
        }
        if (Objects.isNull(sampler.getSoPath())) {
            sampler.setSoPath("");
        }
        //统一把 dllPath 后缀改成 .so
        sampler.setDllPath(FileSuffixUtil.changeToSo(sampler.getDllPath()));

        samplerMapper.insert(sampler);
        operationDetailService.recordOperationLog(TokenUserSiteWebUtil.getLoginUserId(), String.valueOf(sampler.getSamplerId()), OperationObjectTypeEnum.SAMPLER, i18n.T("monitor.sampler.name"), i18n.T("add"), "", sampler.getSamplerName());
        changeEventService.sendCreate(sampler);
    }

    private boolean isExists(String protocolCode) {
        return samplerMapper.exists(Wrappers.lambdaQuery(Sampler.class).eq(Sampler::getProtocolCode, protocolCode));
    }
}
