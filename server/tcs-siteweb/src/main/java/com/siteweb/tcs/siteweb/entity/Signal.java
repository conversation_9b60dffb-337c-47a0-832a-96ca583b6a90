package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Signal entity
 */
@Data
@TableName("tbl_signal")
public class Signal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("SignalId")
    private Integer signalId;

    @TableField("Enable")
    private Boolean enable;

    @TableField("Visible")
    private Boolean visible;

    @TableField("Description")
    private String description;

    @TableField("SignalName")
    private String signalName;

    @TableField("SignalCategory")
    private Integer signalCategory;

    @TableField("SignalType")
    private Integer signalType;

    @TableField("ChannelNo")
    private Integer channelNo;

    @TableField("ChannelType")
    private Integer channelType;

    @TableField("Expression")
    private String expression;

    @TableField("DataType")
    private Integer dataType;

    @TableField("ShowPrecision")
    private String showPrecision;

    @TableField("Unit")
    private String unit;

    @TableField("StoreInterval")
    private Double storeInterval;

    @TableField("AbsValueThreshold")
    private Double absValueThreshold;

    @TableField("PercentThreshold")
    private Double percentThreshold;

    @TableField("StaticsPeriod")
    private Integer staticsPeriod;

    @TableField("BaseTypeId")
    private Long baseTypeId;

    @TableField("ChargeStoreInterVal")
    private Double chargeStoreInterVal;

    @TableField("ChargeAbsValue")
    private Double chargeAbsValue;

    @TableField("DisplayIndex")
    private Integer displayIndex;

    @TableField("MDBSignalId")
    private Integer mdbSignalId;

    @TableField("ModuleNo")
    private Integer moduleNo;
}
