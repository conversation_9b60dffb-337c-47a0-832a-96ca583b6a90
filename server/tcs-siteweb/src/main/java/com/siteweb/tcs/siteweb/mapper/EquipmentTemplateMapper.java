package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Equipment Template Mapper
 */
@Mapper
@Repository
public interface EquipmentTemplateMapper extends BaseMapper<EquipmentTemplate> {

    /**
     * 根据名称模糊查询设备模板
     * @param equipmentTemplateName 设备模板名称
     * @return 设备模板列表
     */
    List<EquipmentTemplate> findByNameLike(@Param("equipmentTemplateName") String equipmentTemplateName);

    /**
     * 查询B接口设备根模板ID
     * B接口设备模板通常具有特定的protocolCode，例如 "BInterfaceProtocol"
     * 并且是顶级模板（ParentTemplateId 为 null 或 0，取决于数据库设计）
     * @return B接口设备根模板ID
     */
    Integer getBInterfaceDeviceTemplateRootId();

    /**
     * 查找指定父模板ID的子模板数量
     * @param parentTemplateId 父模板ID
     * @return 子模板数量
     */
    int countByParentTemplateId(@Param("parentTemplateId") Integer parentTemplateId);

    /**
     * 根据动态条件查询设备模板
     * @param equipmentTemplateVO 查询条件
     * @return 设备模板列表
     */
    List<EquipmentTemplate> queryTemplateByVO(@Param("vo") EquipmentTemplateVO equipmentTemplateVO);

    /**
     * 更新设备模板的基础类型为空
     * @return 更新的记录数
     */
    int updateEquipmentBaseTypeToNull();

    /**
     * 根据设备模板分类查询设备模板ID
     * @param equipmentTemplateIdDiv 设备模板分类
     * @return 设备模板ID列表
     */
    List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(@Param("equipmentTemplateIdDiv") Integer equipmentTemplateIdDiv);
}
