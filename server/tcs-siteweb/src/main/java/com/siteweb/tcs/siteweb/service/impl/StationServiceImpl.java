package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.entity.Station;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.StationMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IHouseService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.IStationProjectInfoService;
import com.siteweb.tcs.siteweb.service.IStationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Station Service Implementation
 */
@Slf4j
@Service
public class StationServiceImpl extends ServiceImpl<StationMapper, Station> implements IStationService {

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IStationProjectInfoService stationProjectInfoService;

    @Autowired
    private IHouseService houseService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private StationMapper stationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(Station station) {
        if (station == null) {
            log.warn("Cannot create station: station is null");
            return false;
        }

        try {
            if (station.getStationId() == null || station.getStationId().equals(0)) {
                Integer stationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION, 0);
                station.setStationId(stationId);
            }
            boolean status = stationMapper.insert(station) > 0;
            if (status) {
                // 创建局站项目信息
                stationProjectInfoService.findOrCreateStationProjectInfo(station.getStationId(), station.getProjectName(), station.getContractNo());
                createDefaultHouse(station);
                changeEventService.sendCreate(station);
            }
            return status;
        } catch (Exception e) {
            log.error("Failed to create station: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建默认房屋
     *
     * @param station 站点
     */
    private void createDefaultHouse(Station station) {
        if (station.getStationTemplateId() == null || station.getStationTemplateId() == 0) { // 空局站
            // 创建默认局房
            House house = new House();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName("默认房屋"); // 对应 i18n.T("house.default.name")
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.save(house);
        } else if (station.getStationTemplateId() == 1) { // 空局站
            House house = new House();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName("默认房屋"); // 对应 i18n.T("house.default.name")
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.save(house);

            House baseStationHouse = new House();
            Integer baseStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            baseStationHouse.setStationId(station.getStationId());
            baseStationHouse.setHouseId(baseStationHouseId);
            baseStationHouse.setHouseName("基站房屋"); // 对应 i18n.T("baseStation.house.name")
            baseStationHouse.setDescription(house.getHouseName());
            baseStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.save(baseStationHouse);
        } else if (station.getStationTemplateId() == 2) { // 江苏基站
            // 默认具有"基站"局房
            House house = new House();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName("默认房屋"); // 对应 i18n.T("house.default.name")
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.save(house);

            House baseStationHouse = new House();
            Integer baseStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            baseStationHouse.setStationId(station.getStationId());
            baseStationHouse.setHouseId(baseStationHouseId);
            baseStationHouse.setHouseName("基站房屋"); // 对应 i18n.T("baseStation.house.name")
            baseStationHouse.setDescription(house.getHouseName());
            baseStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.save(baseStationHouse);

            House importantStationHouse = new House();
            Integer importantStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            importantStationHouse.setStationId(station.getStationId());
            importantStationHouse.setHouseId(importantStationHouseId);
            importantStationHouse.setHouseName("重要信号房屋"); // 对应 i18n.T("important.signal.house.name")
            importantStationHouse.setDescription(house.getHouseName());
            importantStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.save(importantStationHouse);
        }
    }

    @Override
    public Station findByStationId(int stationId) {
        return stationMapper.findStationById(stationId);
    }

}
