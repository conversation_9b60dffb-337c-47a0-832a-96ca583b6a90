<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.EventBaseDicMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.EventBaseDic">
        <id column="BaseTypeId" property="baseTypeId" />
        <result column="BaseTypeName" property="baseTypeName" />
        <result column="BaseEquipmentId" property="baseEquipmentId" />
        <result column="EnglishName" property="englishName" />
        <result column="EventSeverityId" property="eventSeverityId" />
        <result column="ComparedValue" property="comparedValue" />
        <result column="BaseLogicCategoryId" property="baseLogicCategoryId" />
        <result column="StartDelay" property="startDelay" />
        <result column="EndDelay" property="endDelay" />
        <result column="ExtendField1" property="extendField1" />
        <result column="ExtendField2" property="extendField2" />
        <result column="ExtendField3" property="extendField3" />
        <result column="ExtendField4" property="extendField4" />
        <result column="ExtendField5" property="extendField5" />
        <result column="Description" property="description" />
        <result column="BaseNameExt" property="baseNameExt" />
        <result column="IsSystem" property="isSystem" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem
    </sql>

    <!-- 生成事件基类字典 -->
    <insert id="generateEventBaseDic">
        INSERT INTO TBL_EventBaseDic
        (BaseTypeId,
         BaseTypeName,
         BaseEquipmentId,
         EnglishName,
         EventSeverityId,
         ComparedValue,
         BaseLogicCategoryId,
         StartDelay,
         EndDelay,
         ExtendField1,
         ExtendField2,
         ExtendField3,
         ExtendField4,
         ExtendField5,
         Description,
         BaseNameExt,
         IsSystem)
        SELECT #{baseTypeId},
               eventBaseDic.BaseTypeName,
               eventBaseDic.BaseEquipmentId,
               eventBaseDic.EnglishName,
               eventBaseDic.EventSeverityId,
               eventBaseDic.ComparedValue,
               eventBaseDic.BaseLogicCategoryId,
               eventBaseDic.StartDelay,
               eventBaseDic.EndDelay,
               eventBaseDic.ExtendField1,
               eventBaseDic.ExtendField2,
               eventBaseDic.ExtendField3,
               eventBaseDic.ExtendField4,
               eventBaseDic.ExtendField5,
               eventBaseDic.Description,
               eventBaseDic.BaseNameExt,
               0
        FROM TBL_EventBaseDic eventBaseDic
        WHERE eventBaseDic.BaseTypeId = #{sourceId}
    </insert>
</mapper>
