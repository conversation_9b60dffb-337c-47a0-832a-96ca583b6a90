<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.StationBaseTypeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.StationBaseType">
        <id column="Id" property="id" />
        <result column="Type" property="type" />
        <result column="StandardId" property="standardId" />
    </resultMap>

    <!-- 基站映射类型结果映射 -->
    <resultMap id="BaseStationMapTypeResultMap" type="com.siteweb.tcs.siteweb.dto.BaseStationMapTypeDTO">
        <result column="checked" property="checked" />
        <result column="itemId" property="itemId" />
        <result column="itemValue" property="itemValue" />
        <result column="counts" property="counts" />
        <result column="id" property="id" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        Id, Type, StandardId
    </sql>

    <!-- 获取基站映射类型 -->
    <select id="getBaseStationMapType" resultMap="BaseStationMapTypeResultMap">
        SELECT
            CASE WHEN sbm.StationCategory IS NOT NULL THEN 1 ELSE 0 END AS checked,
            sc.StationCategoryId AS itemId,
            sc.StationCategoryName AS itemValue,
            (
                SELECT COUNT(*)
                FROM tbl_station s
                WHERE s.StationCategoryId = sc.StationCategoryId
            ) AS counts,
            sbt.Id AS id,
            sbt.Type AS type
        FROM tbl_stationcategory sc
        LEFT JOIN tbl_stationbasemap sbm ON sc.StationCategoryId = sbm.StationCategory
            AND sbm.StationBaseType = #{stationBaseTypeId}
            AND sbm.StandardType = #{standardType}
        LEFT JOIN tbl_stationbasetype sbt ON sbt.Id = #{stationBaseTypeId}
        ORDER BY sc.StationCategoryId
    </select>
</mapper>
