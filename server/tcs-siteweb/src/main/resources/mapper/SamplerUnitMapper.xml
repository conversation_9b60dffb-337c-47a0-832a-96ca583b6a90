<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.SamplerUnitMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.SamplerUnit">
        <id column="Id" property="id" />
        <result column="SamplerUnitId" property="samplerUnitId" />
        <result column="PortId" property="portId" />
        <result column="MonitorUnitId" property="monitorUnitId" />
        <result column="SamplerId" property="samplerId" />
        <result column="ParentSamplerUnitId" property="parentSamplerUnitId" />
        <result column="SamplerType" property="samplerType" />
        <result column="SamplerUnitName" property="samplerUnitName" />
        <result column="Address" property="address" />
        <result column="SpUnitInterval" property="spUnitInterval" />
        <result column="DllPath" property="dllPath" />
        <result column="ConnectState" property="connectState" />
        <result column="UpdateTime" property="updateTime" />
        <result column="PhoneNumber" property="phoneNumber" />
        <result column="Description" property="description" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        Id, SamplerUnitId, PortId, MonitorUnitId, SamplerId, ParentSamplerUnitId, 
        SamplerType, SamplerUnitName, Address, SpUnitInterval, DllPath, ConnectState, 
        UpdateTime, PhoneNumber, Description
    </sql>
    
    <!-- 根据采集器ID列表查询关联的采集器名称 -->
    <select id="findReferenceSamplerNameBySamplerIdList" resultType="java.lang.String">
        SELECT b.SamplerName 
        FROM tsl_samplerunit a 
        INNER JOIN tsl_sampler b ON a.SamplerId = b.SamplerId
        WHERE b.SamplerId IN
        <foreach collection="samplerIdList" item="samplerId" open="(" close=")" separator=",">
            #{samplerId}
        </foreach>
    </select>
    
    <!-- 查询带有端口信息的采集单元 -->
    <select id="selectSamplerUnitWithPort" resultMap="BaseResultMap">
        SELECT a.* 
        FROM tsl_samplerunit a, tsl_port b 
        WHERE a.MonitorUnitId = #{monitorUnitId} 
        AND a.PortId = b.PortId 
        AND a.MonitorUnitId = b.MonitorUnitId
        ORDER BY b.PortNo, a.Address
    </select>
    
    <!-- 根据监控单元ID、采集单元名称和端口名称查询采集单元 -->
    <select id="findSamplerUnit" resultMap="BaseResultMap">
        SELECT samplerUnit.* 
        FROM tsl_monitorunit monitorUnit
        INNER JOIN tsl_samplerunit samplerUnit ON monitorUnit.MonitorUnitId = samplerUnit.MonitorUnitId
        INNER JOIN tsl_port port ON port.PortId = samplerUnit.PortId
        WHERE monitorUnit.MonitorUnitId = #{monitorUnitId}
        AND samplerUnit.SamplerUnitName = #{samplerUnitName}
        AND port.PortName = #{portName}
    </select>
</mapper>
