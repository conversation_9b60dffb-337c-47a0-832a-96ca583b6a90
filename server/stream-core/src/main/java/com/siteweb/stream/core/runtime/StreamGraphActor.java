package com.siteweb.stream.core.runtime;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.stream.common.enums.RunMode;
import com.siteweb.stream.common.messages.BusinessMessage;
import com.siteweb.stream.common.messages.ShapeRouteMessage;
import com.siteweb.stream.common.runtime.events.*;
import com.siteweb.stream.common.stream.*;
import com.siteweb.stream.common.util.Parameters;
import com.siteweb.stream.core.entity.StreamFlow;
import com.siteweb.stream.core.entity.StreamGraph;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: StreamGraphActor
 * @descriptions: actor模式下流模型-图实体类
 * @author: xsx
 * @date: 2/14/2025 10:51 AM
 **/
public class StreamGraphActor extends AbstractActor {

    private final Map<Long, StreamFlowInstance> streamFlowInstanceMap = new HashMap<>();

    private GraphRuntimeContext graphRuntimeContext;

    private List<ActorRef> entryMessageSubscribers = new ArrayList<>();

    private StreamGraphActor(StreamGraph streamGraph, GraphRuntimeContext graphRuntimeContext) {
        this.graphRuntimeContext = graphRuntimeContext;
        /**
         * 1.解析图的parameter
         * 2.创建对应的流，添加到streamFlowInstanceMap中
         */
        List<StreamFlow> streamFlowInfoList = streamGraph.getFlows();
        if (!CollectionUtils.isEmpty(streamFlowInfoList)) {
            streamFlowInfoList.forEach(e -> {
                FlowRuntimeContext flowRuntimeContext = new FlowRuntimeContext();
                flowRuntimeContext.setGraphActor(self());
                flowRuntimeContext.setGraphRuntimeContext(graphRuntimeContext);
                ActorRef streamFlowActor = getContext().actorOf(Props.create(StreamFlowActor.class, e, flowRuntimeContext));
                StreamFlowInstance streamFlowInstance = new StreamFlowInstance();
                streamFlowInstance.setStreamFlowActorRef(streamFlowActor);
                streamFlowInstance.setStreamFlowOption(e.getStreamFlowOption());
                streamFlowInstance.setFlowRuntimeContext(flowRuntimeContext);
                streamFlowInstanceMap.put(e.getStreamFlowId(), streamFlowInstance);
                getContext().watchWith(streamFlowActor, new FlowInstanceDeadLetter(e.getStreamFlowId()));
            });
        }
    }

    @Override
    public void preStart() throws Exception, Exception {
        super.preStart();
    }

    @Override
    public void postStop() throws Exception, Exception {
        super.postStop();
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
//                .match(GraphLifeCycleEvent.class,this::handleLifeCycle)
                .match(FlowInstanceDeadLetter.class, this::handleFlowInstanceDead)
                .match(GraphStartEvent.class, this::handleGraphStart)
                .match(GraphTerminateEvent.class, this::handleGraphTerminate)
                .match(FlowStartEvent.class, this::handleFlowStart)
                .match(FlowTerminateEvent.class, this::handleFlowTerminate)
                .match(GraphOptionChangeEvent.class, this::handleGraphOptionChange)
                .match(BusinessMessage.class, this::handleBusinessMessage)
                .match(ShapeRouteMessage.class, this::handleShapeRouteMessage)
                .match(SubscribeRouteMessageEvent.class ,this::handleEntryMessageEvent)
                .build();
    }

    private void handleEntryMessageEvent(SubscribeRouteMessageEvent msg){
        if (msg.getSubscribe()){
            entryMessageSubscribers.add(getSender());
        }else{
            entryMessageSubscribers.remove(getSender());
        }
    }

    private void handleShapeRouteMessage(ShapeRouteMessage message) {
        for (var actor :entryMessageSubscribers){
            actor.tell(message.getPayload(), self());
        }
    }


    private void handleGraphStart(GraphStartEvent graphStartEvent) {
        //循环启动流
        if (!CollectionUtils.isEmpty(streamFlowInstanceMap)) {
            streamFlowInstanceMap.forEach((k, v) -> {
                FlowStartEvent flowStartEvent = new FlowStartEvent(graphStartEvent.getStreamGraphId(), k);
                v.start(flowStartEvent);
            });
        }
    }

    private void handleGraphTerminate(GraphTerminateEvent graphTerminateEvent) {
        //循环停止流
        if (!CollectionUtils.isEmpty(streamFlowInstanceMap)) {
            streamFlowInstanceMap.forEach((k, v) -> {
                FlowTerminateEvent flowTerminateEvent = new FlowTerminateEvent(graphTerminateEvent.getStreamGraphId(), k);
                v.stop(flowTerminateEvent);
            });
        }
    }


//    private void handleLifeCycle(GraphLifeCycleEvent graphLifecycleEvent){
//        /**
//         * 创建、启动、停止、销毁
//         */
//
//    }

    private void handleFlowInstanceDead(FlowInstanceDeadLetter flowInstanceDeadLetter) {
        /**
         * 处理节点死亡事件
         */
        long streamFlowId = flowInstanceDeadLetter.getStreamFlowId();

    }

    // 存起来
    private void handleGraphOptionChange(GraphOptionChangeEvent streamOptionChangeMessage) {
        StreamGraphOption streamGraphOption = streamOptionChangeMessage.getStreamGraphOption();
        //解析图参数
        parseStreamGraphOption(streamGraphOption);
        /**
         * 修改option
         */
        boolean hasShapeOption = false;
        Map<Long, Map<Long, StreamShapeOption>> streamShapeOptionMap = streamOptionChangeMessage.getStreamShapeOptionMap();
        if (CollectionUtil.isNotEmpty(streamShapeOptionMap)) {
            hasShapeOption = true;
        }
        Map<Long, StreamFlowOption> streamFlowOptionMap = streamOptionChangeMessage.getStreamFlowOptionMap();
        if (CollectionUtil.isNotEmpty(streamFlowOptionMap)) {
            for (Map.Entry<Long, StreamFlowOption> entry : streamFlowOptionMap.entrySet()) {
                FlowOptionChangeEvent streamFlowOptionChangeMessage = new FlowOptionChangeEvent();
                Long streamFlowId = entry.getKey();
                StreamFlowOption streamFlowOption = entry.getValue();
                streamFlowOptionChangeMessage.setStreamFlowId(streamFlowId);
                streamFlowOptionChangeMessage.setStreamFlowOption(streamFlowOption);
                if (hasShapeOption) {
                    streamFlowOptionChangeMessage.setStreamShapeOptionMap(streamShapeOptionMap.get(streamFlowId));
                }
                StreamFlowInstance streamFlowInstance = streamFlowInstanceMap.get(streamFlowId);
                streamFlowInstance.updateFlowInstanceOption(streamFlowOptionChangeMessage);
            }
        }
    }

    private void parseStreamGraphOption(StreamGraphOption streamGraphOption) {
        if (ObjectUtil.isNotEmpty(streamGraphOption)) {
            String runMode1 = streamGraphOption.getProperty("runMode").toString();
            RunMode runMode11 = RunMode.valueOf(runMode1);
            RunMode runMode = runMode11;
            graphRuntimeContext.setRunMode(runMode);
        }
    }

    private void handleFlowStart(FlowStartEvent flowStartEvent) {
        StreamFlowInstance streamFlowInstance = streamFlowInstanceMap.get(flowStartEvent.getStreamFlowId());
        //继续包装flowStartEvent
        streamFlowInstance.start(flowStartEvent);
    }

    private void handleFlowTerminate(FlowTerminateEvent flowTerminateEvent) {
        StreamFlowInstance streamFlowInstance = streamFlowInstanceMap.get(flowTerminateEvent.getStreamFlowId());
        streamFlowInstance.stop(flowTerminateEvent);
    }

    private void handleBusinessMessage(BusinessMessage businessMessage) {
        if (ObjectUtil.isNotEmpty(businessMessage.getStreamGraphId()) && graphRuntimeContext.getGraphId().longValue() != businessMessage.getStreamGraphId().longValue())
            return;
        if (ObjectUtil.isNotEmpty(businessMessage.getStreamFlowId())) {
            StreamFlowInstance streamFlowInstance = streamFlowInstanceMap.get(businessMessage.getStreamFlowId());
            if (ObjectUtil.isNotEmpty(streamFlowInstance)) {
                streamFlowInstance.notifyBusinessMessage(businessMessage, getContext());
            }
        } else {
            //广播
            streamFlowInstanceMap.entrySet().forEach(e -> {
                e.getValue().notifyBusinessMessage(businessMessage, getContext());
            });
        }
    }


}
