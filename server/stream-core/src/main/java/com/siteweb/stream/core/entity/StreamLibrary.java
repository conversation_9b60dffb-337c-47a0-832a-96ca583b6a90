package com.siteweb.stream.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-05-28)
 **/
@Data
@TableName("tcs_stream_library")
public class StreamLibrary {

    // 模块唯一ID
    @TableId(value = "library_id", type = IdType.AUTO)
    private String libraryId;

    // 模块名称
    @TableField(value = "library_name")
    private String libraryName;

    // 模块版本
    @TableField(value = "library_version")
    private String libraryVersion;

    // 模块版本
    @TableField(value = "library_package")
    private String libraryPackage;


    // 模块支持
    @TableField(value = "library_provider")
    private String libraryProvider;

    // 模块支持
    @TableField(value = "build_time")
    private String buildTime;

    // Jar包路径
    @TableField(value = "jar_file")
    private String jarFile;

    // Jar包code
    @TableField(value = "jar_code")
    private String jarCode;


    // 是否启用
    @TableField(value = "enable")
    private Boolean enable;


    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "updated_by")
    private String updatedBy;

}
