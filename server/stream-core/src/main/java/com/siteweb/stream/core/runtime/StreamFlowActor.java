package com.siteweb.stream.core.runtime;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.stream.common.messages.BusinessMessage;
import com.siteweb.stream.common.runtime.events.FlowOptionChangeEvent;
import com.siteweb.stream.common.runtime.events.FlowStartEvent;
import com.siteweb.stream.common.runtime.events.FlowTerminateEvent;
import com.siteweb.stream.common.runtime.events.NodeInstanceDeadLetter;
import com.siteweb.stream.common.stream.FlowRuntimeContext;
import com.siteweb.stream.common.stream.StreamFlowOption;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.core.entity.StreamFlow;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: StreamFlowActor
 * @descriptions: actor模式下的流模型-流实体类
 * @author: xsx
 * @date: 2/14/2025 10:25 AM
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class StreamFlowActor extends AbstractActor {
    // 源实例化节点，用于进行 start stop destroy
    private final StreamShapeInstance sourceShapeInstance;
    //每个节点的下一个outlet信息(一些ActorRef)，用于重建注入，目前还不知道具体列表里面类型
    //key -> shapeInstanceId
    //value -> 节点实例化对象
    private final Map<Long, StreamShapeInstance> streamShapeInstanceMap = new HashMap<>();

    private StreamFlowOption streamFlowOption;

    private FlowRuntimeContext flowRuntimeContext;


    /**
     * 构造函数
     *
     * @param streamFlowInfo 流实体配置详情
     */
    public StreamFlowActor(StreamFlow streamFlowInfo, FlowRuntimeContext flowRuntimeContext) throws Exception {
        // 构建图
        this.flowRuntimeContext = flowRuntimeContext;
        this.flowRuntimeContext.setStreamFlowId(streamFlowInfo.getStreamFlowId());
        sourceShapeInstance = RunnableGraphBuilder.getInstance().build(getContext(), streamFlowInfo, streamShapeInstanceMap,flowRuntimeContext);
    }

    //xsx 告诉图自己启动完毕 todo message换成真实的
    @Override
    public void preStart() throws Exception, Exception {
        super.preStart();
        getContext().parent().tell("start finish", getSelf());
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(NodeInstanceDeadLetter.class, this::handleShapeInstanceDead)
                .match(FlowStartEvent.class, this::startFlow)
                .match(FlowTerminateEvent.class, this::terminateFlow)
                .match(BusinessMessage.class,this::handleBusinessMessage)
                .match(FlowOptionChangeEvent.class, this::handleOptionChange).build();
    }

    //处理ShapeInstance遗言
    private void handleShapeInstanceDead(NodeInstanceDeadLetter shapeInstanceDeadLetter) {
        long streamNodeId = shapeInstanceDeadLetter.getStreamNodeId();

    }

    // todo 确定
    private void startFlow(FlowStartEvent flowStartEvent) {
        Long flowId = flowStartEvent.getStreamFlowId();
        sourceShapeInstance.tell(new FlowStartEvent(1L,1L), ActorRef.noSender());




    }

    private void terminateFlow(FlowTerminateEvent flowTerminateEvent) {
        Long flowId = flowTerminateEvent.getStreamFlowId();
        sourceShapeInstance.stop();
    }

    private void handleOptionChange(FlowOptionChangeEvent streamFlowOptionChangeMessage) {
        StreamFlowOption streamFlowOption = streamFlowOptionChangeMessage.getStreamFlowOption();
        this.streamFlowOption = streamFlowOption;
        Map<Long, StreamShapeOption> streamShapeOptionMap = streamFlowOptionChangeMessage.getStreamShapeOptionMap();
        if(CollectionUtil.isNotEmpty(streamShapeOptionMap)){
            for (Map.Entry<Long, StreamShapeOption> entry : streamShapeOptionMap.entrySet()) {
                Long streamNodeId = entry.getKey();
                StreamShapeOption streamShapeOption = entry.getValue();
                StreamShapeInstance streamShapeInstance = streamShapeInstanceMap.get(streamNodeId);
                if(ObjectUtil.isNotEmpty(streamShapeInstance)){
                    streamShapeInstance.options(streamShapeOption);
                }
            }
        }
    }

    private void handleBusinessMessage(BusinessMessage businessMessage){
        if(ObjectUtil.isNotEmpty(businessMessage.getStreamGraphId()) && ObjectUtil.isNotEmpty(businessMessage.getStreamFlowId())
        && flowRuntimeContext.getStreamFlowId().longValue() != businessMessage.getStreamFlowId().longValue())
            return;
        Long streamNodeId = businessMessage.getStreamNodeId();
        if(ObjectUtil.isNotEmpty(streamNodeId)){
            StreamShapeInstance streamShapeInstance = streamShapeInstanceMap.get(streamNodeId);
            if(ObjectUtil.isNotEmpty(streamShapeInstance)){
                streamShapeInstance.forward(businessMessage, getContext());
            }
        }else {
            streamShapeInstanceMap.entrySet().stream().forEach(e ->{
                e.getValue().forward(businessMessage,  getContext());
            });
        }
    }
}
