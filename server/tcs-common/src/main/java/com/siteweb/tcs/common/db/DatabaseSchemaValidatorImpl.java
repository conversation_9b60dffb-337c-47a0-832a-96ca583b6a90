package com.siteweb.tcs.common.db;

import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationInfo;
import org.flywaydb.core.api.MigrationInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据库结构校验服务
 * 提供数据库结构的完整性校验、异常检测和自动修复功能
 */
@Service
public class DatabaseSchemaValidatorImpl implements DatabaseSchemaValidator {
    @Override
    public boolean validate() {
        return validateDatabaseSchema();
    }

    private static final Logger logger = LoggerFactory.getLogger(DatabaseSchemaValidator.class);

    @Autowired
    private Flyway flyway;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DatabaseMigrationService migrationService;

    @Value("${tcs.database.validation.enabled:true}")
    private boolean validationEnabled;

    @Value("${tcs.database.validation.auto-repair:false}")
    private boolean autoRepairEnabled;

    @Value("${tcs.database.validation.read-only-on-failure:true}")
    private boolean readOnlyOnFailure;

    private volatile boolean schemaValid = false;
    private volatile boolean inReadOnlyMode = false;

    @PostConstruct
    public void init() {
        if (validationEnabled) {
            validateDatabaseSchema();
        }
    }

    /**
     * 验证数据库结构
     *
     * @return 验证结果
     */
    public boolean validateDatabaseSchema() {
        try {
            logger.info("开始验证数据库结构");

            // 1. 检查Flyway迁移历史表是否完整
            boolean historyTableValid = validateMigrationHistoryTable();
            if (!historyTableValid) {
                handleValidationFailure("迁移历史表验证失败");
                return false;
            }

            // 2. 检查当前数据库版本是否与预期一致
            boolean versionValid = validateDatabaseVersion();
            if (!versionValid) {
                handleValidationFailure("数据库版本验证失败");
                return false;
            }

            // 3. 检查表结构是否完整
            boolean tablesValid = validateTableStructures();
            if (!tablesValid) {
                handleValidationFailure("表结构验证失败");
                return false;
            }

            // 4. 检查索引和约束
            boolean constraintsValid = validateConstraints();
            if (!constraintsValid) {
                handleValidationFailure("索引和约束验证失败");
                return false;
            }

            // 所有检查通过
            schemaValid = true;
            inReadOnlyMode = false;
            logger.info("数据库结构验证通过");
            return true;

        } catch (Exception e) {
            logger.error("数据库结构验证过程中发生错误", e);
            handleValidationFailure("验证过程发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证迁移历史表
     */
    private boolean validateMigrationHistoryTable() {
        try {
            MigrationInfoService infoService = flyway.info();
            MigrationInfo[] migrations = infoService.all();

            // 检查是否存在未完成或失败的迁移
            for (MigrationInfo migration : migrations) {
                if (migration.getState().isFailed()) {
                    logger.error("发现失败的迁移: {}", migration.getScript());
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("验证迁移历史表时发生错误", e);
            return false;
        }
    }

    /**
     * 验证数据库版本
     */
    private boolean validateDatabaseVersion() {
        try {
            Map<String, Object> currentVersion = migrationService.getCurrentVersion();
            if ("未初始化".equals(currentVersion.get("version")) || 
                "未知".equals(currentVersion.get("version"))) {
                logger.error("数据库版本状态异常: {}", currentVersion.get("description"));
                return false;
            }
            return true;
        } catch (Exception e) {
            logger.error("验证数据库版本时发生错误", e);
            return false;
        }
    }

    /**
     * 验证表结构
     */
    private boolean validateTableStructures() {
        try {
            DatabaseMetaData metaData = dataSource.getConnection().getMetaData();
            Set<String> expectedTables = getExpectedTables();
            Set<String> actualTables = new HashSet<>();

            try (ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"})) {
                while (tables.next()) {
                    actualTables.add(tables.getString("TABLE_NAME").toLowerCase());
                }
            }

            // 检查是否缺少必要的表
            Set<String> missingTables = expectedTables.stream()
                    .filter(table -> !actualTables.contains(table.toLowerCase()))
                    .collect(Collectors.toSet());

            if (!missingTables.isEmpty()) {
                logger.error("缺少必要的数据库表: {}", missingTables);
                return false;
            }

            return true;
        } catch (SQLException e) {
            logger.error("验证表结构时发生错误", e);
            return false;
        }
    }

    /**
     * 验证索引和约束
     */
    private boolean validateConstraints() {
        try {
            DatabaseMetaData metaData = dataSource.getConnection().getMetaData();
            Set<String> tables = getExpectedTables();

            for (String table : tables) {
                // 验证主键
                try (ResultSet primaryKeys = metaData.getPrimaryKeys(null, null, table)) {
                    if (!primaryKeys.next()) {
                        logger.error("表 {} 缺少主键", table);
                        return false;
                    }
                }

                // 验证必要的索引
                Set<String> expectedIndexes = getExpectedIndexes(table);
                Set<String> actualIndexes = new HashSet<>();
                try (ResultSet indexes = metaData.getIndexInfo(null, null, table, false, false)) {
                    while (indexes.next()) {
                        actualIndexes.add(indexes.getString("INDEX_NAME").toLowerCase());
                    }
                }

                Set<String> missingIndexes = expectedIndexes.stream()
                        .filter(index -> !actualIndexes.contains(index.toLowerCase()))
                        .collect(Collectors.toSet());

                if (!missingIndexes.isEmpty()) {
                    logger.error("表 {} 缺少必要的索引: {}", table, missingIndexes);
                    return false;
                }
            }

            return true;
        } catch (SQLException e) {
            logger.error("验证索引和约束时发生错误", e);
            return false;
        }
    }

    /**
     * 处理验证失败情况
     */
    private void handleValidationFailure(String reason) {
        logger.error("数据库结构验证失败: {}", reason);
        schemaValid = false;

        if (autoRepairEnabled) {
            logger.info("尝试自动修复数据库结构");
            if (migrationService.repair()) {
                logger.info("数据库结构自动修复成功");
                validateDatabaseSchema(); // 重新验证
                return;
            }
        }

        if (readOnlyOnFailure) {
            logger.warn("系统将切换到只读模式");
            inReadOnlyMode = true;
        }
    }

    /**
     * 获取预期的数据库表列表
     */
    private Set<String> getExpectedTables() {
        // 这里需要根据实际项目情况配置必要的表
        return new HashSet<>(Arrays.asList(
            "tcs_flyway_schema_history",
            "tcs_user",
            "tcs_role",
            "tcs_permission"
            // 添加其他必要的表
        ));
    }

    /**
     * 获取指定表预期的索引列表
     */
    private Set<String> getExpectedIndexes(String table) {
        // 这里需要根据实际项目情况配置必要的索引
        Map<String, Set<String>> tableIndexes = new HashMap<>();
        tableIndexes.put("tcs_user", new HashSet<>(Arrays.asList(
            "idx_user_username",
            "idx_user_email"
        )));
        tableIndexes.put("tcs_role", new HashSet<>(Arrays.asList(
            "idx_role_name"
        )));

        return tableIndexes.getOrDefault(table, Collections.emptySet());
    }

    /**
     * 检查系统是否处于只读模式
     */
    public boolean isInReadOnlyMode() {
        return inReadOnlyMode;
    }

    /**
     * 检查数据库结构是否有效
     */
    public boolean isSchemaValid() {
        return schemaValid;
    }
}