package com.siteweb.tcs.common.o11y.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 探针操作响应消息
 * 用于Actor返回探针操作的结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProbeActionResponse {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 错误消息（如果操作失败）
     */
    private String errorMessage;
    
    /**
     * 返回的数据（如果操作成功）
     */
    private Object data;
    
    /**
     * 创建成功响应
     */
    public static ProbeActionResponse success(Object data) {
        return ProbeActionResponse.builder()
                .success(true)
                .data(data)
                .build();
    }
    
    /**
     * 创建失败响应
     */
    public static ProbeActionResponse failure(String errorMessage) {
        return ProbeActionResponse.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
}