package com.siteweb.tcs.common.runtime;

import org.apache.pekko.actor.ActorRef;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;

import static org.apache.pekko.pattern.Patterns.ask;

public class ActorUtil {

    public static Object askActorSync(ActorRef actor, Object message, long timeout) {
        CompletableFuture<Object> futureResult =
                ask(actor, message, Duration.ofMillis(timeout)).toCompletableFuture();

        try {
            return futureResult.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    public static Object askActorSync(ActorRef actor, Object message) {
        return askActorSync(actor, message, 1000);
    }

    public static CompletionStage<Object> askActorAsync(ActorRef actor, Object message) {
        return askActorAsync(actor, message, 1000);
    }

    public static CompletionStage<Object> askActorAsync(<PERSON><PERSON><PERSON> actor, Object message, long timeout) {
        return ask(actor, message, Duration.ofMillis(timeout));
    }
}

