package com.siteweb.tcs.common.exception.plugin;

import com.siteweb.tcs.common.exception.code.BusinessErrorCode;
import com.siteweb.tcs.common.exception.code.PluginErrorCode;
import com.siteweb.tcs.common.exception.code.StandardPluginErrorCode;
import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.PluginException;
import com.siteweb.tcs.common.runtime.ThingConnectPlugin;

/**
 * Factory for creating plugin exceptions.
 * This factory provides methods for creating various types of plugin exceptions.
 */
public class PluginExceptionFactory {

    /**
     * Create a plugin exception from a plugin error code
     *
     * @param errorCode The plugin error code
     * @param plugin    The plugin that generated the exception
     * @return A new PluginException
     */
    public static PluginException createPluginException(PluginErrorCode errorCode, ThingConnectPlugin plugin) {
        return errorCode.toException(plugin.getPluginId(), plugin.getPluginName());
    }

    /**
     * Create a plugin exception from a plugin error code with details
     *
     * @param errorCode The plugin error code
     * @param details   Additional error details
     * @param plugin    The plugin that generated the exception
     * @return A new PluginException
     */
    public static PluginException createPluginException(PluginErrorCode errorCode, Object details, ThingConnectPlugin plugin) {
        return errorCode.toException(details, plugin.getPluginId(), plugin.getPluginName());
    }

    /**
     * Create a plugin exception from a plugin error code with a cause
     *
     * @param errorCode The plugin error code
     * @param cause     The cause of the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginException
     */
    public static PluginException createPluginException(PluginErrorCode errorCode, Throwable cause, ThingConnectPlugin plugin) {
        return errorCode.toException(cause, plugin.getPluginId(), plugin.getPluginName());
    }

    /**
     * Create a plugin exception from a plugin error code with details and a cause
     *
     * @param errorCode The plugin error code
     * @param details   Additional error details
     * @param cause     The cause of the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginException
     */
    public static PluginException createPluginException(PluginErrorCode errorCode, Object details, Throwable cause, ThingConnectPlugin plugin) {
        return errorCode.toException(details, cause, plugin.getPluginId(), plugin.getPluginName());
    }

    /**
     * Create a plugin business exception from a business error code
     *
     * @param errorCode The business error code
     * @param plugin    The plugin that generated the exception
     * @return A new PluginBusinessException
     */
    public static PluginBusinessException createBusinessException(BusinessErrorCode errorCode, ThingConnectPlugin plugin) {
        return new PluginBusinessException(errorCode, plugin.getPluginId(), plugin.getPluginName());
    }

    /**
     * Create a plugin business exception from a business error code with details
     *
     * @param errorCode The business error code
     * @param details   Additional error details
     * @param plugin    The plugin that generated the exception
     * @return A new PluginBusinessException
     */
    public static PluginBusinessException createBusinessException(BusinessErrorCode errorCode, Object details, ThingConnectPlugin plugin) {
        return new PluginBusinessException(errorCode, details, plugin.getPluginId());
    }

    /**
     * Create a plugin business exception from a business error code with a cause
     *
     * @param errorCode The business error code
     * @param cause     The cause of the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginBusinessException
     */
    public static PluginBusinessException createBusinessException(BusinessErrorCode errorCode, Throwable cause, ThingConnectPlugin plugin) {
        return new PluginBusinessException(errorCode, cause, plugin.getPluginId());
    }

    /**
     * Create a plugin business exception from a business error code with details and a cause
     *
     * @param errorCode The business error code
     * @param details   Additional error details
     * @param cause     The cause of the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginBusinessException
     */
    public static PluginBusinessException createBusinessException(BusinessErrorCode errorCode, Object details, Throwable cause, ThingConnectPlugin plugin) {
        return new PluginBusinessException(errorCode, details, cause, plugin.getPluginId());
    }

    /**
     * Create a plugin technical exception from a technical error code
     *
     * @param errorCode The technical error code
     * @param plugin    The plugin that generated the exception
     * @return A new PluginTechnicalException
     */
    public static PluginTechnicalException createTechnicalException(TechnicalErrorCode errorCode, ThingConnectPlugin plugin) {
        return new PluginTechnicalException(errorCode, plugin.getPluginId(), plugin.getPluginName());
    }

    /**
     * Create a plugin technical exception from a technical error code with a component
     *
     * @param errorCode The technical error code
     * @param component The technical component that generated the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginTechnicalException
     */
    public static PluginTechnicalException createTechnicalException(TechnicalErrorCode errorCode, String component, ThingConnectPlugin plugin) {
        return new PluginTechnicalException(errorCode, component, plugin.getPluginId());
    }

    /**
     * Create a plugin technical exception from a technical error code with details
     *
     * @param errorCode The technical error code
     * @param details   Additional error details
     * @param plugin    The plugin that generated the exception
     * @return A new PluginTechnicalException
     */
    public static PluginTechnicalException createTechnicalException(TechnicalErrorCode errorCode, Object details, ThingConnectPlugin plugin) {
        return new PluginTechnicalException(errorCode, details, plugin.getPluginId());
    }

    /**
     * Create a plugin technical exception from a technical error code with a cause
     *
     * @param errorCode The technical error code
     * @param cause     The cause of the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginTechnicalException
     */
    public static PluginTechnicalException createTechnicalException(TechnicalErrorCode errorCode, Throwable cause, ThingConnectPlugin plugin) {
        return new PluginTechnicalException(errorCode, cause, plugin.getPluginId());
    }

    /**
     * Create a plugin technical exception from a technical error code with details and a cause
     *
     * @param errorCode The technical error code
     * @param details   Additional error details
     * @param cause     The cause of the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginTechnicalException
     */
    public static PluginTechnicalException createTechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause, ThingConnectPlugin plugin) {
        return new PluginTechnicalException(errorCode, details, cause, plugin.getPluginId());
    }

    /**
     * Create a plugin technical exception from a technical error code with details, cause, and component
     *
     * @param errorCode The technical error code
     * @param details   Additional error details
     * @param cause     The cause of the exception
     * @param component The technical component that generated the exception
     * @param plugin    The plugin that generated the exception
     * @return A new PluginTechnicalException
     */
    public static PluginTechnicalException createTechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause, String component, ThingConnectPlugin plugin) {
        return new PluginTechnicalException(errorCode, details, cause, component, plugin.getPluginId());
    }

    /**
     * Create a plugin exception from a Throwable
     *
     * @param throwable The Throwable to convert
     * @param plugin    The plugin that generated the exception
     * @return A new PluginException
     */
    public static PluginException fromThrowable(Throwable throwable, ThingConnectPlugin plugin) {
        if (throwable instanceof PluginException) {
            return (PluginException) throwable;
        } else if (throwable instanceof PluginBusinessException) {
            return new PluginException(StandardPluginErrorCode.PLUGIN_BUSINESS_ERROR, throwable, plugin.getPluginId(), plugin.getPluginName());
        } else if (throwable instanceof PluginTechnicalException) {
            return new PluginException(StandardPluginErrorCode.PLUGIN_TECHNICAL_ERROR, throwable, plugin.getPluginId(), plugin.getPluginName());
        } else {
            return new PluginException(StandardPluginErrorCode.PLUGIN_SYSTEM_ERROR, throwable, plugin.getPluginId(), plugin.getPluginName());
        }
    }
}
