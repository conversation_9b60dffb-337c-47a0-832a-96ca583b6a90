package com.siteweb.tcs.common.runtime;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 插件内的注入帮助类，用于获取插件领域内的Bean
 * 仅用作插件的非Spring Boot管理的Bean获取
 * 需要在插件ThingConnectPlugin.start()完成后使用
 *
 * <AUTHOR> (2024-06-24)
 **/
@Slf4j
@Component
public final class PluginScope implements ThingConnectPluginEvent, ApplicationContextAware {
    private ApplicationContext hostApplicationContext;
    private static final StackWalker walker = StackWalker.getInstance(StackWalker.Option.RETAIN_CLASS_REFERENCE);
    private static PluginScope instance;
    private final List<ThingConnectPlugin> plugins = new ArrayList<>();


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.hostApplicationContext = applicationContext;
    }

    public PluginScope() {
        instance = this;
    }


    @Override
    public void pluginStart(ThingConnectPlugin plugin) {
        plugins.add(plugin);
    }

    @Override
    public void pluginStop(ThingConnectPlugin plugin) {
        plugins.remove(plugin);
    }

    private Object getBeanInternal(Class<?> _class, String beanName) {
        var plugin = getPluginInternal(_class);
        if (plugin == null) {
            return this.hostApplicationContext.getBean(beanName);
        }
        return plugin.getApplicationContext().getBean(beanName);
    }

    private <T> T getBeanInternal(Class<?> _class, Class<?> beanClass) {
        var plugin = getPluginInternal(_class);
        if (plugin == null) {
            return (T) this.hostApplicationContext.getBean(beanClass);
        }
        return (T) plugin.getApplicationContext().getBean(beanClass);
    }


    private ThingConnectPlugin getPluginInternal(Class<?> _class) {
//        log.info("get Bean with {}", _class.getTypeName());
//        plugins.forEach(e -> {
//            log.info("plugin: {}", e.getPluginId());
//        });
        return plugins.stream()
                .filter(plugin -> _class.getPackageName().startsWith(plugin.getContext().getDescriptor().getPackageName()))
                .findFirst()
                .orElse(null);
    }


    /***
     * 获取某个插件内部的Bean（插件必须处于运行状态）。
     * <AUTHOR> (2024/6/24)
     * @param beanName  beanName 必须
     */
    public static Object getBean(String beanName) {
        Class<?> _class = walker.walk(frames -> frames.skip(1)
                .findFirst()
                .get()
                .getDeclaringClass()
        );
        return instance.getBeanInternal(_class, beanName);
    }


    /***
     * 获取某个插件内部的Bean（插件必须处于运行状态）。
     * <AUTHOR> (2024/6/24)
     * @param beanClass 插件的类型。
     */
    public static <T> T getBean(Class<?> beanClass) {
        Class<?> _class = walker.walk(frames -> frames.skip(1)
                .findFirst()
                .get()
                .getDeclaringClass()
        );
        return instance.getBeanInternal(_class, beanClass);
    }


    /**
     * 获取当前模块内的插件实例ID
     *
     * <AUTHOR> (2024/6/24)
     */
    public static String getPluginId() {
        Class<?> _class = walker.walk(frames -> frames.skip(1)
                .findFirst()
                .get()
                .getDeclaringClass()
        );
        ThingConnectPlugin plugin = instance.getPluginInternal(_class);
        if (plugin == null) throw new RuntimeException("当前模块下未检索到启动的插件实例");
        return plugin.getPluginId();
    }


    /**
     * 扫描对象的bean并进行填充。
     *
     * @param object 待扫描对象
     * <AUTHOR> (2024/7/23)
     */
    public static void autowireBean(Object object) {
        Class<?> _class = walker.walk(frames -> frames.skip(1)
                .findFirst()
                .get()
                .getDeclaringClass()
        );
        ThingConnectPlugin plugin = instance.getPluginInternal(_class);
        if (plugin == null) throw new RuntimeException("当前模块下未检索到启动的插件实例");
        plugin.getApplicationContext().getAutowireCapableBeanFactory().autowireBean(object);
    }


    /**
     * 获取当前模块内的插件实例ID
     *
     * <AUTHOR> (2024/6/24)
     */
    public static String getPluginName() {
        Class<?> _class = walker.walk(frames -> frames.skip(1)
                .findFirst()
                .get()
                .getDeclaringClass()
        );
        ThingConnectPlugin plugin = instance.getPluginInternal(_class);
        if (plugin == null) throw new RuntimeException("当前模块下未检索到启动的插件实例");
        return plugin.getPluginName();
    }


}
