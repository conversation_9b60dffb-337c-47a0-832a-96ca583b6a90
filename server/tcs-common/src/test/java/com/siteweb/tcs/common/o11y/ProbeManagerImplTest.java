package com.siteweb.tcs.common.o11y;

import org.apache.pekko.actor.ActorSystem;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * ProbeManagerImpl初始化测试类
 * 用于验证ProbeManagerImpl是否正确初始化
 */
@SpringBootTest
@ContextConfiguration(classes = TestConfig.class)
public class ProbeManagerImplTest {

//    @Autowired
//    private ApplicationContext applicationContext;
//
//    @Autowired
//    private ActorSystem actorSystem;
//
//    @Autowired
//    private ProbeManagerImpl probeManagerImpl;
//
//    @BeforeEach
//    public void setUp() {
//        // 确保ActorSystem已初始化
//        assertNotNull(actorSystem, "ActorSystem未正确初始化");
//
//        // 确保ProbeManagerImpl已注入
//        assertNotNull(probeManagerImpl, "ProbeManagerImpl未正确注入");
//
//        // 确保ProbeManagerAdapter中的ProbeManagerImpl已设置
//        ProbeManagerAdapter.setProbeManagerImpl(probeManagerImpl);
//    }
//
//    @Test
//    public void testProbeManagerImplInitialization() {
//        // 验证ProbeManagerImpl是否已正确初始化
//        assertNotNull(probeManagerImpl, "ProbeManagerImpl未正确初始化");
//
//        // 验证ProbeManagerAdapter是否能正常使用
//        try {
//            // 调用一个简单的方法来验证ProbeManagerAdapter是否正常工作
//            long probeCount = ProbeManagerAdapter.getProbeCount();
//            System.out.println("当前探针数量: " + probeCount);
//        } catch (Exception e) {
//            System.err.println("ProbeManagerAdapter调用失败: " + e.getMessage());
//            throw e;
//        }
//    }
}