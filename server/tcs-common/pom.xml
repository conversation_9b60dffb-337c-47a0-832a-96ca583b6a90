<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>thing-connect-server</artifactId>
        <groupId>com.siteweb</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tcs-common</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <name>tcs-common</name>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.4.3</version>
        </dependency>

        <!-- 添加Spring Boot测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 添加Flyway依赖 -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>9.16.0</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
            <version>9.16.0</version>
        </dependency>

        <!-- Pekko Core Dependencies -->
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-actor_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-actor-typed_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-stream_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-serialization-jackson_2.13</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.15.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.4</version>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <skipDockerBuild>true</skipDockerBuild>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
