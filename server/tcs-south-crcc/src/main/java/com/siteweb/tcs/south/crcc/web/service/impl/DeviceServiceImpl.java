package com.siteweb.tcs.south.crcc.web.service.impl;

import com.siteweb.tcs.south.crcc.dal.dto.DeviceDTO;
import com.siteweb.tcs.south.crcc.exception.CrccPluginException;
import com.siteweb.tcs.south.crcc.web.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 设备服务实现类
 */
@Slf4j
@Service
public class DeviceServiceImpl implements DeviceService {

    // 模拟设备数据存储
    private final Map<String, DeviceDTO> deviceMap = new HashMap<>();

    @Override
    public DeviceDTO getDevice(String id) {
        log.info("Getting device with id: {}", id);
        return deviceMap.get(id);
    }

    @Override
    public List<DeviceDTO> listDevices() {
        log.info("Listing all devices");
        return new ArrayList<>(deviceMap.values());
    }

    @Override
    public DeviceDTO createDevice(DeviceDTO device) {
        log.info("Creating new device: {}", device);
        
        if (device.getId() == null || device.getId().trim().isEmpty()) {
            device.setId(UUID.randomUUID().toString());
        }
        
        if (device.getName() == null || device.getName().trim().isEmpty()) {
            throw new CrccPluginException("Device name cannot be empty", 400);
        }
        
        device.setLastConnectionTime(System.currentTimeMillis());
        deviceMap.put(device.getId(), device);
        
        return device;
    }

    @Override
    public DeviceDTO updateDevice(String id, DeviceDTO device) {
        log.info("Updating device with id: {}", id);
        
        if (!deviceMap.containsKey(id)) {
            throw new CrccPluginException("Device not found: " + id, 404);
        }
        
        device.setId(id); // 确保ID不变
        deviceMap.put(id, device);
        
        return device;
    }

    @Override
    public boolean deleteDevice(String id) {
        log.info("Deleting device with id: {}", id);
        
        if (!deviceMap.containsKey(id)) {
            return false;
        }
        
        deviceMap.remove(id);
        return true;
    }
} 