<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.cmcc.dal.mapper.ExampleMapper">
    <!-- 这是一个示例Mapper文件，确保MyBatis配置能够正常加载 -->
    <!-- 实际开发中，请根据业务需求添加具体的SQL语句 -->

    <!-- 示例查询 -->
    <select id="selectExample" resultType="java.lang.String">
        SELECT 'Hello World from CMCC Plugin' AS greeting
    </select>
</mapper> 