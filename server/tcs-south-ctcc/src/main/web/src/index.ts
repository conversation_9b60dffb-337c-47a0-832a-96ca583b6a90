// 引入重置样式
import "./style/reset.scss";
// 导入公共样式
import "./style/index.scss";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css";
import "element-plus/dist/index.css";

import routes from "./menu-config";

console.warn("[CTCC插件] 开始加载插件...", new Date().toISOString());
console.warn("[CTCC插件] 全局对象检查:", {
  Vue全局对象: window.Vue === undefined ? "不存在" : "存在",
  VueRouter全局对象: window.VueRouter === undefined ? "不存在" : "存在",
  microFrontendRegistry:
    window.microFrontendRegistry === undefined ? "不存在" : "存在"
});

// 声明全局微前端注册对象类型
declare global {
  interface Window {
    microFrontendRegistry?: {
      mergeLocaleMessage: (
        locale: string,
        messages: Record<string, any>
      ) => void;
      registerMenu: (menuItem: any) => void;
      registerRoute: (routeConfig: any) => void;
    };
    Vue?: any;
    VueRouter?: any;
  }
}

// 路由配置转为菜单格式
function routeToMenu(route: any) {
  // 提取并处理主菜单
  const mainMenu = {
    icon: route.meta?.icon || "",
    name: String(route.meta?.title || route.name),
    order: route.meta?.order || 0,
    path: route.path,
    show: route.meta?.hideInMenu !== true,
    children: [] as any[]
  };

  // 转换子菜单
  if (route.children && route.children.length > 0) {
    mainMenu.children = route.children.map((child: any) => {
      // 处理子路由路径 - 确保子路由路径是完整的
      let fullPath = child.path;

      // 如果子路由路径是相对路径（不以/开头），则需要拼接父路由路径
      if (!child.path.startsWith("/")) {
        // 确保路径正确拼接，避免重复的/
        fullPath = route.path.endsWith("/")
          ? `${route.path}${child.path}`
          : `${route.path}/${child.path}`;
      }

      return {
        name: String(child.meta?.title || child.name),
        parent: route.path,
        parents: [route.path],
        path: fullPath,
        show: child.meta?.hideInMenu !== true,
        children: []
      };
    });
  }

  return mainMenu;
}

// 从i18n文件加载并合并国际化消息
function loadAndMergeLocaleMessages() {
  console.warn("[CTCC插件] 开始加载i18n文件...");

  try {
    // 使用import.meta.glob加载所有i18n文件
    const i18nFiles = import.meta.glob("./locales/langs/**/*.json", {
      eager: true
    });
    console.warn(
      "[CTCC插件] i18n文件加载完成, 文件数量:",
      Object.keys(i18nFiles).length
    );

    // 处理不同语言的消息
    const messages: Record<string, Record<string, any>> = {
      "zh-CN": {},
      "en-US": {}
    };

    // 遍历所有加载的i18n文件
    for (const path in i18nFiles) {
      // 从路径中提取语言code
      const match = path.match(/\/langs\/([^/]+)\/(.+)\.json$/);
      if (match) {
        const locale = match[1];
        const module = match[2];
        const fileContent = (i18nFiles[path] as any).default;

        console.warn(
          `[CTCC插件] 处理i18n文件: ${path}, 语言: ${locale}, 模块: ${module}`
        );

        // 确保语言对象存在
        if (typeof locale === "string" && locale in messages) {
          // 如果文件内容是一个扁平对象，则把它放在模块命名空间下
          messages[locale][module] = fileContent;
        }
      }
    }

    // 注册所有语言的消息
    if (window.microFrontendRegistry) {
      for (const locale in messages) {
        if (
          typeof locale === "string" &&
          locale in messages &&
          Object.keys(messages[locale]).length > 0
        ) {
          console.warn(`[CTCC插件] 注册${locale}语言消息`);
          window.microFrontendRegistry.mergeLocaleMessage(
            locale,
            messages[locale]
          );
        }
      }
    } else {
      console.error(
        "[CTCC插件] 无法找到microFrontendRegistry，国际化消息注册失败"
      );
    }
  } catch (error) {
    console.error("[CTCC插件] 加载国际化消息失败:", error);
  }
}

// 注册所有插件
function registerPlugins() {
  console.warn("[CTCC插件] 开始注册插件组件...");

  // 先注册国际化消息
  loadAndMergeLocaleMessages();

  // 使用新的微前端注册机制
  routes.forEach(route => {
    console.warn(
      `[CTCC插件] 注册插件: ${String(route.name)}, 路径: ${route.path}`
    );

    if (window.microFrontendRegistry) {
      // 注册路由
      try {
        console.warn(`[CTCC插件] 注册路由: ${String(route.name)}`);
        window.microFrontendRegistry.registerRoute(route);
        console.warn(`[CTCC插件] 路由注册成功: ${String(route.name)}`);
      } catch (error) {
        console.error(`[CTCC插件] 路由注册失败: ${String(route.name)}`, error);
      }

      // 注册菜单 - 将路由转换为菜单格式
      try {
        const menuConfig = routeToMenu(route);
        console.warn(`[CTCC插件] 注册菜单: ${menuConfig.name}`);
        console.warn(
          "[CTCC插件] 菜单配置:",
          JSON.stringify(menuConfig, null, 2)
        );
        window.microFrontendRegistry.registerMenu(menuConfig);
        console.warn(`[CTCC插件] 菜单注册成功: ${menuConfig.name}`);
      } catch (error) {
        console.error(`[CTCC插件] 菜单注册失败: ${String(route.name)}`, error);
      }
    } else {
      console.error(
        `[CTCC插件] 没有可用的注册机制，无法注册插件: ${String(route.name)}`
      );
    }
  });

  console.warn("[CTCC插件] 所有插件注册完成");
}

// 启动注册流程
console.warn("[CTCC插件] 初始化完成，开始注册流程");
registerPlugins();

export default routes;
