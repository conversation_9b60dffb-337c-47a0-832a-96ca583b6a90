package com.siteweb.tcs.south.cmcc.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.south.cmcc.dal.mapper"}, sqlSessionFactoryRef = "cmccSqlSessionFactory")
public class DataSourceConfig {

    @Bean(name = "cmccDataSourceProperties")
    @ConfigurationProperties(prefix = "plugin.datasource.cmcc")
    public DataSourceProperties cmccDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "cmccDataSource")
    public DataSource cmccDataSource(@Qualifier("cmccDataSourceProperties") DataSourceProperties dataSourceProperties) {
        return dataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "cmccTransactionManager")
    public DataSourceTransactionManager cmccTransactionManager(@Qualifier("cmccDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cmccSqlSessionFactory")
    public SqlSessionFactory cmccSqlSessionFactory(@Qualifier("cmccDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/south-cmcc-plugin/*.xml"));
        return bean.getObject();
    }
} 