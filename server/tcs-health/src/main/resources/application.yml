spring:
  application:
    name: Siteweb Health Monitor
---
spring:
  web:
    locale: zh_CN
    locale-resolver: fixed
---
spring:
  servlet:
    multipart:
      #      单次请求文件大小设置
      max-file-size: 100MB
      max-request-size: 120MB
---
spring:
  boot:
    admin:
      # context-path: /api/thing
      ui:
        title: ${UI_TITLE:Siteweb Health Monitor}
        brand: <img src="assets/img/icon-spring-boot-admin.svg"><span>${spring.boot.admin.ui.title}</span>
        view-settings:
          - name: "about"
            enabled: false
      # 启用日志收集
      log-file:
        enabled: true
      # 配置 Actuator 日志路径
      log-path: /actuator/logfile
      # 配置 Actuator 日志文件名
      log-filename: your-app.log



server:
  port: 9800   # Spring boot - Admin Port


logging:
  config: classpath:logback-spring.xml
  level:
    ROOT: INFO
    org.siteweb.health.monitoring: INFO
    org.springframework.web: INFO
  pattern:
    file: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"

management:
  endpoints:
    web:
      exposure:
        include: "*"

---
management:
  endpoint:
    refresh:
      enabled: true
    restart:
      enabled: true
    shutdown:
      enabled: true
    env:
      post:
        enabled: true
    health:
      show-details: ALWAYS
    logfile:
      enabled: true
  trace:
    http:
      enabled: true
