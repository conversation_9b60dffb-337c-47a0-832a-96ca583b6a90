const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Plugin",
  component: Layout,
  redirect: "/siteweb-omc/protocol",
  meta: {
    icon: "ep/home-filled",
    title: "SITEWEB-OMC",
    rank: 0
  },
  children: [
    {
      path: "/siteweb-omc/protocol",
      name: "SitewebOmcProtocol",
      component: () => import("@/views/protocol/index.vue"),
      meta: {
        icon: "mingcute:file-security-line",
        title: "协议管理",
        showParent: true
      }
    },
    {
      path: "/siteweb-omc/monitor-unit",
      name: "SitewebOmcMonitorUnit",
      component: () => import("@/views/monitor-unit/index.vue"),
      meta: {
        icon: "ic:outline-monitor-heart",
        title: "采集器",
        showParent: true
      }
    },
    {
      path: "/siteweb-omc/device-management/:id?",
      name: "SitewebOmcDeviceManagment",
      component: () => import("@/views/device-managment/index.vue"),
      meta: {
        icon: "ic:outline-monitor-heart",
        title: "设备管理",
        showLink: false
      }
    }
  ]
} satisfies RouteConfigsTable;
