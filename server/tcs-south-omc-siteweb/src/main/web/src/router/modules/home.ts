const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Plugin",
  component: Layout,
  redirect: "/siteweb-omc/protocol",
  meta: {
    icon: "ep/home-filled",
    title: "SITEWEB-OMC",
    rank: 0
  },
  children: [
    {
      path: "/siteweb-omc/protocol",
      name: "SitewebOmcProtocol",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        icon: "mingcute:file-security-line",
        title: "协议管理",
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
