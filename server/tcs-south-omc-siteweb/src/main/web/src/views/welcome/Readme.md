# 协议管理页面功能文档

## 页面概述

协议管理页面是一个用于管理通信协议的综合管理界面，支持协议的导入、查看、编辑、删除以及动态库文件的管理功能。页面主要分为协议列表展示区域和协议导入功能模块。

## 核心功能模块

### 1. 协议列表管理

#### 1.1 表格展示功能
- **多列数据展示**：支持显示协议名称、动态库路径、关联模板、更新状态、设备型号等信息
- **虚拟滚动**：支持大数据量的虚拟滚动展示，提升页面性能
- **列宽调整**：支持拖拽调整表格列宽，用户可自定义列宽
- **表格排序**：支持单列排序功能
- **无边框设计**：采用无边框表格样式，界面更简洁

#### 1.2 数据选择功能
- **全选功能**：支持一键全选/取消全选所有协议
- **单选功能**：支持单个协议的选择和取消选择
- **选中状态展示**：选中的行会有高亮显示效果
- **批量操作支持**：基于选择状态支持批量删除等操作

#### 1.3 数据搜索与筛选
- **协议名称搜索**：支持根据协议名称进行模糊搜索
- **动态库路径搜索**：支持根据动态库路径进行模糊搜索
- **关联模板搜索**：支持根据关联的设备模板名称进行搜索
- **设备型号筛选**：通过"编辑条件"功能支持多选设备型号筛选
- **更新状态筛选**：通过"编辑条件"功能支持筛选已上传/未上传状态
- **实时搜索**：搜索输入后延迟500ms自动执行搜索，避免频繁请求
- **条件组合筛选**：支持多个搜索条件同时生效

### 2. 协议导入功能

#### 2.1 协议模板上传
- **文件类型限制**：仅支持.xml格式的协议模板文件
- **文件选择器**：提供友好的文件选择界面
- **上传状态提示**：显示"*已选择"提示，确认文件已选中
- **必选验证**：协议模板文件为必选项，带有红色*标记
- **上传进度控制**：上传过程中禁用相关按钮，防止重复操作

#### 2.2 动态库文件上传
- **335X动态库上传**：支持上传.dll或.so格式的335X协议动态库
- **9200动态库上传**：支持上传.dll或.so格式的9200协议动态库
- **文件格式验证**：严格限制文件格式为.dll或.so
- **双平台支持**：同时支持Windows(.dll)和Linux(.so)平台的动态库
- **选择状态提示**：每个文件类型都有独立的选择状态提示

#### 2.3 标准设备种类设置
- **自动识别**：上传协议模板后自动识别设备种类信息
- **标准映射**：支持为协议中的模板设置标准设备种类
- **弹窗选择**：通过模态框提供设备种类选择界面
- **下拉选择器**：提供设备种类的下拉选择功能
- **关联更新**：设置完成后自动更新协议的设备种类信息

#### 2.4 上传流程控制
- **分步上传**：先上传协议模板，再上传动态库文件
- **错误处理**：支持各种上传错误的处理和提示
- **成功反馈**：上传成功后提供明确的成功提示信息
- **状态管理**：通过loading状态管理上传过程的UI状态

### 3. 协议编辑功能

#### 3.1 在线编辑
- **双击编辑**：通过双击表格单元格进入编辑模式
- **字段编辑**：支持编辑协议名称、动态库路径、设备型号等字段
- **输入验证**：编辑时进行字段长度验证（最小3字符）
- **自动聚焦**：编辑模式下自动聚焦到输入框
- **编辑状态管理**：防止同时编辑多个字段的冲突

#### 3.2 下拉选择编辑
- **设备型号选择**：设备型号字段通过下拉选择器进行编辑
- **数据源管理**：下拉选项从后端API动态获取
- **选择确认**：选择后需要确认才能保存修改
- **键值映射**：支持显示名称与实际值的映射关系

#### 3.3 编辑确认机制
- **修改确认弹窗**：修改任何字段都需要通过确认弹窗确认
- **取消恢复**：取消修改时自动恢复原值
- **防误操作**：通过二次确认防止意外修改
- **实时保存**：确认后立即保存到后端

### 4. 协议删除功能

#### 4.1 批量删除
- **选择验证**：删除前验证是否有选中的协议
- **确认弹窗**：删除前弹出确认对话框
- **批量处理**：支持同时删除多个选中的协议
- **ID拼接**：将多个协议ID拼接为逗号分隔的字符串传递给后端

#### 4.2 删除反馈
- **成功提示**：删除成功后显示成功消息
- **列表刷新**：删除后自动刷新协议列表
- **选择清空**：删除后清空选择状态
- **错误处理**：删除失败时的错误处理机制

### 5. 动态库管理功能

#### 5.1 动态库上传
- **单独上传入口**：每行协议都有独立的"动态库上传"操作入口
- **类型选择**：支持上传335X和9200两种类型的动态库
- **协议关联**：上传的动态库自动关联到对应的协议
- **覆盖提示**：上传前提示可能覆盖原有动态库文件

#### 5.2 动态库删除
- **分类删除**：提供"335X动态库删除"和"9200动态库删除"两个独立操作
- **确认删除**：删除前需要确认操作
- **精确删除**：根据协议代码和动态库类型精确删除指定文件
- **状态反馈**：删除后提供操作结果反馈

### 6. 用户界面功能

#### 6.1 响应式布局
- **自适应高度**：表格高度根据浏览器窗口大小自动调整
- **滚动条管理**：支持表格内容的水平和垂直滚动
- **位置记忆**：页面切换后能够记住滚动条位置
- **虚拟滚动优化**：大数据量情况下的滚动性能优化

#### 6.2 操作提示与反馈
- **loading状态**：操作过程中显示加载状态
- **成功消息**：操作成功后的绿色提示消息
- **错误消息**：操作失败后的红色错误提示
- **警告消息**：操作前的黄色警告提示
- **操作禁用**：操作过程中禁用相关按钮防止重复点击

#### 6.3 数据状态管理
- **实时更新**：操作后实时刷新数据显示
- **状态同步**：前端状态与后端数据保持同步
- **缓存管理**：合理的数据缓存和更新策略
- **错误恢复**：操作失败时的数据状态恢复

### 7. 技术特性

#### 7.1 性能优化
- **虚拟滚动**：支持大数据量的高性能渲染
- **防抖搜索**：搜索输入的防抖处理减少API调用
- **按需加载**：组件和数据的按需加载
- **内存管理**：合理的组件生命周期管理

#### 7.2 用户体验
- **双击编辑**：直观的双击编辑交互方式
- **即时反馈**：操作后的即时视觉反馈
- **状态保持**：页面状态在路由切换时的保持
- **无缝操作**：流畅的用户操作体验

## API接口说明

### 协议管理相关接口
- `GET api/config/sampler` - 获取协议列表
- `PUT api/config/sampler` - 更新协议信息
- `DELETE api/config/sampler?samplerIds={ids}` - 批量删除协议

### 协议导入相关接口  
- `POST api/config/equipmenttemplate/import` - 上传协议模板文件
- `POST api/config/sampler/upload/dllpath` - 上传动态库文件
- `PUT api/config/equipmenttemplate/updatecategory` - 更新设备种类

### 动态库管理相关接口
- `DELETE api/config/sampler/protocolfile` - 删除动态库文件
- `GET api/config/sampler/download/dllpath` - 下载动态库文件

### 基础数据接口
- `GET api/config/equipmenttemplate` - 获取设备模板列表
- `GET api/config/dataitems?entryId=37` - 获取设备型号列表
- `GET api/config/equipmentcategorymap/list/{category}` - 获取设备种类映射

## 注意事项

1. **文件安全性**：上传的动态库文件会在配置下发时一起下发，可能覆盖原有文件，需谨慎操作
2. **并发控制**：编辑操作有并发控制，防止同时编辑多个字段
3. **数据一致性**：所有修改操作都需要后端确认，确保数据一致性
4. **错误处理**：完善的错误处理机制，包括网络错误、业务错误等各种情况
5. **性能考虑**：大数据量情况下使用虚拟滚动，搜索使用防抖优化
