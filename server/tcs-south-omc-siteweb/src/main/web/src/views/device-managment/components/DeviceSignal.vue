<template>
  <div class="device-signal-container">
    <!-- 信号数据表格 -->
    <div class="table-container">
      <el-auto-resizer v-loading="loading">
        <template #default="{ height, width }">
          <el-table-v2
            ref="tableRef"
            :columns="columns"
            :data="filteredTableData"
            :width="width"
            :height="Math.max(height, 800)"
            row-key="signalId"
            fixed
          />
        </template>
      </el-auto-resizer>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElButton, ElCheckbox, ElIcon, ElPopover, ElInput, ElSelect, ElOption } from 'element-plus'
import { Filter } from '@element-plus/icons-vue'
import { getDataDictionary, getSignalListByTemplate } from '@/api/device-management'
import type { Column, HeaderCellSlotProps } from 'element-plus'

interface Props {
  equipmentId: string
  active: boolean
  tabIndex?: number
  template?: any
  muCategory?: number
  buttonFlag?: boolean
  searchText?: string
}

interface SignalData {
  id: number | null
  equipmentTemplateId: number
  signalId: number
  signalName: string
  displayIndex: number
  signalCategory: number
  signalType: number
  channelNo: number | string
  channelType: number
  expression: string
  dataType: string
  showPrecision: string
  unit: string
  storeInterval: string
  absValueThreshold: number
  percentThreshold: number
  staticsPeriod: number
  enable: boolean
  visible: boolean
  chargeStoreInterVal?: number
  chargeAbsValue?: number
  description: string
  signalProperty: string
  signalPropertyIds: number[]
  moduleNo: number
  baseTypeId: number | null
  baseTypeName: string
  stateValue: string
  signalMeaningsList: any[]
  signalPropertyList: any[]
  acrossSignal: boolean
  hasInstance: boolean
}

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    enabled: boolean
    value?: any
    options?: Array<{label: string, value: any}>
    type: 'text' | 'select' | 'boolean' | 'number'
  }
}

const props = withDefaults(defineProps<Props>(), {
  buttonFlag: false,
  muCategory: 0
})

// 响应式数据
const loading = ref(false)
const tableRef = ref()
const tableData = ref<SignalData[]>([])

// 字典数据
const signalCategoryArr = ref<{id: string, name: string}[]>([])
const signalTypeArr = ref<{id: string, name: string}[]>([])
const channelTypeArr = ref<{id: string, name: string}[]>([])
const dataTypeArr = ref<{id: string, name: string}[]>([])
const signalPropertyArr = ref<{id: string, name: string}[]>([])

// 电池设备数据
const batteryData = ref<any>(null)

// 过滤器状态
const filterState = ref<FilterState>({})

// 存储周期预设值
const storeIntervalArr = ['86400', '28800', '14400', '3600', '1800', '600', '300', '43200', '21600', '17280', '10800', '9600', '8640', '7200', '5760', '5400', '4800', '4320', '3456', '3200', '2880', '2700', '2400', '2160', '1920', '1728', '1600', '1440', '1350', '1200', '1152', '1080', '960', '900', '864', '800', '720', '675', '640', '576', '540', '480', '450', '432', '400', '384', '360', '320', '288', '270', '240', '225', '216', '200', '192', '180', '160', '150', '144', '135', '128', '120', '108', '100', '96', '90', '80', '75', '72', '64', '60', '54', '50', '48', '45', '40', '36', '32', '30', '27', '25', '24', '20', '18', '16', '15', '12', '10', '9', '8', '6', '5', '4', '3', '2', '1', '0']

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    signalName: { enabled: false, value: '', type: 'text' },
    signalId: { enabled: false, value: '', type: 'text' },
    displayIndex: { enabled: false, value: '', type: 'number' },
    signalCategory: { 
      enabled: false, 
      value: '', 
      type: 'select',
      options: [
        { label: '模拟量', value: 1 },
        { label: '开关量', value: 2 },
        { label: '脉冲量', value: 3 }
      ]
    },
    signalType: { 
      enabled: false, 
      value: '', 
      type: 'select',
      options: [
        { label: '输入信号', value: 1 },
        { label: '输出信号', value: 2 },
        { label: '计算信号', value: 3 }
      ]
    },
    channelNo: { enabled: false, value: '', type: 'text' },
    channelType: { 
      enabled: false, 
      value: '', 
      type: 'select',
      options: [
        { label: 'AI', value: 1 },
        { label: 'DI', value: 2 },
        { label: 'AO', value: 3 },
        { label: 'DO', value: 4 }
      ]
    },
    expression: { enabled: false, value: '', type: 'text' },
    dataType: { 
      enabled: false, 
      value: '', 
      type: 'select',
      options: [
        { label: 'BOOL', value: 'BOOL' },
        { label: 'INT', value: 'INT' },
        { label: 'REAL', value: 'REAL' },
        { label: 'STRING', value: 'STRING' }
      ]
    },
    showPrecision: { enabled: false, value: '', type: 'text' },
    unit: { enabled: false, value: '', type: 'text' },
    storeInterval: { enabled: false, value: '', type: 'text' },
    absValueThreshold: { enabled: false, value: '', type: 'number' },
    percentThreshold: { enabled: false, value: '', type: 'number' },
    staticsPeriod: { enabled: false, value: '', type: 'number' },
    enable: { 
      enabled: false, 
      value: '', 
      type: 'select',
      options: [
        { label: '是', value: true },
        { label: '否', value: false }
      ]
    },
    visible: { 
      enabled: false, 
      value: '', 
      type: 'select',
      options: [
        { label: '是', value: true },
        { label: '否', value: false }
      ]
    },
    description: { enabled: false, value: '', type: 'text' },
    signalProperty: { enabled: false, value: '', type: 'text' },
    stateValue: { enabled: false, value: '', type: 'text' },
    moduleNo: { enabled: false, value: '', type: 'number' },
    baseTypeName: { enabled: false, value: '', type: 'text' },
    chargeStoreInterVal: { enabled: false, value: '', type: 'number' },
    chargeAbsValue: { enabled: false, value: '', type: 'number' }
  }
}

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey
  
  return (props: HeaderCellSlotProps) => {
    const popoverRef = ref()
    
    const onFilter = () => {
      popoverRef.value?.hide()
    }
    
    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].enabled = false
        filterState.value[filterKey].value = filterState.value[filterKey].type === 'select' ? '' : 
                                             filterState.value[filterKey].type === 'number' ? '' : ''
      }
    }
    
    const renderFilterControl = () => {
      const filter = filterState.value[filterKey]
      if (!filter) return null
      
      switch (filter.type) {
        case 'text':
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElInput
                v-model={filter.value}
                placeholder="输入过滤条件"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
              />
            </>
          )
        case 'number':
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElInput
                v-model={filter.value}
                placeholder="输入数值"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
                type="number"
              />
            </>
          )
        case 'select':
          return (
            <>
              <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
              <ElSelect
                v-model={filter.value}
                placeholder="选择过滤条件"
                size="small"
                style="margin-top: 8px"
                disabled={!filter.enabled}
                clearable
              >
                {filter.options?.map(option => (
                  <ElOption key={option.value} label={option.label} value={option.value} />
                ))}
              </ElSelect>
            </>
          )
        default:
          return null
      }
    }
    
    return (
      <div class="flex items-center justify-center">
        <span class="mr-2 text-xs">{props.column.title}</span>
        <ElPopover ref={popoverRef} trigger="click" width={200}>
          {{
            default: () => (
              <div class="filter-wrapper">
                <div class="filter-group">
                  {renderFilterControl()}
                </div>
                <div class="el-table-v2__demo-filter">
                  <ElButton text onClick={onFilter}>
                    确认
                  </ElButton>
                  <ElButton text onClick={onReset}>
                    重置
                  </ElButton>
                </div>
              </div>
            ),
            reference: () => (
              <ElIcon class="cursor-pointer">
                <Filter />
              </ElIcon>
            ),
          }}
        </ElPopover>
      </div>
    )
  }
}

// 创建标签显示单元格
const createTagCell = (dataKey: string, options?: any[]) => {
  return ({ rowData }: { rowData: SignalData }) => {
    const cellStyle = getCellStyle(rowData)
    let displayValue = rowData[dataKey as keyof SignalData]
    
    // 处理布尔值显示 - 有效和可见字段
    if (dataKey === 'enable' || dataKey === 'visible') {
      const isTrue = displayValue as boolean
      return (
        <div style={cellStyle}>
          <span class={isTrue ? 'text-green-600' : 'text-red-600'}>
            {isTrue ? '是' : '否'}
          </span>
        </div>
      )
    }
    
    // 处理信号种类
    if (dataKey === 'signalCategory') {
      const categoryMap: { [key: number]: string } = {
        1: '模拟量',
        2: '开关量',
        3: '脉冲量'
      }
      
      const text = categoryMap[displayValue as number]
      if (text) {
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        )
      }
      
      // 如果没有匹配的预设值，从字典中查找
      if (options && typeof displayValue === 'number') {
        const option = options.find(item => parseInt(item.id) === displayValue)
        const text = option?.name || displayValue
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        )
      }
    }
    
    // 处理信号分类
    if (dataKey === 'signalType') {
      const typeMap: { [key: number]: string } = {
        1: '输入信号',
        2: '输出信号',
        3: '计算信号'
      }
      
      const text = typeMap[displayValue as number]
      if (text) {
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        )
      }
      
      // 从字典中查找
      if (options && typeof displayValue === 'number') {
        const option = options.find(item => parseInt(item.id) === displayValue)
        const text = option?.name || displayValue
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        )
      }
    }
    
    // 处理通道类型
    if (dataKey === 'channelType') {
      const channelTypeMap: { [key: number]: string } = {
        1: 'AI',
        2: 'DI',
        3: 'AO',
        4: 'DO'
      }
      
      const text = channelTypeMap[displayValue as number]
      if (text) {
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        )
      }
      
      // 从字典中查找
      if (options && typeof displayValue === 'number') {
        const option = options.find(item => parseInt(item.id) === displayValue)
        const text = option?.name || displayValue
        return (
          <div style={cellStyle}>
            <span>{text}</span>
          </div>
        )
      }
    }
    
    // 处理数据类型
    if (dataKey === 'dataType') {
      const dataTypeMap: { [key: string]: string } = {
        'BOOL': 'BOOL',
        'INT': 'INT',
        'REAL': 'REAL',
        'STRING': 'STRING'
      }
      
      const text = dataTypeMap[displayValue as string] || displayValue
      return (
        <div style={cellStyle}>
          <span>{text || ''}</span>
        </div>  
      )
    }
    
    // 处理信号属性（多个值用逗号分隔）
    if (dataKey === 'signalPropertyIds' && Array.isArray(displayValue)) {
      const propertyTexts = displayValue.map(id => {
        const option = signalPropertyArr.value.find(item => parseInt(item.id) === id)
        return option?.name || id
      })
      
      return (
        <div style={cellStyle}>
          <span>{propertyTexts.join(', ')}</span>
        </div>
      )
    }
    
    // 默认处理
    return (
      <div style={cellStyle}>
        {displayValue || ''}
      </div>
    )
  }
}

// 创建状态信号专用显示组件
const createStateSignalCell = () => {
  return ({ rowData }: { rowData: SignalData }) => {
    const cellStyle = getCellStyle(rowData)
    
    return (
      <div style={cellStyle}>
        {rowData.signalCategory === 2 && rowData.stateValue ? (
          <div class="state-signal-container">
            <span class="state-text">
              {rowData.stateValue}
            </span>
          </div>
        ) : rowData.signalCategory === 2 ? (
          <span class="placeholder-text">
            未配置状态信号
          </span>
        ) : (
          <span class="disabled-text">
            非开关信号
          </span>
        )}
      </div>
    )
  }
}

// 创建只读显示单元格
const createReadOnlyCell = (dataKey: string, options?: any[]) => {
  return ({ rowData }: { rowData: SignalData }) => {
    const cellStyle = getCellStyle(rowData)
    let displayValue = rowData[dataKey as keyof SignalData]
    
    // 如果有选项数组，转换显示值
    if (options && typeof displayValue === 'number') {
      const option = options.find(item => parseInt(item.id) === displayValue)
      displayValue = option?.name || displayValue
    }
    
    return (
      <div style={cellStyle}>
        {displayValue || ''}
      </div>
    )
  }
}

// 定义表格列
const columns = computed<Column<SignalData>[]>(() => {
  const baseColumns: Column<SignalData>[] = [
    // 信号名称
    {
      key: 'signalName',
      dataKey: 'signalName',
      title: '名称',
      width: 150,
      fixed: 'left',
      cellRenderer: createReadOnlyCell('signalName'),
      headerCellRenderer: createFilterHeader({ dataKey: 'signalName' })
    },
    // 信号ID
    {
      key: 'signalId',
      dataKey: 'signalId',
      title: '信号ID',
      width: 100,
      fixed: 'left',
      cellRenderer: createReadOnlyCell('signalId'),
      headerCellRenderer: createFilterHeader({ dataKey: 'signalId' })
    },
    // 显示顺序
    {
      key: 'displayIndex',
      dataKey: 'displayIndex',
      title: '显示顺序',
      width: 100,
      cellRenderer: createReadOnlyCell('displayIndex'),
      headerCellRenderer: createFilterHeader({ dataKey: 'displayIndex' })
    },
    // 信号种类 - 使用tag显示
    {
      key: 'signalCategory',
      dataKey: 'signalCategory',
      title: '种类',
      width: 120,
      cellRenderer: createTagCell('signalCategory', signalCategoryArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: 'signalCategory' })
    },
    // 信号分类 - 使用tag显示
    {
      key: 'signalType',
      dataKey: 'signalType',
      title: '分类',
      width: 120,
      cellRenderer: createTagCell('signalType', signalTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: 'signalType' })
    },
    // 通道号
    {
      key: 'channelNo',
      dataKey: 'channelNo',
      title: '通道号',
      width: 90,
      cellRenderer: createReadOnlyCell('channelNo'),
      headerCellRenderer: createFilterHeader({ dataKey: 'channelNo' })
    },
    // 通道类型 - 使用tag显示
    {
      key: 'channelType',
      dataKey: 'channelType',
      title: '通道类型',
      width: 120,
      cellRenderer: createTagCell('channelType', channelTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: 'channelType' })
    },
    // 表达式
    {
      key: 'expression',
      dataKey: 'expression',
      title: '表达式',
      width: 170,
      cellRenderer: createReadOnlyCell('expression'),
      headerCellRenderer: createFilterHeader({ dataKey: 'expression' })
    },
    // 数据类型 - 使用tag显示
    {
      key: 'dataType',
      dataKey: 'dataType',
      title: '数据类型',
      width: 120,
      cellRenderer: createTagCell('dataType', dataTypeArr.value),
      headerCellRenderer: createFilterHeader({ dataKey: 'dataType' })
    },
    // 精度
    {
      key: 'showPrecision',
      dataKey: 'showPrecision',
      title: '精度',
      width: 100,
      cellRenderer: createReadOnlyCell('showPrecision'),
      headerCellRenderer: createFilterHeader({ dataKey: 'showPrecision' })
    },
    // 单位
    {
      key: 'unit',
      dataKey: 'unit',
      title: '单位',
      width: 100,
      cellRenderer: createReadOnlyCell('unit'),
      headerCellRenderer: createFilterHeader({ dataKey: 'unit' })
    },
    // 存储周期
    {
      key: 'storeInterval',
      dataKey: 'storeInterval',
      title: '存储周期(秒)',
      width: 140,
      cellRenderer: createReadOnlyCell('storeInterval'),
      headerCellRenderer: createFilterHeader({ dataKey: 'storeInterval' })
    },
    // 绝对值阈值
    {
      key: 'absValueThreshold',
      dataKey: 'absValueThreshold',
      title: '绝对值阈值',
      width: 120,
      cellRenderer: createReadOnlyCell('absValueThreshold'),
      headerCellRenderer: createFilterHeader({ dataKey: 'absValueThreshold' })
    },
    // 百分比阈值
    {
      key: 'percentThreshold',
      dataKey: 'percentThreshold',
      title: '百分比阈值',
      width: 120,
      cellRenderer: createReadOnlyCell('percentThreshold'),
      headerCellRenderer: createFilterHeader({ dataKey: 'percentThreshold' })
    },
    // 统计周期
    {
      key: 'staticsPeriod',
      dataKey: 'staticsPeriod',
      title: '统计周期(小时)',
      width: 140,
      cellRenderer: createReadOnlyCell('staticsPeriod'),
      headerCellRenderer: createFilterHeader({ dataKey: 'staticsPeriod' })
    },
    // 有效 - 使用tag显示
    {
      key: 'enable',
      dataKey: 'enable',
      title: '有效',
      width: 80,
      cellRenderer: createTagCell('enable'),
      headerCellRenderer: createFilterHeader({ dataKey: 'enable' })
    },
    // 可见 - 使用tag显示
    {
      key: 'visible',
      dataKey: 'visible',
      title: '可见',
      width: 80,
      cellRenderer: createTagCell('visible'),
      headerCellRenderer: createFilterHeader({ dataKey: 'visible' })
    },
    // 说明
    {
      key: 'description',
      dataKey: 'description',
      title: '说明',
      width: 120,
      cellRenderer: createReadOnlyCell('description'),
      headerCellRenderer: createFilterHeader({ dataKey: 'description' })
    },
    // 信号属性 - 使用多个tag显示
    {
      key: 'signalProperty',
      dataKey: 'signalPropertyIds',
      title: '信号属性',
      width: 150,
      cellRenderer: createTagCell('signalPropertyIds'),
      headerCellRenderer: createFilterHeader({ dataKey: 'signalProperty' })
    },
    // 状态信号 - 使用专用的状态信号组件
    {
      key: 'stateValue',
      dataKey: 'stateValue',
      title: '状态信号',
      width: 250,
      cellRenderer: createStateSignalCell(),
      headerCellRenderer: createFilterHeader({ dataKey: 'stateValue' })
    },
    // 所属模块
    {
      key: 'moduleNo',
      dataKey: 'moduleNo',
      title: '所属模块',
      width: 100,
      cellRenderer: createReadOnlyCell('moduleNo'),
      headerCellRenderer: createFilterHeader({ dataKey: 'moduleNo' })
    },
    // 基类信号
    {
      key: 'baseTypeName',
      dataKey: 'baseTypeName',
      title: '基类信号',
      width: 180,
      cellRenderer: createReadOnlyCell('baseTypeName'),
      headerCellRenderer: createFilterHeader({ dataKey: 'baseTypeName' })
    }
  ]

  // 如果显示电池字段，添加相关列
  if (showBatteryFields.value) {
    baseColumns.splice(-2, 0, 
      {
        key: 'chargeStoreInterVal',
        dataKey: 'chargeStoreInterVal',
        title: '后备状态电池存储周期(秒)',
        width: 200,
        cellRenderer: createReadOnlyCell('chargeStoreInterVal'),
        headerCellRenderer: createFilterHeader({ dataKey: 'chargeStoreInterVal' })
      },
      {
        key: 'chargeAbsValue',
        dataKey: 'chargeAbsValue',
        title: '后备状态电池绝对值阀值',
        width: 200,
        cellRenderer: createReadOnlyCell('chargeAbsValue'),
        headerCellRenderer: createFilterHeader({ dataKey: 'chargeAbsValue' })
      }
    )
  }

  return baseColumns
})

// 计算属性
const showBatteryFields = computed(() => {
  // 根据设备类别判断是否显示电池字段
  if (!batteryData.value || !props.template?.equipmentCategory) {
    return false
  }
  
  return props.template.equipmentCategory in batteryData.value
})

// 应用过滤器的数据
const filteredTableData = computed(() => {
  let data = tableData.value
  
  // 首先应用搜索文本过滤
  if (props.searchText && props.searchText.trim() !== '') {
    const searchTerm = props.searchText.toLowerCase()
    data = data.filter(item => {
      return (
        (item.signalName && item.signalName.toLowerCase().includes(searchTerm)) ||
        (item.signalId && item.signalId.toString().includes(searchTerm)) ||
        (item.description && item.description.toLowerCase().includes(searchTerm))
      )
    })
  }
  
  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.enabled && filter.value !== '' && filter.value !== null && filter.value !== undefined) {
      data = data.filter(item => {
        const itemValue = item[key as keyof SignalData]
        
        switch (filter.type) {
          case 'text':
            return itemValue && itemValue.toString().toLowerCase().includes(filter.value.toLowerCase())
          case 'number':
            return itemValue !== null && itemValue !== undefined && itemValue.toString() === filter.value.toString()
          case 'select':
            if (key === 'signalPropertyIds') {
              // 处理信号属性数组
              return Array.isArray(itemValue) && itemValue.includes(filter.value)
            }
            return itemValue === filter.value
          default:
            return true
        }
      })
    }
  })
  
  return data
})

// 方法
const initData = async () => {
  console.log('开始初始化数据，参数检查:', {
    equipmentId: props.equipmentId,
    template: props.template,
    active: props.active,
    tabIndex: props.tabIndex
  })
  
  if (!props.equipmentId) {
    console.warn('equipmentId为空，无法初始化数据')
    ElMessage.warning('设备ID为空，无法加载信号数据')
    return
  }
  
  loading.value = true
  try {
    console.log('开始并行加载字典数据、信号列表和电池设备数据')
    await Promise.all([
      loadDictionaryData(),
      loadSignalList(),
      loadBatteryDeviceData()
    ])
    console.log('数据初始化完成')
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('数据加载失败: ' + (error.message || error))
  } finally {
    loading.value = false
  }
}

const loadDictionaryData = async () => {
  // 字典数据映射
  const dictMapping = {
    17: { key: 'signalCategoryArr', name: '信号种类' },
    18: { key: 'signalTypeArr', name: '信号分类' },
    22: { key: 'channelTypeArr', name: '通道类型' },
    70: { key: 'dataTypeArr', name: '数据类型' },
    21: { key: 'signalPropertyArr', name: '信号属性' }
  }
  
  try {
    // 并行获取所有字典数据
    const promises = Object.entries(dictMapping).map(async ([entryId, config]) => {
      const result = await getDataDictionary(Number(entryId))
      
      if (result.code === 0 && result.data) {
        // 转换数据格式
        const formattedData = result.data.map((item: any) => ({
          id: item.itemId?.toString() || '',
          name: item.itemValue || ''
        }))
        
        return { key: config.key, data: formattedData }
      } else {
        console.warn(`获取${config.name}字典数据失败:`, result.msg || result.message)
        return { key: config.key, data: [] }
      }
    })
    
    const results = await Promise.all(promises)
    
    // 将数据赋值给对应的响应式变量
    results.forEach(result => {
      switch (result.key) {
        case 'signalCategoryArr':
          signalCategoryArr.value = result.data
          break
        case 'signalTypeArr':
          signalTypeArr.value = result.data
          break
        case 'channelTypeArr':
          channelTypeArr.value = result.data
          break
        case 'dataTypeArr':
          dataTypeArr.value = result.data
          break
        case 'signalPropertyArr':
          signalPropertyArr.value = result.data
          break
      }
    })
    
  } catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.warning('部分字典数据加载失败，可能影响下拉选项显示')
    
    // 设置默认数据
    signalCategoryArr.value = [
      { id: '1', name: '模拟量' },
      { id: '2', name: '开关量' },
      { id: '3', name: '脉冲量' }
    ]
    
    signalTypeArr.value = [
      { id: '1', name: '输入信号' },
      { id: '2', name: '输出信号' },
      { id: '3', name: '计算信号' }
    ]
    
    channelTypeArr.value = [
      { id: '1', name: 'AI' },
      { id: '2', name: 'DI' },
      { id: '3', name: 'AO' },
      { id: '4', name: 'DO' }
    ]
    
    dataTypeArr.value = [
      { id: '1', name: 'REAL' },
      { id: '2', name: 'INT' },
      { id: '3', name: 'BOOL' }
    ]
    
    signalPropertyArr.value = [
      { id: '1', name: '重要信号' },
      { id: '2', name: '报警信号' },
      { id: '3', name: '统计信号' }
    ]
  }
}

const loadBatteryDeviceData = async () => {
  try {
    console.log('正在获取电池设备类别数据')
    
    // 使用静态的电池设备类型配置，与Angular版本保持一致
    // 根据设备属性枚举，电池设备包括24V和48V电池
    batteryData.value = {
      6: true,  // 24伏蓄电池
      7: true   // 48伏蓄电池
    }
    
    console.log('电池设备数据加载成功:', batteryData.value)
  } catch (error) {
    console.error('获取电池设备数据失败:', error)
    batteryData.value = null
  }
}

const loadSignalList = async () => {
  if (!props.template?.id) {
    console.warn('模板ID未提供，无法加载信号列表. template:', props.template)
    tableData.value = []
    return
  }
  
  try {
    console.log('正在获取信号列表，模板ID:', props.template.id)
    
    const result = await getSignalListByTemplate(props.template.id)
    
    console.log('信号列表API响应:', result)
    
    // 根据记忆，code: 0表示成功状态
    if (result.code === 0 && result.data) {
      const processedData = result.data.map((item: any) => {
        // 处理状态信号列表，生成状态值显示字符串
        let stateValue = ''
        if (item.signalMeaningsList && item.signalMeaningsList.length > 0) {
          stateValue = item.signalMeaningsList
            .map((meaning: any) => `${meaning.stateValue}:${meaning.meanings}`)
            .join(' / ')
        }
        
        // 处理信号属性列表
        let signalPropertyIds: number[] = []
        let signalProperty = ''
        if (item.signalPropertyList && item.signalPropertyList.length > 0) {
          signalPropertyIds = item.signalPropertyList.map((prop: any) => prop.signalPropertyId)
          signalProperty = signalPropertyIds.join(';')
        }
        
        // 转换数据结构，处理null值和类型转换
        const signalData: SignalData = {
          id: item.id,
          equipmentTemplateId: item.equipmentTemplateId,
          signalId: item.signalId,
          signalName: item.signalName || '',
          displayIndex: item.displayIndex || 0,
          signalCategory: item.signalCategory || 0,
          signalType: item.signalType || 0,
          channelNo: item.channelNo !== null ? item.channelNo : 0,
          channelType: item.channelType || 0,
          expression: item.expression || '',
          dataType: getDataTypeString(item.dataType),
          showPrecision: item.showPrecision || '0',
          unit: item.unit || '',
          storeInterval: item.storeInterval?.toString() || '0',
          absValueThreshold: item.absValueThreshold || 0,
          percentThreshold: item.percentThreshold || 0,
          staticsPeriod: item.staticsPeriod || 0,
          enable: item.enable !== false, // 默认true
          visible: item.visible !== false, // 默认true
          chargeStoreInterVal: item.chargeStoreInterVal || undefined,
          chargeAbsValue: item.chargeAbsValue || undefined,
          description: item.description || '',
          signalProperty: signalProperty,
          signalPropertyIds: signalPropertyIds,
          moduleNo: item.moduleNo || 0,
          baseTypeId: item.baseTypeId,
          baseTypeName: item.baseTypeName || '',
          stateValue: stateValue,
          signalMeaningsList: item.signalMeaningsList || [],
          signalPropertyList: item.signalPropertyList || [],
          acrossSignal: item.acrossSignal || false,
          hasInstance: false // 初始化为false
        }
        
        return signalData
      })
      
      tableData.value = processedData
    } else {
      ElMessage.error('获取信号列表失败: ' + (result.msg || result.message || '未知错误'))
      tableData.value = []
    }
  } catch (error) {
    console.error('获取信号列表失败:', error)
    ElMessage.error('获取信号列表失败，请检查网络连接')
    tableData.value = []
  }
}

// 数据类型转换辅助函数
const getDataTypeString = (dataType: any): string => {
  if (dataType === null || dataType === undefined) return ''
  
  // 根据数字映射到字符串类型
  const dataTypeMap: { [key: number]: string } = {
    0: 'BOOL',
    1: 'STRING', 
    2: 'INT',
    3: 'REAL'
  }
  
  return dataTypeMap[dataType] || dataType.toString()
}

const getCellStyle = (row: SignalData) => {
  const style: any = {}
  
  // 跨站信号显示蓝色
  if (row.acrossSignal) {
    style.color = '#0000FF'
  }
  
  // 已设置实例显示绿色背景
  if (row.hasInstance) {
    style.backgroundColor = '#B0F47D'
  }
  
  return style
}

// 监听props变化
watch(() => props.active, (newVal) => {
  console.log('DeviceSignal active变化:', newVal, 'tabIndex:', props.tabIndex, 'template:', props.template)
  if (newVal && props.tabIndex === 1) {
    console.log('开始初始化数据 - active变化')
    initData()
  }
}, { immediate: true })

watch(() => props.tabIndex, (newVal) => {
  console.log('DeviceSignal tabIndex变化:', newVal, 'active:', props.active, 'template:', props.template)
  if (newVal === 1 && props.active) {
    console.log('开始初始化数据 - tabIndex变化')
    initData()
  }
}, { immediate: true })

watch(() => props.template, (newVal) => {
  console.log('DeviceSignal template变化:', newVal, 'active:', props.active, 'tabIndex:', props.tabIndex)
  if (newVal && props.active && props.tabIndex === 1) {
    console.log('开始初始化数据 - template变化')
    initData()
  }
})

// 综合监听：当所有必要条件都满足时，初始化数据
watch([() => props.active, () => props.tabIndex, () => props.template, () => props.equipmentId], 
  ([active, tabIndex, template, equipmentId]) => {
    console.log('综合监听触发:', { active, tabIndex, template, equipmentId })
    if (active && tabIndex === 1 && template?.id && equipmentId) {
      console.log('所有条件满足，开始初始化数据')
      initData()
    }
  }
)

// 组件挂载
onMounted(() => {
  console.log('DeviceSignal组件挂载:', {
    active: props.active,
    tabIndex: props.tabIndex,
    template: props.template,
    equipmentId: props.equipmentId
  })
  
  // 初始化过滤器状态
  initFilterState()
  
  if (props.active && props.tabIndex === 1) {
    console.log('开始初始化数据 - 组件挂载')
    initData()
  }
})
</script>

<style scoped>
.device-signal-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow: auto;
}

:deep(.el-table .el-table__cell) {
  padding: 4px 0;
}

/* 文本颜色样式 */
.text-green-600 {
  color: #16a085;
  font-weight: 500;
}

.text-red-600 {
  color: #e74c3c;
  font-weight: 500;
}

.state-text {
  font-size: 12px;
  line-height: 1.4;
  word-break: break-all;
  display: block;
  max-height: 40px;
  overflow: hidden;
}

.placeholder-text {
  color: #999;
  font-style: italic;
}

.disabled-text {
  color: #ccc;
}

.state-signal-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  max-width: 240px;
}

.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 2px;
}

/* Tag样式优化 */
.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.state-tag {
  font-size: 11px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.placeholder-tag {
  color: #999;
  background-color: #f5f7fa;
  border-color: #dcdfe6;
}

.disabled-tag {
  color: #ccc;
  background-color: #f0f0f0;
  border-color: #e0e0e0;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}
</style> 