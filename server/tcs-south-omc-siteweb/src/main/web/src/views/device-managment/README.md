# 设备管理页面

## 概述

这个设备管理页面是基于Angular版本重新实现的Vue.js版本，提供了完整的设备信息管理功能。

## 文件结构

```
device-managment/
├── index.vue                    # 主页面入口
├── components/                  # 子组件目录
│   ├── DeviceInfo.vue          # 设备信息组件
│   ├── DeviceSignal.vue        # 信号管理组件
│   ├── DeviceEvent.vue         # 事件管理组件
│   ├── DeviceControl.vue       # 控制管理组件
│   └── DeviceLog.vue           # 变更记录组件
├── todo.md                     # 功能分析文档
└── README.md                   # 使用说明
```

## 路由参数

页面通过以下路由参数访问：

- **路径**: `/siteweb-omc/device-managment/:id?`
- **参数**:
  - `id`: 设备ID (可选，默认为731000026)
  - `title`: 设备名称 (可选，显示在页面标题)

### 示例路由
```
/siteweb-omc/device-managment/12345?title=测试设备
/siteweb-omc/device-managment  # 使用默认设备ID 731000026
```

## 功能特性

### 1. 主页面 (index.vue)
- 响应式设计的页面布局
- 面包屑导航，支持返回上一页
- 5个Tab页面：设备信息、信号、事件、控制、变更记录
- 模板管理跳转功能

### 2. 设备信息 (DeviceInfo.vue)
- 设备基本信息的查看和编辑
- 表单验证
- 数据保存功能
- 加载状态显示

### 3. 其他组件
- 信号管理：显示监控单元类型等信息
- 事件管理：显示监控单元类型等信息
- 控制管理：基础框架
- 变更记录：显示对象类型信息

## API接口

当前使用模拟数据，主要接口包括：

- `getDeviceInfo(equipmentId)`: 获取设备信息
- `updateDeviceInfo(data)`: 更新设备信息
- `getMonitorUnitInfo(muId)`: 获取监控单元信息

## 使用方法

### 1. 路由配置
当前路由配置在 `router/modules/home.ts` 中：

```typescript
{
  path: "/siteweb-omc/device-managment/:id?",
  name: "SitewebOmcDeviceManagment",
  component: () => import("@/views/device-managment/index.vue"),
  meta: {
    icon: "ic:outline-monitor-heart",
    title: "设备管理",
    showParent: true
  }
}
```

### 2. 页面跳转
```typescript
// 跳转到设备管理页面
router.push({
  path: `/siteweb-omc/device-managment/${equipmentId}`,
  query: {
    title: deviceName
  }
})

// 或者跳转到默认设备
router.push('/siteweb-omc/device-managment')
```

### 3. API接口集成
修改 `@/api/device-management.ts` 文件，将模拟数据替换为真实的API调用：

```typescript
// 替换模拟实现为真实API调用
export const getDeviceInfo = async (equipmentId: string): Promise<DeviceInfo> => {
  return http.get<DeviceInfo, any>(`/api/config/equipment/${equipmentId}`)
}
```

## 技术栈

- **Vue 3**: 使用Composition API
- **TypeScript**: 类型安全
- **Element Plus**: UI组件库
- **Vue Router**: 路由管理
- **响应式设计**: 支持移动端

## 开发注意事项

1. **路由参数验证**: 页面会检查设备ID是否存在，缺失时自动返回上一页
2. **加载状态**: 所有API调用都有相应的loading状态
3. **错误处理**: 完善的错误提示和异常处理
4. **Tab激活**: 只有当Tab激活时才会加载对应的数据
5. **响应式**: 支持桌面端和移动端的不同布局

## 后续扩展

- 完善信号管理功能
- 完善事件管理功能
- 添加控制管理功能
- 添加变更记录功能
- 集成真实的API接口
- 添加权限控制
- 添加数据导出功能 