# 设备管理模块功能分析

## 功能概述

该模块是一个完整的设备管理系统，提供设备信息管理、信号配置、事件配置、控制管理等功能。主要包含以下几个核心组件：

## 主要功能模块

### 1. 设备管理主界面 (device-management)

**功能描述：**
- 提供设备管理的主入口界面
- 包含5个Tab页面：设备信息、信号、事件、控制、变更记录
- 支持跳转到设备模板管理页面

**用户操作逻辑：**
1. 进入设备管理页面，默认显示设备信息Tab
2. 点击不同Tab可切换到对应功能页面
3. 点击右上角"模板管理"按钮可跳转到设备模板页面

**相关API接口：**
- `GET /api/config/equipment/{equipmentId}` - 获取设备信息
- `GET /api/config/monitor-unit/config/{muId}` - 获取监控单元信息

### 2. 设备信息管理 (device-info)

**功能描述：**
- 设备详细信息的查看和编辑
- 包含设备基本信息、分类信息、资产信息、工程信息等
- 支持告警过滤表达式配置
- 支持设备属性、厂商、状态等下拉选择

**用户操作逻辑：**
1. 页面自动加载当前设备信息
2. 用户可编辑各项设备属性
3. 点击"告警过滤表达式"输入框打开表达式配置弹窗
4. 点击"保存"按钮提交修改

**相关API接口：**
- `GET /api/config/equipment/{equipmentId}` - 获取设备信息
- `PUT /api/config/equipment/config` - 更新设备信息
- `GET /api/config/equipment/simplifyequipments` - 获取上级设备列表
- `GET /api/config/dataitems/{entryId}` - 获取数据字典
- `GET /api/config/equipmentbasetype/list` - 获取设备基类

### 3. 跨站信号配置 (cross-site-signal)

**功能描述：**
- 配置跨监控单元的信号表达式
- 显示信号的完整路径信息（局站→监控单元→设备→信号）
- 支持从测点和从指标两种表达式配置方式

**用户操作逻辑：**
1. 弹窗显示信号的基本信息（只读）
2. 点击"从测点"按钮打开测点表达式配置
3. 点击"从指标"按钮打开指标表达式配置
4. 配置完成后点击"确定"保存

**相关API接口：**
- `GET /api/config/signal/crossmonitorunitsignal/condition` - 获取跨站信号信息
- `POST /api/config/signal/createacrossmonitorunitsignal` - 创建/更新跨站信号

### 4. 设备事件实例管理 (device-event-instance)

**功能描述：**
- 配置设备事件的开始表达式和抑制表达式
- 显示事件的完整路径信息
- 支持表达式的可视化配置

**用户操作逻辑：**
1. 弹窗显示事件基本信息（只读）
2. 点击"开始表达式"输入框配置开始条件
3. 点击"抑制表达式"输入框配置抑制条件
4. 点击"确定"保存配置

**相关API接口：**
- `GET /api/config/monitorunitevent/condition` - 获取事件实例信息
- `POST /api/config/monitorunitevent` - 保存事件实例配置

### 5. 设备事件实例列表 (device-event-instance-list)

**功能描述：**
- 显示设备下所有事件实例列表
- 支持按列搜索过滤
- 支持批量选择和删除
- 表格列可调整大小

**用户操作逻辑：**
1. 列表显示所有事件实例
2. 在表头搜索框中输入关键词进行过滤
3. 勾选需要删除的事件实例
4. 点击"确定"执行批量删除

**相关API接口：**
- `GET /api/config/monitorunitevent/condition?equipmentId={eqId}` - 获取事件实例列表
- `DELETE /api/config/monitorunitevent` - 批量删除事件实例

### 6. 设备信号实例管理 (device-signal-instance)

**功能描述：**
- 配置设备信号的采集方式
- 支持三种配置模式：按引用采集单元、按信号表达式、按引用采集单元及信号表达式
- 支持选择引用的采集单元和通道号

**用户操作逻辑：**
1. 选择配置模式（单选按钮）
2. 根据选择的模式配置相应参数：
   - 模式1：选择采集单元 + 输入通道号
   - 模式2：配置信号表达式
   - 模式3：同时配置采集单元、通道号和表达式
3. 点击"确定"保存配置

**相关API接口：**
- `GET /api/config/monitorunitsignal/condition` - 获取信号实例信息
- `GET /api/config/sampler-unit/samplerunitwithport` - 获取采集单元列表
- `POST /api/config/monitorunitsignal` - 保存信号实例配置

### 7. 设备信号实例列表 (device-signal-instance-list)

**功能描述：**
- 显示设备下所有信号实例列表
- 支持按列搜索过滤
- 支持批量选择和删除
- 显示信号的引用采集单元、通道号、表达式等信息

**用户操作逻辑：**
1. 列表显示所有信号实例
2. 在表头搜索框中输入关键词进行过滤
3. 勾选需要删除的信号实例
4. 点击"确定"执行批量删除

**相关API接口：**
- `GET /api/config/monitorunitsignal/condition?equipmentId={eqId}` - 获取信号实例列表
- `DELETE /api/config/monitorunitsignal` - 批量删除信号实例

## 完整API接口清单

### 设备管理相关
- `GET /api/config/equipment/tree` - 获取设备树
- `GET /api/config/equipment/{equipmentId}` - 获取设备详情
- `PUT /api/config/equipment/config` - 更新设备配置
- `GET /api/config/equipment/simplifyequipments` - 获取简化设备列表
- `POST /api/config/equipment/instance` - 创建设备实例

### 监控单元相关
- `GET /api/config/monitor-unit/config/{muId}` - 获取监控单元信息

### 事件实例相关
- `GET /api/config/monitorunitevent/condition` - 获取事件实例（支持设备ID和事件ID参数）
- `POST /api/config/monitorunitevent` - 创建/更新事件实例
- `DELETE /api/config/monitorunitevent` - 删除事件实例

### 信号实例相关
- `GET /api/config/monitorunitsignal/condition` - 获取信号实例（支持设备ID和信号ID参数）
- `POST /api/config/monitorunitsignal` - 创建/更新信号实例
- `DELETE /api/config/monitorunitsignal` - 删除信号实例

### 跨站信号相关
- `GET /api/config/signal/crossmonitorunitsignal/condition` - 获取跨站信号信息
- `POST /api/config/signal/createacrossmonitorunitsignal` - 创建跨站信号

### 采集单元相关
- `GET /api/config/sampler-unit/samplerunitwithport` - 获取带端口的采集单元列表

### 基础数据相关
- `GET /api/config/dataitems/{entryId}` - 获取数据字典项
- `GET /api/config/equipmentbasetype/list` - 获取设备基类列表
- `GET /api/config/dataitems/equipmentcategorys` - 获取设备分类
