<template>
  <div class="protocol-management-container min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center mb-4">
        <div class="w-1 h-8 bg-primary rounded-full mr-4" />
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            协议管理
          </h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            管理通信协议的导入、查看、编辑、删除以及动态库文件管理
          </p>
        </div>
      </div>

      <!-- 工具栏 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <!-- 操作按钮 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <el-button type="primary" @click="showImportDialog" :disabled="isLoading">
              <el-icon size="16" class="mr-2"><Upload /></el-icon>
              增加协议
            </el-button>
            <el-button 
              type="danger" 
              plain
              :disabled="selectedIds.length === 0 || isLoading"
              @click="handleBatchDelete"
            >
              <el-icon size="16" class="mr-2"><Delete /></el-icon>
              删除协议
            </el-button>
          </div>
          <span class="text-sm text-gray-600 dark:text-gray-400">
            共 {{ filteredData.length }} 个协议
          </span>
        </div>
      </div>
    </div>

    <!-- 协议列表表格 -->
    <div
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
    >
      <div style="height: 600px">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              v-loading="isLoading"
              :columns="tableColumns"
              :data="filteredData"
              :width="width"
              :height="height"
              fixed
              :row-height="60"
              @row-select="handleRowSelect"
            />
          </template>
        </el-auto-resizer>
      </div>
    </div>

    <!-- 协议导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入模板"
      width="640px"
      :close-on-click-modal="false"
    >
      <div class="space-y-6">
        <!-- 警告提示 -->
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          动态库上传后，下发配置时会连同动态库一起下发，可能覆盖原有动态库文件，请谨慎操作！
        </div>
        
        <!-- 协议模板上传 -->
        <div>
          <div class="mb-3">
            <span class="text-sm font-medium text-gray-700">选择模板文件</span>
            <span class="text-red-500 ml-1">*</span>
          </div>
          <el-upload
            class="w-full"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleTemplateFileChange"
            :show-file-list="false"
            accept=".xml"
          >
            <div class="py-12 px-6 text-center">
              <el-icon size="48" class="text-gray-400 mb-4">
                <UploadFilled />
              </el-icon>
              <div class="text-lg text-gray-600 dark:text-gray-400 mb-2">
                请选择文件...
              </div>
              <div class="text-sm text-gray-400">仅支持 .xml 文件</div>
            </div>
          </el-upload>
          <div v-if="importForm.templateFile" class="mt-3 flex items-center text-green-600">
            <el-icon class="mr-2"><Check /></el-icon>
            <span>已选择文件：{{ importForm.templateFile.name }}</span>
          </div>
        </div>

        <!-- 动态库上传区域 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="mb-4">
            <span class="text-sm font-medium text-gray-700">选择动态库文件</span>
            <span class="text-sm text-gray-500 ml-2">（可选，至少选择一个）</span>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 335X动态库上传 -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">335X动态库文件</label>
              <el-upload
                action="#"
                :auto-upload="false"
                :on-change="(file) => handleDllFileChange(file, '335X')"
                :show-file-list="false"
                accept=".dll,.so"
              >
                <el-button type="primary" plain class="w-full">
                  <el-icon class="mr-2"><Upload /></el-icon>
                  选择335X文件
                </el-button>
              </el-upload>
              <div v-if="importForm.dll335XFile" class="text-sm text-green-600 flex items-center">
                <el-icon class="mr-1"><Check /></el-icon>
                {{ importForm.dll335XFile.name }}
              </div>
            </div>

            <!-- 9200动态库上传 -->
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-600">9200动态库文件</label>
              <el-upload
                action="#"
                :auto-upload="false"
                :on-change="(file) => handleDllFileChange(file, '9200')"
                :show-file-list="false"
                accept=".dll,.so"
              >
                <el-button type="primary" plain class="w-full">
                  <el-icon class="mr-2"><Upload /></el-icon>
                  选择9200文件
                </el-button>
              </el-upload>
              <div v-if="importForm.dll9200File" class="text-sm text-green-600 flex items-center">
                <el-icon class="mr-1"><Check /></el-icon>
                {{ importForm.dll9200File.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="isUploading"
            :disabled="!importForm.templateFile || (!importForm.dll335XFile && !importForm.dll9200File)"
            @click="confirmImport"
          >
            {{ isUploading ? "上传中..." : "确认" }}
          </el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 动态库上传对话框 -->
    <el-dialog
      v-model="dllUploadDialogVisible"
      title="导入动态库"
      width="600px"
    >
      <el-form label-width="140px">
        <!-- 335X动态库上传 -->
        <el-form-item label="选择335X动态库文件">
          <div class="w-full">
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :on-change="(file) => handleSingleDllFileChange(file, '1')"
              :show-file-list="false"
              accept=".dll,.so"
            >
              <el-button type="primary" plain>
                <el-icon class="mr-2"><Upload /></el-icon>
                请选择文件...
              </el-button>
            </el-upload>
            <div
              v-if="singleDllForm.dll335XFile"
              class="mt-2 text-green-600"
            >
              *已选择
            </div>
          </div>
        </el-form-item>

        <!-- 9200动态库上传 -->
        <el-form-item label="选择9200动态库文件">
          <div class="w-full">
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :on-change="(file) => handleSingleDllFileChange(file, '2')"
              :show-file-list="false"
              accept=".dll,.so"
            >
              <el-button type="primary" plain>
                <el-icon class="mr-2"><Upload /></el-icon>
                请选择文件...
              </el-button>
            </el-upload>
            <div
              v-if="singleDllForm.dll9200File"
              class="mt-2 text-green-600"
            >
              *已选择
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="dllUploadDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="isUploading"
            :disabled="!singleDllForm.dll335XFile && !singleDllForm.dll9200File"
            @click="confirmDllUpload"
          >
            {{ isUploading ? "上传中..." : "确认" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设备种类设置对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      title="为协议中的模板设置标准设备种类"
      width="500px"
    >
      <el-form label-width="120px">
        <el-form-item :label="categoryTip">
          <el-select
            v-model="selectedStandardType"
            placeholder="请选择标准设备种类"
            style="width: 300px"
          >
            <el-option
              v-for="item in standardTypes"
              :key="item.equipmentCategory"
              :label="item.equipmentCategoryName"
              :value="item.equipmentCategory"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="categoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmStandardType">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, reactive, nextTick, withKeys } from "vue";
import {
  Plus,
  Search,
  Delete,
  Document,
  Upload,
  UploadFilled,
  Filter,
  Check
} from "@element-plus/icons-vue";
import { 
  ElMessage, 
  ElMessageBox,
  ElButton,
  ElCheckbox,
  ElIcon,
  ElPopover,
  TableV2FixedDir,
  ElInput,
  ElSelect,
  ElOption,
  ElTag
} from "element-plus";
import type { HeaderCellSlotProps, InputInstance, Column, FunctionalComponent } from "element-plus";
import {
  getProtocolList,
  updateProtocol,
  deleteProtocolByIds,
  uploadProtocolTemplate,
  uploadDllFile,
  updateDeviceCategory,
  deleteDllFile,
  getDeviceModels,
  getDeviceCategoryMap,
  type ProtocolInfo,
  type DeviceModel,
  type DeviceCategoryMap
} from "@/api/protocol";

defineOptions({
  name: "ProtocolManagement"
});

// 状态定义
const isLoading = ref(false);
const isUploading = ref(false);
const selectedIds = ref<string[]>([]);
const tableData = ref<ProtocolInfo[]>([]);
const deviceModelList = ref<DeviceModel[]>([]);

// 筛选状态
const nameFilter = ref('');
const pathFilter = ref('');
const templateFilter = ref('');
const statusFilter = ref<string[]>([]);
const modelFilter = ref<string[]>([]);
const popoverRefs = reactive({});

// 编辑状态管理
const editingOriginalValues = reactive({});



// 导入对话框
const importDialogVisible = ref(false);
const importForm = reactive({
  templateFile: null as File | null,
  dll335XFile: null as File | null,
  dll9200File: null as File | null
});

// 单独动态库上传
const dllUploadDialogVisible = ref(false);
const currentProtocolForDll = ref<ProtocolInfo | null>(null);
const singleDllForm = reactive({
  dll335XFile: null as File | null,
  dll9200File: null as File | null
});



// 设备种类设置
const categoryDialogVisible = ref(false);
const standardTypes = ref<DeviceCategoryMap[]>([]);
const selectedStandardType = ref("");
const categoryTip = ref("");
const currentEquipmentTemplateId = ref("");



// 生命周期
onMounted(() => {
  getDeviceModelList();
  getProtocolListData();
});

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;
  
  // 应用筛选过滤
  if (nameFilter.value) {
    data = data.filter(item => 
      item.samplerName?.toLowerCase().includes(nameFilter.value.toLowerCase())
    );
  }
  if (pathFilter.value) {
    data = data.filter(item => 
      item.dllPath?.toLowerCase().includes(pathFilter.value.toLowerCase())
    );
  }
  if (templateFilter.value) {
    data = data.filter(item => 
      item.equipmentTemplateName?.toLowerCase().includes(templateFilter.value.toLowerCase())
    );
  }
  if (statusFilter.value.length > 0) {
    data = data.filter(item => statusFilter.value.includes(item.updateState));
  }
  if (modelFilter.value.length > 0) {
    data = data.filter(item => modelFilter.value.includes(item.modal));
  }
  
  return data;
});

// 编辑单元格组件
type EditCellProps = {
  value: string
  onChange: (value: string) => void
  onBlur: () => void
  onKeydownEnter: () => void
  onKeydownEsc?: () => void
  forwardRef: (el: InputInstance) => void
}

const EditCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  onKeydownEsc,
  forwardRef,
}) => {
  return (
    <ElInput
      ref={forwardRef as any}
      modelValue={value}
      onInput={onChange}
      onBlur={onBlur}
      onKeydown={(e: KeyboardEvent) => {
        if (e.key === 'Enter') {
          onKeydownEnter();
        } else if (e.key === 'Escape' && onKeydownEsc) {
          onKeydownEsc();
        }
      }}
      size="small"
    />
  )
}

// 筛选弹出框内容
const FilterPopover = ({ 
  type, 
  options, 
  selected, 
  onConfirm, 
  onReset 
}: {
  type: string,
  options: any[],
  selected: string[],
  onConfirm: () => void,
  onReset: () => void
}) => {
  return (
    <div class="filter-wrapper">
      <div class="filter-group space-y-2 max-h-40 overflow-auto">
        {options.map(option => (
          <ElCheckbox
            key={option.value || option.itemValue}
            modelValue={selected.includes(option.value || option.itemValue)}
            onChange={(checked: boolean) => {
              const value = option.value || option.itemValue;
              if (checked) {
                if (!selected.includes(value)) {
                  selected.push(value);
                }
              } else {
                const index = selected.indexOf(value);
                if (index > -1) {
                  selected.splice(index, 1);
                }
              }
            }}
          >
            {option.label || option.itemValue}
          </ElCheckbox>
        ))}
      </div>
      <div class="el-table-v2__demo-filter">
        <ElButton text onClick={onConfirm}>
          确认
        </ElButton>
        <ElButton text onClick={onReset}>
          重置
        </ElButton>
      </div>
    </div>
  )
}

// 表格列配置
const tableColumns = computed<Column<ProtocolInfo>[]>(() => [
  // 选择列
  {
    key: 'selection',
    width: 60,
    cellRenderer: ({ rowData }) => (
      <ElCheckbox
        modelValue={selectedIds.value.includes(rowData.samplerId)}
        onChange={(checked: boolean) => {
          if (checked) {
            if (!selectedIds.value.includes(rowData.samplerId)) {
              selectedIds.value.push(rowData.samplerId);
            }
          } else {
            const index = selectedIds.value.indexOf(rowData.samplerId);
            if (index > -1) {
              selectedIds.value.splice(index, 1);
            }
          }
        }}
      />
    ),
    headerCellRenderer: () => (
      <ElCheckbox
        modelValue={selectedIds.value.length === filteredData.value.length && filteredData.value.length > 0}
        indeterminate={selectedIds.value.length > 0 && selectedIds.value.length < filteredData.value.length}
        onChange={(checked: boolean) => {
          if (checked) {
            selectedIds.value = filteredData.value.map(item => item.samplerId);
          } else {
            selectedIds.value = [];
          }
        }}
      />
    )
  },
  // 协议名称
  {
    key: 'samplerName',
    title: '协议名称',
    dataKey: 'samplerName',
    width: 200,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: string) => {
        rowData.samplerName = value;
      };
      const onEnterEditMode = () => {
        // 保存原始值
        if (!editingOriginalValues[rowData.samplerId]) {
          editingOriginalValues[rowData.samplerId] = {};
        }
        editingOriginalValues[rowData.samplerId].samplerName = rowData.samplerName;
        rowData.nameEdit = true;
      };
      const onExitEditMode = async () => {
        rowData.nameEdit = false;
        await confirmChange(rowData, 'samplerName', 'nameEdit');
      };
      const onCancelEdit = () => {
        // 恢复原始值
        if (editingOriginalValues[rowData.samplerId]) {
          rowData.samplerName = editingOriginalValues[rowData.samplerId].samplerName;
          delete editingOriginalValues[rowData.samplerId].samplerName;
        }
        rowData.nameEdit = false;
      };
      const input = ref();
      const setRef = (el: any) => {
        input.value = el;
        if (el) {
          el.focus?.();
        }
      };

      return rowData.nameEdit ? (
        <EditCell
          forwardRef={setRef}
          value={rowData.samplerName}
          onChange={onChange}
          onBlur={onExitEditMode}
          onKeydownEnter={onExitEditMode}
          onKeydownEsc={onCancelEdit}
        />
      ) : (
        <div class="flex items-center cursor-pointer table-v2-inline-editing-trigger" onClick={onEnterEditMode}>
          <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
            <ElIcon size={14} class="text-white">
              <Document />
            </ElIcon>
          </div>
          <span class="font-medium">{rowData.samplerName}</span>
        </div>
      );
    },
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">协议名称</span>
        <ElInput
          modelValue={nameFilter.value}
          onInput={(value: string) => nameFilter.value = value}
          placeholder="搜索..."
          size="small"
          style="width: 120px"
          clearable
        />
      </div>
    )
  },
  // 动态库路径
  {
    key: 'dllPath',
    title: '动态库路径',
    dataKey: 'dllPath',
    width: 250,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: string) => {
        rowData.dllPath = value;
      };
      const onEnterEditMode = () => {
        // 保存原始值
        if (!editingOriginalValues[rowData.samplerId]) {
          editingOriginalValues[rowData.samplerId] = {};
        }
        editingOriginalValues[rowData.samplerId].dllPath = rowData.dllPath;
        rowData.dllEdit = true;
      };
      const onExitEditMode = async () => {
        rowData.dllEdit = false;
        await confirmChange(rowData, 'dllPath', 'dllEdit');
      };
      const onCancelEdit = () => {
        // 恢复原始值
        if (editingOriginalValues[rowData.samplerId]) {
          rowData.dllPath = editingOriginalValues[rowData.samplerId].dllPath;
          delete editingOriginalValues[rowData.samplerId].dllPath;
        }
        rowData.dllEdit = false;
      };
      const input = ref();
      const setRef = (el: any) => {
        input.value = el;
        if (el) {
          el.focus?.();
        }
      };

      return rowData.dllEdit ? (
        <EditCell
          forwardRef={setRef}
          value={rowData.dllPath}
          onChange={onChange}
          onBlur={onExitEditMode}
          onKeydownEnter={onExitEditMode}
          onKeydownEsc={onCancelEdit}
        />
      ) : (
        <div 
          class="cursor-pointer table-v2-inline-editing-trigger" 
          onClick={onEnterEditMode}
          title={rowData.dllPath}
        >
          {rowData.dllPath}
        </div>
      );
    },
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">动态库路径</span>
        <ElInput
          modelValue={pathFilter.value}
          onInput={(value: string) => pathFilter.value = value}
          placeholder="搜索..."
          size="small"
          style="width: 120px"
          clearable
        />
      </div>
    )
  },
  // 关联模板
  {
    key: 'equipmentTemplateName',
    title: '关联模板',
    dataKey: 'equipmentTemplateName',
    width: 250,
    cellRenderer: ({ rowData }) => (
      <span>{rowData.equipmentTemplateName}</span>
    ),
    headerCellRenderer: () => (
      <div class="flex items-center justify-between">
        <span class="mr-2 text-sm font-medium">关联模板</span>
        <ElInput
          modelValue={templateFilter.value}
          onInput={(value: string) => templateFilter.value = value}
          placeholder="搜索..."
          size="small"
          style="width: 120px"
          clearable
        />
      </div>
    )
  },
  // 更新状态
  {
    key: 'updateState',
    title: '更新状态',
    dataKey: 'updateState',
    width: 150,
    cellRenderer: ({ rowData }) => (
      <ElTag
        type={rowData.updateState === '已上传' ? 'success' : 'warning'}
        effect="light"
      >
        {rowData.updateState}
      </ElTag>
    ),
    headerCellRenderer: () => {
      const popoverRef = ref();
      const tempSelected = ref<string[]>([...statusFilter.value]);
      
      const onFilter = () => {
        statusFilter.value = [...tempSelected.value];
        popoverRef.value?.hide();
      };
      
      const onReset = () => {
        tempSelected.value = [];
        statusFilter.value = [];
        popoverRef.value?.hide();
      };

      return (
        <div class="flex items-center justify-between">
          <span class="mr-2 text-sm font-medium">更新状态</span>
          <ElPopover ref={popoverRef} trigger="click" width={200}>
            {{
              default: () => (
                <FilterPopover
                  type="status"
                  options={[
                    { label: '已上传', value: '已上传' },
                    { label: '未上传', value: '未上传' }
                  ]}
                  selected={tempSelected.value}
                  onConfirm={onFilter}
                  onReset={onReset}
                />
              ),
              reference: () => (
                <ElIcon class="cursor-pointer">
                  <Filter />
                </ElIcon>
              ),
            }}
          </ElPopover>
        </div>
      );
    }
  },
  // 设备型号
  {
    key: 'modal',
    title: '设备型号',
    dataKey: 'modal',
    width: 150,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: string) => {
        rowData.samplerType = value;
        const modelIndex = deviceModelList.value.findIndex(
          model => model.itemId === value
        );
        rowData.modal = modelIndex >= 0 ? deviceModelList.value[modelIndex].itemValue : '';
      };
      const onEnterEditMode = () => {
        // 保存原始值
        if (!editingOriginalValues[rowData.samplerId]) {
          editingOriginalValues[rowData.samplerId] = {};
        }
        editingOriginalValues[rowData.samplerId].modal = rowData.modal;
        editingOriginalValues[rowData.samplerId].samplerType = rowData.samplerType;
        rowData.modalEdit = true;
      };
      const onExitEditMode = async () => {
        rowData.modalEdit = false;
        await confirmChange(rowData, 'modal', 'modalEdit');
      };
      const onCancelEdit = () => {
        // 恢复原始值
        if (editingOriginalValues[rowData.samplerId]) {
          rowData.modal = editingOriginalValues[rowData.samplerId].modal;
          rowData.samplerType = editingOriginalValues[rowData.samplerId].samplerType;
          delete editingOriginalValues[rowData.samplerId].modal;
          delete editingOriginalValues[rowData.samplerId].samplerType;
        }
        rowData.modalEdit = false;
      };

      return rowData.modalEdit ? (
        <ElSelect
          modelValue={rowData.samplerType}
          onChange={onChange}
          onBlur={onExitEditMode}
          onKeydown={(e: KeyboardEvent) => {
            if (e.key === 'Escape') {
              onCancelEdit();
            }
          }}
          size="small"
          style="width: 120px"
        >
          {deviceModelList.value.map(item => (
            <ElOption
              key={item.itemId}
              label={item.itemValue}
              value={item.itemId}
            />
          ))}
        </ElSelect>
      ) : (
        <div class="cursor-pointer table-v2-inline-editing-trigger" onClick={onEnterEditMode}>
          {rowData.modal}
        </div>
      );
    },
    headerCellRenderer: () => {
      const popoverRef = ref();
      const tempSelected = ref<string[]>([...modelFilter.value]);
      
      const onFilter = () => {
        modelFilter.value = [...tempSelected.value];
        popoverRef.value?.hide();
      };
      
      const onReset = () => {
        tempSelected.value = [];
        modelFilter.value = [];
        popoverRef.value?.hide();
      };

      return (
        <div class="flex items-center justify-between">
          <span class="mr-2 text-sm font-medium">设备型号</span>
          <ElPopover ref={popoverRef} trigger="click" width={200}>
            {{
              default: () => (
                <FilterPopover
                  type="model"
                  options={deviceModelList.value.map(item => ({ 
                    label: item.itemValue, 
                    itemValue: item.itemValue 
                  }))}
                  selected={tempSelected.value}
                  onConfirm={onFilter}
                  onReset={onReset}
                />
              ),
              reference: () => (
                <ElIcon class="cursor-pointer">
                  <Filter />
                </ElIcon>
              ),
            }}
          </ElPopover>
        </div>
      );
    }
  },
  // 操作列
  {
    key: 'actions',
    title: '操作',
    width: 400,
    cellRenderer: ({ rowData }) => (
      <div class="flex items-center space-x-2">
        <ElButton
          size="small"
          type="primary"
          text
          onClick={() => uploadDLLorSO(rowData)}
        >
          动态库上传
        </ElButton>
        <ElButton
          size="small"
          type="danger"
          text
          onClick={() => deleteDLLorSO(rowData, '1')}
        >
          335X动态库删除
        </ElButton>
        <ElButton
          size="small"
          type="danger"
          text
          onClick={() => deleteDLLorSO(rowData, '2')}
        >
          9200动态库删除
        </ElButton>
      </div>
    )
  }
]);

// 获取协议列表
const getProtocolListData = async () => {
  isLoading.value = true;
  try {
    const res = await getProtocolList();
    if (res.code === 0) {
      const protocols = res.data;
      // 处理数据，添加更新状态
      protocols.forEach((item: ProtocolInfo) => {
        const modelIndex = deviceModelList.value.findIndex(
          model => model.itemId === item.samplerType
        );
        item.modal = modelIndex >= 0 ? deviceModelList.value[modelIndex].itemValue : '';
        item.updateState = item.uploadProtocolFile ? '已上传' : '未上传';
      });
      tableData.value = protocols;
    } else {
      ElMessage.error(res.msg || "获取协议列表失败");
    }
  } catch (error) {
    ElMessage.error("请求异常");
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

// 获取设备型号列表
const getDeviceModelList = async () => {
  try {
    const res = await getDeviceModels();
    if (res.code === 0) {
      deviceModelList.value = res.data;
    }
  } catch (error) {
    console.error("获取设备型号失败", error);
  }
};

// 行选择处理
const handleRowSelect = (selection: ProtocolInfo[]) => {
  selectedIds.value = selection.map(item => item.samplerId);
};

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true;
  importForm.templateFile = null;
  importForm.dll335XFile = null;
  importForm.dll9200File = null;
};

// 处理模板文件选择
const handleTemplateFileChange = (file: any) => {
  if (!file?.raw) return;
  
  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith(".xml")) {
    ElMessage.error("只支持上传 .xml 格式的协议模板文件");
    return;
  }
  
  importForm.templateFile = file.raw;
};

// 处理动态库文件选择
const handleDllFileChange = (file: any, type: '335X' | '9200') => {
  if (!file?.raw) return;
  
  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith(".dll") && !fileName.endsWith(".so")) {
    ElMessage.error("只支持上传 .dll 或 .so 格式的动态库文件");
    return;
  }
  
  if (type === '335X') {
    importForm.dll335XFile = file.raw;
  } else {
    importForm.dll9200File = file.raw;
  }
};

// 处理单独动态库文件选择
const handleSingleDllFileChange = (file: any, type: '1' | '2') => {
  if (!file?.raw) return;
  
  const fileName = file.name.toLowerCase();
  if (!fileName.endsWith(".dll") && !fileName.endsWith(".so")) {
    ElMessage.error("只支持上传 .dll 或 .so 格式的动态库文件");
    return;
  }
  
  if (type === '1') {
    singleDllForm.dll335XFile = file.raw;
  } else {
    singleDllForm.dll9200File = file.raw;
  }
};

// 确认导入
const confirmImport = async () => {
  if (!importForm.templateFile) {
    ElMessage.warning("请先上传模板文件！");
    return;
  }
  
  if (!importForm.dll335XFile && !importForm.dll9200File) {
    ElMessage.warning("请至少上传一个动态库文件！");
    return;
  }
  
  isUploading.value = true;
  try {
    // 1. 上传模板文件
    const templateFormData = new FormData();
    templateFormData.append('file', importForm.templateFile);
    
    const templateRes = await uploadProtocolTemplate(templateFormData);
    if (templateRes.code !== 0) {
      ElMessage.error(templateRes.msg || "非法模板文件！");
      return;
    }
    
    ElMessage.success("模板文件上传成功！");
    const protocolCode = templateRes.data.protocolCode;
    
    // 2. 检查是否需要设置设备种类
    const equipmentCategory = templateRes.data.equipmentCategory;
    const haveEquipmentCategory = equipmentCategory && equipmentCategory !== 0 && 
                                  equipmentCategory !== null && equipmentCategory !== "";
    
    if (haveEquipmentCategory) {
      currentEquipmentTemplateId.value = templateRes.data.equipmentTemplateId;
      try {
        const categoryRes = await getDeviceCategoryMap(equipmentCategory);
        if (categoryRes.code === 0) {
          standardTypes.value = categoryRes.data.equipmentCategoryMapList;
          if (standardTypes.value && standardTypes.value.length > 0) {
            categoryTip.value = `选择${categoryRes.data.standardName}标准设备种类`;
            categoryDialogVisible.value = true;
            importDialogVisible.value = false;
          }
        }
      } catch (error) {
        console.error("获取设备种类失败", error);
      }
    }
    
    // 3. 上传动态库文件
    await uploadDllFiles(protocolCode, haveEquipmentCategory);
    
    if (!haveEquipmentCategory) {
      importDialogVisible.value = false;
      await getProtocolListData();
    }
  } catch (error) {
    ElMessage.error("导入过程中发生错误");
    console.error(error);
  } finally {
    isUploading.value = false;
  }
};

// 上传动态库文件
const uploadDllFiles = async (protocolCode: string, haveEquipmentCategory: boolean) => {
  // 串行上传335X动态库
  if (importForm.dll335XFile) {
    try {
      const formData335X = new FormData();
      formData335X.append('file', importForm.dll335XFile);
      formData335X.append('protocolCode', protocolCode);
      formData335X.append('protocolType', '1');
      
      const res = await uploadDllFile(formData335X);
      if (res.code === 0) {
        ElMessage.success('335X动态库上传成功！');
      } else {
        ElMessage.error(res.msg || '非法335X动态库文件！');
      }
    } catch (error) {
      console.error("335X动态库上传失败", error);
    }
  }
  
  // 串行上传9200动态库
  if (importForm.dll9200File) {
    try {
      const formData9200 = new FormData();
      formData9200.append('file', importForm.dll9200File);
      formData9200.append('protocolCode', protocolCode);
      formData9200.append('protocolType', '2');
      
      const res = await uploadDllFile(formData9200);
      if (res.code === 0) {
        ElMessage.success('9200动态库上传成功！');
      } else {
        ElMessage.error(res.msg || '非法9200动态库文件！');
      }
    } catch (error) {
      console.error("9200动态库上传失败", error);
    }
  }
  
  // 如果没有设备种类设置需求，关闭对话框并刷新列表
  if (!haveEquipmentCategory) {
    await getProtocolListData();
  }
};

// 确认设备种类设置
const confirmStandardType = async () => {
  if (!selectedStandardType.value) {
    ElMessage.warning("请选择标准设备种类");
    return;
  }
  
  try {
    const res = await updateDeviceCategory(
      currentEquipmentTemplateId.value, 
      selectedStandardType.value
    );
    if (res.code === 0) {
      ElMessage.success("设备种类设置成功");
      categoryDialogVisible.value = false;
      await getProtocolListData();
    } else {
      ElMessage.error(res.msg || "设备种类设置失败");
    }
  } catch (error) {
    ElMessage.error("请求异常");
    console.error(error);
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    return;
  }
  
  try {
    await ElMessageBox.confirm("确认删除？", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    });
    
    const ids = selectedIds.value.join(',');
    const res = await deleteProtocolByIds(ids);
    if (res.code === 0) {
      ElMessage.success("删除成功！");
      selectedIds.value = [];
      await getProtocolListData();
    } else {
      ElMessage.error(res.msg || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  }
};





// 确认修改
const confirmChange = async (row: any, field: string, editField: string) => {
  let originalValue = editingOriginalValues[row.samplerId]?.[field];
  let currentValue = row[field];
  
  // 对于设备型号字段，需要比较真实的值（samplerType）
  if (field === 'modal') {
    originalValue = editingOriginalValues[row.samplerId]?.samplerType;
    currentValue = row.samplerType;
  }
  
  // 如果值没有变化，直接退出编辑模式
  if (originalValue === currentValue) {
    return;
  }
  
  try {
    const fieldMap = {
      samplerName: "协议名称",
      dllPath: "动态库路径", 
      modal: "设备型号"
    };
    
    // 准备显示值
    let displayOriginalValue = originalValue;
    let displayCurrentValue = currentValue;
    
    // 对于设备型号，显示名称而不是ID
    if (field === 'modal') {
      displayOriginalValue = editingOriginalValues[row.samplerId]?.modal || '无';
      displayCurrentValue = row.modal || '无';
    }
    
    // 显示修改确认对话框
    await ElMessageBox.confirm(
      `确认修改 ${row.samplerName} 的 ${fieldMap[field]} 吗？`,
      "确认修改",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p style="margin-bottom: 8px;"><strong>字段：</strong>${fieldMap[field]}</p>
            <p style="margin-bottom: 8px;"><strong>修改前：</strong><span style="color: #909399;">${displayOriginalValue}</span></p>
            <p style="margin-bottom: 0;"><strong>修改后：</strong><span style="color: #409EFF;">${displayCurrentValue}</span></p>
          </div>
        `
      }
    );
    
    // 更新协议
    const updateData = { ...row };
    delete updateData.modal;
    delete updateData.nameEdit;
    delete updateData.dllEdit;
    delete updateData.modalEdit;
    delete updateData.updateState;
    
    const res = await updateProtocol(updateData);
    if (res.code === 0) {
      ElMessage.success("更新成功！");
      // 清除编辑状态
      delete editingOriginalValues[row.samplerId];
      await getProtocolListData();
    } else {
      ElMessage.error(res.msg || "更新失败");
      // 恢复原始值
      restoreOriginalValue(row, field);
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
    // 恢复原始值
    restoreOriginalValue(row, field);
  }
};

// 恢复原始值的辅助函数
const restoreOriginalValue = (row: any, field: string) => {
  if (!editingOriginalValues[row.samplerId]) return;
  
  if (field === 'modal') {
    row.modal = editingOriginalValues[row.samplerId].modal;
    row.samplerType = editingOriginalValues[row.samplerId].samplerType;
  } else {
    row[field] = editingOriginalValues[row.samplerId][field];
  }
};

// 动态库上传
const uploadDLLorSO = (row: ProtocolInfo) => {
  currentProtocolForDll.value = row;
  dllUploadDialogVisible.value = true;
  singleDllForm.dll335XFile = null;
  singleDllForm.dll9200File = null;
};

// 确认动态库上传
const confirmDllUpload = async () => {
  if (!singleDllForm.dll335XFile && !singleDllForm.dll9200File) {
    ElMessage.warning("请至少上传一个动态库文件！");
    return;
  }
  
  if (!currentProtocolForDll.value) {
    ElMessage.error("协议信息不存在");
    return;
  }
  
  isUploading.value = true;
  try {
    const protocolCode = currentProtocolForDll.value.protocolCode;
    await uploadDllFiles(protocolCode, false);
    
    dllUploadDialogVisible.value = false;
    await getProtocolListData();
  } catch (error) {
    ElMessage.error("上传过程中发生错误");
    console.error(error);
  } finally {
    isUploading.value = false;
  }
};

// 删除动态库
const deleteDLLorSO = async (row: ProtocolInfo, type: '1' | '2') => {
  try {
    await ElMessageBox.confirm("确认删除？", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    });
    
    const res = await deleteDllFile(row.protocolCode, type);
    if (res.code === 0) {
      ElMessage.success("删除成功！");
      await getProtocolListData();
    } else {
      ElMessage.error(res.msg || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("请求异常");
      console.error(error);
    }
  }
};
</script>

<style scoped>
.protocol-management-container {
  min-height: 100vh;
}

.table-v2-inline-editing-trigger {
  border: 1px transparent dotted;
  padding: 4px;
  border-radius: 4px;
}

.table-v2-inline-editing-trigger:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  max-height: 200px;
  overflow-y: auto;
}
</style>
