# 设备采集管理系统功能文档

## 系统概述
设备采集管理系统是一个通信设备集中管理平台，用于管理局站、监控单元、端口、采集器等设备层级结构，支持从局站规划到设备配置的全流程管理。

## 主要功能模块

### 1. 主界面管理 (collection-management.component)

#### 核心功能
- **三栏布局设计**：左侧局站树、中间监控单元列表、右侧设备详情
- **可调整布局**：支持分割条调整各栏宽度
- **层级树管理**：局站层级树形结构展示和管理

#### 交互功能
- **局站树右键菜单**：
  - 新增分组、局站、局房
  - 删除、重命名操作
  - 上级局站设置
- **监控单元右键菜单**：
  - 添加、编辑、删除监控单元
  - 监控单元分配功能
- **搜索功能**：
  - 局站树实时搜索过滤
  - 监控单元列表过滤
- **拖拽功能**：支持树节点拖拽排序

#### 弹框交互
- 新增局站分组对话框
- 新增局房对话框  
- 级别同步模态窗

### 2. 设备添加管理 (add-device.component)

#### 核心功能
- **复合表单管理**：设备信息、端口信息、采集单元信息一体化配置
- **关联关系管理**：设备-模板-端口-采集单元完整关联链

#### 表单字段配置
- **设备信息区域**：
  - 所属局站、设备模板文件
  - 所属监控单元、设备名称
  - 是否实例化、工程名称、合同号
  - 上一级设备选择
  
- **端口信息区域**：
  - 端口号、端口类型（支持所有/基础切换）
  - 父采集单元、电话号码
  - 端口设置参数

- **采集单元区域**：
  - 采集单元名称、采集周期
  - 采集单元地址（0-32767范围）
  - 采集单元动态库、采集单元类型
  - 电话号码配置

#### 交互功能
- **设备模板选择**：弹框选择设备模板
- **端口类型切换**：普通类型/所有类型切换
- **数据联动**：
  - 监控单元切换联动端口类型
  - 端口类型联动默认设置
  - 采集器类型联动动态库路径
- **表单验证**：必填字段验证和格式校验

### 3. 设备模板选择 (select-eqtemplate.component)

#### 核心功能
- **树形模板展示**：层级结构展示设备模板
- **智能搜索定位**：关键字搜索和节点定位

#### 交互功能
- **搜索功能**：
  - 关键字搜索树节点
  - 搜索结果高亮显示
  - 支持搜索结果循环切换
- **选择功能**：
  - 单击选择模板
  - 双击直接确认
- **状态记忆**：
  - 记忆上次选中状态
  - 记忆树展开状态
- **虚拟滚动**：支持大数据量模板展示

### 4. 监控单元管理 (add-mu.component)

#### 核心功能
- **监控单元配置**：完整的监控单元信息管理
- **服务器关联**：多类型服务器关联配置

#### 表单字段
- **基本信息**：
  - 监控单元ID、名称
  - IP地址、监控单元类型
  - 所属局站
  
- **服务器配置**：
  - 所属RMU服务器（RMU/中心监控类型必填）
  - 数据服务器（多选）
  - RDS服务器（多选）
  
- **项目信息**：
  - 工程名称、合同号
  - 描述信息

#### 交互功能
- **类型联动**：监控单元类型联动RMU服务器必填状态
- **多选配置**：数据服务器和RDS服务器多选
- **输入过滤**：自动过滤特殊字符
- **数据记忆**：工程名称和合同号会话记忆

### 5. 端口管理 (add-port.component)

#### 核心功能
- **端口参数配置**：端口通信参数完整配置
- **类型设置联动**：端口类型自动联动设置参数

#### 表单字段
- **端口基本信息**：
  - 监控单元名称、监控单元编号
  - 端口名称、端口号
  
- **端口配置**：
  - 端口类型（支持基础/全部切换）
  - 电话号码
  - 端口设置参数

#### 交互功能
- **类型切换**：基础端口类型/全部端口类型切换
- **参数联动**：根据端口类型自动设置默认参数
  - 串口类型：9600,n,8,1
  - 网络类型：0.0.0.0:0 或特定IP端口
  - 其他类型：对应默认配置
- **端口号联动**：端口号自动生成端口名（COM+端口号）

### 6. 采集器管理 (add-sampler.component)

#### 核心功能
- **采集单元配置**：采集器参数完整配置
- **采集器类型管理**：多种采集器类型支持

#### 表单字段
- **采集器选择**：采集器类型下拉选择
- **基本信息**：
  - 采集单元名称
  - 采集单元地址（0-32767）
  
- **配置参数**：
  - 采集单元动态库
  - 端口信息（新增时显示）
  - 采集周期
  - 电话号码

#### 交互功能
- **类型联动**：采集器类型选择联动参数自动填充
- **地址验证**：采集单元地址范围验证
- **模式切换**：新增/编辑模式界面差异化
- **参数联动**：采集器类型联动动态库路径和采集器类型

### 7. 局站管理 (add-station.component)

#### 核心功能
- **局站信息配置**：完整的局站属性配置
- **局站类型管理**：基站/机房两种场景支持

#### 表单字段
- **基本信息**：
  - 局站名称、所属分组
  - 局站种类（基站网点场景/机房IDC场景）
  - 局站类型（根据种类联动）
  
- **项目信息**：
  - 工程名称、合同号
  
- **模板配置**：
  - 局站模板选择（可选）

#### 交互功能
- **种类联动**：局站种类切换联动局站类型选项
- **模板选择**：局站模板选择组件集成
- **字符过滤**：局站名称特殊字符自动过滤
- **数据记忆**：工程信息会话记忆

### 8. 批量局站管理 (add-station-from-model)

#### 核心功能
- **样板站复制**：基于样板站批量创建局站
- **批量导入**：Excel文件批量导入局站信息

#### 子组件功能
- **multi-station-import**：
  - Excel文件上传解析
  - 批量局站信息可编辑表格
  - 局站模板下载
  
- **model-stations**：
  - 样板站下局站列表展示
  - 局站信息表格显示
  
- **station-model-list**：
  - 样板站选择列表
  - 样板站信息展示

#### 交互功能
- **文件上传**：
  - Excel文件选择和解析
  - 模板文件下载功能
- **可编辑表格**：
  - 局站名称和IP地址在线编辑
  - 表格数据实时验证
- **样板站选择**：样板站选择弹框
- **批量提交**：
  - 批量创建进度提示
  - 提交状态Loading显示

### 9. 设备详情管理 (level-detail.component)

#### 核心功能
- **局站详情编辑**：局站完整属性信息编辑
- **多维度配置**：地理、管理、项目等多维度信息

#### 表单字段
- **基本信息**：
  - 局站名称、局站编号
  - 所属分组、局站类型
  
- **状态信息**：
  - 局站级别、项目状态
  
- **地理信息**：
  - 经度、纬度（±180度范围，精度0.00001）
  
- **管理信息**：
  - 代理商、联系人
  - 工程名称、合同号
  
- **屏蔽信息**：
  - 屏蔽开始时间、屏蔽结束时间

#### 交互功能
- **实时保存**：点击保存按钮实时更新
- **下拉联动**：各种字典数据下拉选择
- **数值限制**：经纬度数值范围和精度限制
- **只读字段**：项目信息等只读显示

### 10. 设备列表管理 (level-device-list.component)

#### 核心功能
- **设备列表展示**：局站下设备列表完整展示
- **设备操作集成**：设备管理操作集中入口

#### 列表功能
- **数据展示**：
  - 设备名称、所属监控单元
  - 端口信息、最后修改时间
- **虚拟滚动**：支持大数据量设备展示
- **多字段过滤**：设备名称、监控单元、端口、时间过滤
- **状态显示**：设备选中状态高亮

#### 右键菜单
- **设备管理**：
  - 添加设备
  - 编辑设备、删除设备
  
- **设备操作**：
  - 重新关联模板
  - 切换局房
  
- **关联管理**：
  - 设备模板切换
  - 局房分配调整

#### 弹框交互
- **局房切换**：局房选择对话框
- **模板选择**：设备模板选择对话框  
- **删除确认**：设备删除确认对话框

### 11. 采集信息管理 (level-sampler-info.component)

#### 核心功能
- **三级树展示**：端口-采集单元-设备三级树形结构
- **采集架构可视化**：设备采集架构直观展示

#### 树形展示
- **端口节点**：
  - 公寓图标标识
  - 端口名称显示
  
- **采集单元节点**：
  - API图标标识
  - 采集单元名称显示
  
- **设备节点**：
  - 硬盘图标标识
  - 设备名称显示

#### 右键菜单功能
- **端口级别操作**：
  - 编辑端口、删除端口
  - 添加采集单元
  
- **采集单元级别操作**：
  - 编辑采集单元、删除采集单元
  - 添加设备
  
- **设备级别操作**：
  - 编辑设备、删除设备

#### 交互功能
- **双击跳转**：双击设备跳转设备管理详情
- **自适应高度**：树形组件高度自适应调整
- **空白菜单**：空白处右键添加端口和设备

### 12. 级别选择 (select-level.component)

#### 核心功能
- **资源结构树**：系统资源结构层级树选择
- **层级路径**：完整层级路径展示

#### 交互功能
- **树形选择**：
  - 单击选择节点
  - 双击直接确认
- **路径展示**：层级路径完整显示
- **关系管理**：上下级关系清晰展示

### 13. 上级局站管理 (up-station.component)

#### 核心功能
- **上级局站配置**：设置上级局站信息
- **简单表单**：精简的上级局站信息录入

#### 表单字段
- 上级局站名称
- 描述信息


## API接口文档

### 设备管理相关API

#### 设备信息管理
```typescript
// 获取设备信息
GET api/config/equipment/${equipmentId}

// 设备配置新增/更新
POST api/config/equipment/v3/config
PUT api/config/equipment/v3/config

// 设备搜索查询
GET api/config/equipment/search?stationId=${stationId}&houseId=${houseId}

// 设备删除
DELETE api/config/equipment/config/${equipmentId}

// 设备切换模板
POST api/config/equipment/switchtemplate

// 设备切换局房
PUT api/config/equipment/updateHouseId
```

#### 设备模板管理
```typescript
// 获取设备模板树
GET api/config/equipmenttemplate/tree

// 获取设备模板详情
GET api/config/equipmenttemplate/${templateId}
```

### 监控单元管理相关API

#### 监控单元基础操作
```typescript
// 监控单元配置新增/更新
POST api/config/monitor-unit/v3/config
PUT api/config/monitor-unit/v3/config

// 获取监控单元类型
GET api/config/monitor-unit/types

// 获取监控单元下的设备
GET api/config/monitor-unit/equipments/${monitorUnitId}

// 获取监控单元端口列表
GET api/config/monitor-unit/ports/${monitorUnitId}
```

### 端口管理相关API

#### 端口配置管理
```typescript
// 端口配置新增/更新
POST api/config/port/config
PUT api/config/port/config

// 获取端口配置详情
GET api/config/port/config/${portId}

// 获取端口类型列表
GET api/config/port/types?monitorUnitCategory=${category}

// 获取端口下的采集单元
GET api/config/port/sampler-units/${portId}
```

### 采集单元管理相关API

#### 采集单元配置
```typescript
// 采集单元配置新增/更新
POST api/config/sampler-unit/config
PUT api/config/sampler-unit/config

// 获取采集单元配置详情
GET api/config/sampler-unit/config/${samplerUnitId}

// 获取采集器类型列表
GET api/config/sampler
```

### 局站管理相关API

#### 局站基础操作
```typescript
// 局站配置新增/更新
POST api/config/station/config
PUT api/config/station/config

// 获取局站配置详情
GET api/config/station/config/${stationId}

// 局站重命名
PUT api/config/station/rename

// 获取局站分类数据项
GET api/config/station/categorizedataitemsbytype
```

#### 局房管理
```typescript
// 获取局站下的局房列表
GET api/config/house/config?stationId=${stationId}
```

### 资源结构管理相关API

#### 资源结构树
```typescript
// 获取完整资源结构树
GET api/config/resource-structure/full-tree

// 资源结构节点操作
POST api/config/resource-structure/node
PUT api/config/resource-structure/node
DELETE api/config/resource-structure/node/${nodeId}
```

### 样板站管理相关API

#### 样板站操作
```typescript
// 获取样板站列表
GET api/config/swatchstation/list

// 获取样板站下的局站
GET api/config/swatchstation/station/${swatchStationId}

// 批量新增局站（基于样板站）
POST api/config/swatchstation/v3/addstation
```

### 系统配置相关API

#### 数据字典管理
```typescript
// 获取数据项列表
GET api/config/dataitems?entryId=${entryId}

// 数据项类型说明：
// entryId=71: 局站分类
// entryId=2: 局站级别  
// entryId=5: 项目状态
// entryId=15: 代理商
```

#### 工作站服务器管理
```typescript
// 获取服务器资源列表
GET api/config/workstation/server-source-list
```

#### 员工信息管理
```typescript
// 获取员工ID和部门ID列表
GET api/config/employee/list/employeeIdAndDepartmentId
```

### API调用规范

#### 请求格式
- **Content-Type**: `application/json`
- **请求方法**: GET, POST, PUT, DELETE
- **参数格式**: URL参数 + JSON Body

#### 响应格式
```typescript
// 标准响应格式
interface ApiResponse<T> {
  code: number;           // 响应码：200-成功，其他-失败
  message: string;        // 响应消息
  data: T;               // 响应数据
  timestamp: string;     // 响应时间戳
}
```

#### 错误处理
- **400**: 请求参数错误
- **401**: 未授权访问  
- **403**: 禁止访问
- **404**: 资源不存在
- **500**: 服务器内部错误

### API使用示例

#### 新增监控单元示例
```typescript
// 请求
POST api/config/monitor-unit/v3/config
Content-Type: application/json

{
  "monitorUnitName": "监控单元001",
  "ipAddress": "*************", 
  "monitorUnitCategory": 18,
  "stationId": 1,
  "workStationId": "1",
  "dataServer": "1,2",
  "rdsServer": "3,4",
  "projectName": "测试工程",
  "contractNo": "HT2024001",
  "description": "测试监控单元"
}

// 响应
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "monitorUnitId": 123,
    "monitorUnitName": "监控单元001",
    // ... 其他字段
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

#### 设备搜索示例
```typescript
// 请求
GET api/config/equipment/search?stationId=1&houseId=2

// 响应
{
  "code": 200,
  "message": "查询成功", 
  "data": [
    {
      "equipmentId": 1,
      "equipmentName": "设备001",
      "monitorUnitId": 123,
      "monitorUnitName": "监控单元001",
      "portName": "COM1",
      "updateTime": "2024-12-19 10:30:00"
    }
    // ... 更多设备
  ],
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### API性能优化

#### 分页查询
```typescript
// 支持分页的API
GET api/config/equipment/search?page=1&size=20&stationId=${stationId}

// 分页响应格式
{
  "code": 200,
  "data": {
    "content": [...],      // 数据列表
    "totalElements": 100,  // 总记录数
    "totalPages": 5,       // 总页数
    "size": 20,           // 页大小
    "page": 1             // 当前页
  }
}
```

#### 缓存策略
- **静态数据缓存**: 数据字典、设备类型等相对固定的数据
- **会话缓存**: 用户选择状态、树展开状态等
- **本地存储**: 项目名称、合同号等用户输入记忆

#### 批量操作
```typescript
// 批量删除设备
DELETE api/config/equipment/batch
{
  "equipmentIds": [1, 2, 3, 4]
}

// 批量更新设备状态  
PUT api/config/equipment/batch-status
{
  "equipmentIds": [1, 2, 3],
  "status": "active"
}
```

### API安全机制

#### 权限控制
- **角色权限**: 基于用户角色的API访问控制
- **操作权限**: 细粒度的增删改查权限控制
- **数据权限**: 基于组织架构的数据访问权限

#### 请求验证
- **参数验证**: 必填字段、数据格式、长度限制等
- **业务验证**: 数据唯一性、关联关系、状态流转等
- **权限验证**: 操作权限、数据范围权限等

---

**最后更新时间**：2024年12月19日  
**文档版本**：v1.0
