<template>
  <div class="device-list-container">
    <div class="table-header">
      <div class="filter-row">
        <div class="filter-item">
          <span class="filter-label">设备名称</span>
          <el-input 
            v-model="filters.equipmentName" 
            placeholder="输入设备名称"
            size="small"
            clearable
            @input="handleFilterChange"
          />
        </div>
        <div class="filter-item">
          <span class="filter-label">所属监控单元</span>
          <el-input 
            v-model="filters.monitorUnitName" 
            placeholder="输入监控单元名称"
            size="small"
            clearable
            @input="handleFilterChange"
          />
        </div>
        <div class="filter-item">
          <span class="filter-label">最后修改时间</span>
          <el-input 
            v-model="filters.updateTime" 
            placeholder="输入时间"
            size="small"
            clearable
            @input="handleFilterChange"
          />
        </div>
      </div>
    </div>

    <div class="table-content">
      <el-table 
        :data="filteredDevices" 
        height="100%"
        stripe
        highlight-current-row
        @current-change="handleCurrentChange"
        @row-contextmenu="handleRowContextMenu"
        v-loading="loading"
      >
        <el-table-column label="设备名称" min-width="150">
          <template #default="{ row }">
            <span 
              class="device-name-link" 
              @click="handleDeviceNameClick(row)"
              :title="row.equipmentName"
            >
              {{ row.equipmentName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="monitorUnitName" label="所属监控单元" min-width="150" />
        <el-table-column prop="updateTime" label="最后修改时间" width="200" />
      </el-table>
    </div>

    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      :show-timeout="0"
      :hide-timeout="0"
      trigger="contextmenu"
      @command="handleContextMenuCommand"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="edit">
            <el-icon><Edit /></el-icon>
            编辑设备
          </el-dropdown-item>
          <el-dropdown-item command="delete" divided>
            <el-icon><Delete /></el-icon>
            删除设备
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'
import { getMonitorUnitDevices } from '@/api/monitor-unit'

interface Props {
  monitorUnit: any
}

const props = defineProps<Props>()

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const devices = ref<any[]>([])
const currentRow = ref<any>(null)
const contextMenuRef = ref()

// 过滤条件
const filters = ref({
  equipmentName: '',
  monitorUnitName: '',
  updateTime: ''
})

// 计算属性
const filteredDevices = computed(() => {
  let result = devices.value
  
  if (filters.value.equipmentName) {
    result = result.filter(item => 
      item.equipmentName?.toLowerCase().includes(filters.value.equipmentName.toLowerCase())
    )
  }
  
  if (filters.value.monitorUnitName) {
    result = result.filter(item => 
      item.monitorUnitName?.toLowerCase().includes(filters.value.monitorUnitName.toLowerCase())
    )
  }
  
  if (filters.value.updateTime) {
    result = result.filter(item => 
      item.updateTime?.toLowerCase().includes(filters.value.updateTime.toLowerCase())
    )
  }
  
  return result
})

// 方法
const loadDevices = async () => {
  if (!props.monitorUnit?.monitorUnitId) return
  
  try {
    loading.value = true
    const response = await getMonitorUnitDevices(props.monitorUnit.monitorUnitId)
    devices.value = response.data || []
    // 添加监控单元名称
    devices.value.forEach(device => {
      device.monitorUnitName = props.monitorUnit.monitorUnitName
    })
  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error('加载设备列表失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  // 过滤逻辑在计算属性中处理
}

const handleDeviceNameClick = (device: any) => {
  if (!device.equipmentId) {
    ElMessage.warning('设备ID不存在，无法跳转')
    return
  }
  
  // 跳转到设备管理页面
  router.push({
    path: `/siteweb-omc/device-management/${device.equipmentId}`,
    query: {
      title: device.equipmentName || '设备管理'
    }
  })
}

const handleCurrentChange = (row: any) => {
  currentRow.value = row
}

const handleRowContextMenu = (row: any, column: any, event: MouseEvent) => {
  event.preventDefault()
  currentRow.value = row
  // 显示右键菜单的逻辑可以在这里实现
}

const handleContextMenuCommand = (command: string) => {
  if (!currentRow.value) return
  
  switch (command) {
    case 'edit':
      editDevice(currentRow.value)
      break
    case 'delete':
      deleteDevice(currentRow.value)
      break
  }
}

const editDevice = (device: any) => {
  ElMessage.info('编辑设备功能待实现')
}

const deleteDevice = (device: any) => {
  ElMessageBox.confirm(
    `确认要删除设备 ${device.equipmentName} 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 删除设备的逻辑
    ElMessage.success('删除成功')
    loadDevices()
  }).catch(() => {
    // 取消删除
  })
}

// 监听props变化
watch(() => props.monitorUnit, () => {
  loadDevices()
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadDevices()
})
</script>

<style scoped>
.device-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  padding: 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.filter-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.table-content {
  flex: 1;
  padding: 16px;
}

.el-table {
  --el-table-border-color: #e4e7ed;
}

.el-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

.el-table :deep(.current-row) {
  background-color: #e1f5fe;
}

.device-name-link {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.device-name-link:hover {
  color: #337ecc;
  text-decoration: underline;
}
</style> 