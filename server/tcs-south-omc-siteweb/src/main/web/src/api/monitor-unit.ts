import { http } from "@/utils/http";

// 监控单元类型定义
export interface MonitorUnit {
  monitorUnitId: number;
  monitorUnitName: string;
  monitorUnitCategory: number;
  monitorUnitCode: string;
  workStationId: number;
  stationId: number;
  ipAddress: string;
  runMode: number;
  configFileCode: string;
  configUpdateTime: string | null;
  sampleConfigCode: string | null;
  softwareVersion: string;
  description: string;
  startTime: string | null;
  heartbeatTime: string | null;
  connectState: number;
  updateTime: string;
  isSync: boolean;
  syncTime: string | null;
  isConfigOK: boolean;
  configFileCode_Old: string | null;
  sampleConfigCode_Old: string | null;
  appConfigId: number;
  canDistribute: boolean;
  enable: boolean;
  rdsServer: string | null;
  dataServer: string | null;
  installTime: string;
  fsu: boolean;
  projectName: string;
  contractNo: string;
  state: number;
  portNos: string;
  workStationName: string | null;
  stationName: string;
}

// 设备类型定义
export interface Equipment {
  equipmentId: number;
  equipmentName: string;
  monitorUnitId: number;
  monitorUnitName?: string;
  portName?: string;
  updateTime?: string;
}

// 采集树节点类型定义
export interface SamplerTreeNode {
  portId?: number;
  portName?: string;
  samplerUnitId?: number;
  samplerUnitName?: string;
  equipmentId?: number;
  equipmentName?: string;
  samplerUnits?: SamplerTreeNode[];
  equipments?: SamplerTreeNode[];
}

// 工作站服务器类型定义
export interface WorkStationServer {
  workStationId: number;
  workStationName: string;
  workStationType: number;
  ipAddress?: string;
  description?: string;
}

// 监控单元类型定义
export interface MonitorUnitType {
  typeId: number;
  typeName: string;
  order: number;
}

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
  timestamp?: number;
  msg?: string | null;
}

// 获取所有采集器列表
export function getMonitorUnits() {
  return http.request<ApiResponse<MonitorUnit[]>>(
    "get",
    "/api/config/monitor-unit"
  );
}

// 获取指定采集器的设备列表
export function getMonitorUnitDevices(monitorUnitId: number) {
  return http.request<ApiResponse<Equipment[]>>(
    "get",
    `/api/config/monitor-unit/equipments/${monitorUnitId}`
  );
}

// 获取指定采集器的采集信息树
export function getMonitorUnitSamplerTree(monitorUnitId: number) {
  return http.request<ApiResponse<SamplerTreeNode[]>>(
    "get",
    `/api/config/monitor-unit/sampler-tree/${monitorUnitId}`
  );
}

// 获取指定采集器的端口列表
export function getMonitorUnitPorts(monitorUnitId: number) {
  return http.request<ApiResponse<any[]>>(
    "get",
    `/api/config/monitor-unit/ports/${monitorUnitId}`
  );
}

// 获取采集器类型列表
export function getMonitorUnitTypes() {
  return http.request<ApiResponse<MonitorUnitType[]>>(
    "get",
    "/api/config/monitor-unit/types"
  );
}

// 获取工作站服务器列表
export function getWorkStationServerList() {
  return http.request<ApiResponse<WorkStationServer[]>>(
    "get",
    "/api/config/workstation/server-source-list"
  );
}

// 创建采集器
export function createMonitorUnit(data: any) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/monitor-unit/v3/config",
    { data }
  );
}

// 更新采集器
export function updateMonitorUnit(data: any) {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/config/monitor-unit/v3/config",
    { data }
  );
}

// 删除采集器
export function deleteMonitorUnit(monitorUnitId: number, isDelEqs: boolean = false) {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/config/monitor-unit/config/${monitorUnitId}${isDelEqs ? '?isDelEqs=true' : ''}`
  );
} 