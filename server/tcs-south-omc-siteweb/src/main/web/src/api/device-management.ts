import { http } from "../utils/http";

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
  timestamp?: number;
  msg?: string | null;
}

// 设备信息接口
export interface DeviceInfo {
  equipmentId?: number;
  equipmentName?: string;
  equipmentTemplate?: string;
  equipmentTemplateId?: number;
  equipmentTemplateName?: string;
  stationId?: number;
  stationName?: string;
  monitorUnitId?: number;
  monitorUnitName?: string;
  equipmentCategory?: number;
  equipmentType?: number;
  batteryType?: number;
  batteryState?: string;
  equipmentProperty?: number;
  workingState?: number;
  vendor?: string;
  manufacturer?: string;
  assetState?: number;
  installDate?: string;
  warrantyDate?: string;
  price?: number;
  assetNumber?: string;
  deviceModel?: string;
  softwareVersion?: string;
  hardwareVersion?: string;
  projectName?: string;
  description?: string;
  parentEquipmentId?: number;
  alarmFilterExpression?: string;
  updateTime?: string;
  enable?: boolean;
}

// 信号信息接口
export interface SignalInfo {
  id?: number;
  signalId?: number;
  equipmentTemplateId?: number;
  signalName?: string;
  displayIndex?: number;
  signalCategory?: number;
  signalType?: number;
  channelNo?: number;
  channelType?: number;
  expression?: string;
  dataType?: number;
  showPrecision?: string;
  unit?: string;
  storeInterval?: string;
  absValueThreshold?: string;
  percentThreshold?: string;
  staticsPeriod?: string;
  enable?: boolean;
  visible?: boolean;
  chargeStoreInterVal?: number;
  chargeAbsValue?: number;
  description?: string;
  signalProperty?: string;
  stateValue?: string;
  moduleNo?: string;
  baseTypeName?: string;
  acrossSignal?: boolean;
  hasInstance?: boolean;
}

// 事件信息接口
export interface EventInfo {
  id?: number;
  eventId?: number;
  equipmentTemplateId?: number;
  eventName?: string;
  displayIndex?: number;
  eventCategory?: number;
  startType?: number;
  endType?: number;
  eventConditionList?: any[];
  eventConditionListLabel?: string;
  startExpression?: string;
  suppressExpression?: string;
  signalId?: number;
  description?: string;
  enable?: boolean;
  visible?: boolean;
  moduleNo?: number;
  turnover?: number;
  hasInstance?: boolean;
}

// 控制信息接口
export interface ControlInfo {
  id?: number;
  controlId?: number;
  equipmentTemplateId?: number;
  controlName?: string;
  displayIndex?: number;
  controlCategory?: number;
  controlSeverity?: number;
  cmdToken?: string;
  timeOut?: number;
  retry?: number;
  commandType?: number;
  controlType?: number;
  dataType?: number;
  maxValue?: number;
  minValue?: number;
  signalId?: number;
  controlMeaningsList?: any[];
  defaultValue?: string;
  description?: string;
  enable?: boolean;
  visible?: boolean;
  moduleNo?: number;
  baseTypeId?: number;
  baseTypeName?: string;
  hasInstance?: boolean;
}

// 数据字典项接口
export interface DictionaryItem {
  itemId: number;
  itemValue: string;
  itemName?: string;
  parentId?: number;
  sortOrder?: number;
}

// 设备基类信息接口
export interface EquipmentBaseType {
  baseTypeId: number;
  baseTypeName: string;
  description?: string;
}

// 信号实例接口
export interface SignalInstance {
  id?: number;
  signalId: number;
  equipmentId: number;
  signalName?: string;
  samplerUnitId?: number;
  samplerUnitName?: string;
  channelNo?: number;
  expression?: string;
  configMode?: number;
  enable?: boolean;
  description?: string;
}

// 事件实例接口
export interface EventInstance {
  id?: number;
  eventId: number;
  equipmentId: number;
  eventName?: string;
  startExpression?: string;
  suppressExpression?: string;
  enable?: boolean;
  description?: string;
}

// 采集单元信息接口
export interface SamplerUnit {
  samplerUnitId: number;
  samplerUnitName: string;
  portInfos?: Array<{
    portId: number;
    portName: string;
    channelCount: number;
  }>;
}

// 跨站信号信息接口
export interface CrossSiteSignal {
  id?: number;
  signalId: number;
  equipmentId: number;
  signalName?: string;
  stationName?: string;
  monitorUnitName?: string;
  equipmentName?: string;
  signalPath?: string;
  pointExpression?: string;
  indexExpression?: string;
}

// 变更记录接口
export interface ChangeLog {
  id: number;
  objectType: number;
  objectId: number;
  changeTime: string;
  changeUser: string;
  changeType: string;
  changeDescription: string;
  oldValue?: string;
  newValue?: string;
}

// ===== 设备管理相关API =====

// 获取设备信息
export function getDeviceInfo(equipmentId: string | number) {
  return http.request<ApiResponse<DeviceInfo>>(
    "get",
    `/api/config/equipment/${equipmentId}`
  );
}

// 更新设备信息
export function updateDeviceInfo(deviceInfo: DeviceInfo) {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/config/equipment/config",
    { data: deviceInfo }
  );
}

// 获取设备树
export function getEquipmentTree() {
  return http.request<ApiResponse<any[]>>(
    "get",
    "/api/config/equipment/tree"
  );
}

// 获取简化设备列表（上级设备列表）
export function getSimplifyEquipments() {
  return http.request<ApiResponse<DeviceInfo[]>>(
    "get",
    "/api/config/equipment/simplifyequipments"
  );
}

// 创建设备实例
export function createEquipmentInstance(data: any) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/equipment/instance",
    { data }
  );
}

// ===== 监控单元相关API =====

// 获取监控单元信息
export function getMonitorUnitInfo(muId: string | number) {
  return http.request<ApiResponse<any>>(
    "get",
    `/api/config/monitor-unit/config/${muId}`
  );
}

// ===== 信号实例相关API =====

// 获取信号实例列表（根据设备ID）- 与 Angular 版本兼容
export function getSignalInstances(equipmentId: string | number) {
  return http.request<ApiResponse<SignalInstance[]>>(
    "get",
    `/api/config/monitorunitsignal/condition?equipmentId=${equipmentId}`
  );
}

// 获取设备信号实例状态（用于检查信号是否已设置实例）
// 使用与 Angular 版本相同的接口
export function getDeviceSignalInstances(equipmentId: string | number) {
  return http.request<ApiResponse<any[]>>(
    "get",
    `/api/config/monitorunitsignal/condition?equipmentId=${equipmentId}`
  );
}

// 获取单个信号实例（根据信号ID）
export function getSignalInstance(signalId: string | number) {
  return http.request<ApiResponse<SignalInstance>>(
    "get",
    `/api/config/monitorunitsignal/condition?signalId=${signalId}`
  );
}

// 创建/更新信号实例（使用与 Angular 版本相同的接口）
export function saveSignalInstance(data: SignalInstance) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/monitorunitsignal",
    { data }
  );
}

// 更新信号（兼容旧版本API调用）
export function updateSignal(signalInfo: SignalInfo) {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/config/signal/config",
    { data: signalInfo }
  );
}

// 批量删除信号实例（使用与 Angular 版本相同的接口）
export function deleteSignalInstances(equipmentId: string | number, signalIds: string) {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/config/monitorunitsignal?equipmentId=${equipmentId}&signalIds=${signalIds}`
  );
}

// 获取单个信号实例信息（使用与 Angular 版本相同的接口）
export function getSignalInstanceInfo(equipmentId: string | number, signalId: string | number) {
  return http.request<ApiResponse<any>>(
    "get",
    `/api/config/monitorunitsignal/condition?equipmentId=${equipmentId}&signalId=${signalId}`
  );
}

// ===== 事件实例相关API =====

// 获取事件实例列表（根据设备ID）
export function getEventInstances(equipmentId: string | number) {
  return http.request<ApiResponse<EventInstance[]>>(
    "get",
    `/api/config/monitorunitevent/condition?equipmentId=${equipmentId}`
  );
}

// 获取单个事件实例（根据事件ID）
export function getEventInstance(eventId: string | number) {
  return http.request<ApiResponse<EventInstance>>(
    "get",
    `/api/config/monitorunitevent/condition?eventId=${eventId}`
  );
}

// 创建/更新事件实例
export function saveEventInstance(data: EventInstance) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/monitorunitevent",
    { data }
  );
}

// 批量删除事件实例
export function deleteEventInstances(eventIds: number[]) {
  return http.request<ApiResponse<any>>(
    "delete",
    "/api/config/monitorunitevent",
    { data: eventIds }
  );
}

// ===== 跨站信号相关API =====

// 获取跨站信号信息
export function getCrossSiteSignalInfo(signalId: string | number) {
  return http.request<ApiResponse<CrossSiteSignal>>(
    "get",
    `/api/config/signal/crossmonitorunitsignal/condition?signalId=${signalId}`
  );
}

// 创建跨站信号
export function createCrossSiteSignal(data: any) {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/config/signal/createacrossmonitorunitsignal",
    { data }
  );
}

// ===== 采集单元相关API =====

// 获取带端口的采集单元列表
export function getSamplerUnitsWithPort() {
  return http.request<ApiResponse<SamplerUnit[]>>(
    "get",
    "/api/config/sampler-unit/samplerunitwithport"
  );
}

// ===== 基础数据相关API =====

// 获取数据字典项
export function getDataDictionary(entryId: number) {
  return http.request<ApiResponse<DictionaryItem[]>>(
    "get",
    `/api/config/dataitems?entryId=${entryId}`
  );
}

// 获取设备基类列表
export function getEquipmentBaseTypes() {
  return http.request<ApiResponse<EquipmentBaseType[]>>(
    "get",
    "/api/config/equipmentbasetype/list"
  );
}

// 获取设备分类
export function getEquipmentCategories() {
  return http.request<ApiResponse<DictionaryItem[]>>(
    "get",
    "/api/config/dataitems/equipmentcategorys"
  );
}

// ===== 变更记录相关API =====

// 获取变更记录
export function getChangeLog(objectType: number, objectId: string | number, pageNo?: number, pageSize?: number) {
  const params = new URLSearchParams({
    objectType: objectType.toString(),
    objectId: objectId.toString()
  });
  
  if (pageNo !== undefined) {
    params.append('pageNo', pageNo.toString());
  }
  if (pageSize !== undefined) {
    params.append('pageSize', pageSize.toString());
  }
  
  return http.request<ApiResponse<{
    records: ChangeLog[];
    total: number;
    current: number;
    size: number;
  }>>(
    "get",
    `/api/config/changelog?${params.toString()}`
  );
}

// 获取设备操作日志
export function getEquipmentOperationLog(
  equipmentId: string | number, 
  current?: number, 
  size?: number, 
  startTime?: string | null, 
  endTime?: string | null
) {
  // 分页参数放在URL查询参数中
  const params = new URLSearchParams();
  if (current !== undefined) {
    params.append('current', current.toString());
  }
  if (size !== undefined) {
    params.append('size', size.toString());
  }
  
  // 请求体数据
  const requestData = {
    objectTypes: 11, // 设备类型固定为11
    objectId: equipmentId.toString(),
    startTime: startTime || null,
    endTime: endTime || null
  };
  
  return http.request<ApiResponse<{
    records: Array<{
      userName: string;
      objectId: string;
      objectType: string;
      propertyName: string;
      operationTime: string;
      operationType: string;
      oldValue: string | null;
      newValue: string;
      objectName: string | null;
    }>;
    total: number;
    current: number;
    size: number;
    pages: number;
  }>>(
    "post",
    `/api/config/operationdetail/page/equipment?${params.toString()}`,
    { data: requestData }
  );
}

// ===== 便捷方法 =====

// 获取信号列表（从模板获取，用于显示可配置信号）
export function getSignalListByTemplate(equipmentTemplateId: string | number) {
  return http.request<ApiResponse<SignalInfo[]>>(
    "get",
    `/api/config/signal?equipmentTemplateId=${equipmentTemplateId}`
  );
}

// 获取事件列表（从模板获取，用于显示可配置事件）
export function getTemplateEventById(equipmentTemplateId: string | number) {
  return http.request<ApiResponse<EventInfo[]>>(
    "get",
    `/api/config/event?equipmentTemplateId=${equipmentTemplateId}`
  );
}

// 获取控制列表（从模板获取，用于显示可配置控制）
export function getTemplateControlById(equipmentTemplateId: string | number) {
  return http.request<ApiResponse<ControlInfo[]>>(
    "get",
    `/api/config/control/list?equipmentTemplateId=${equipmentTemplateId}`
  );
}

// 获取信号列表（从模板获取，用于显示可配置信号）
export async function getSignalList(equipmentId: string | number): Promise<SignalInfo[]> {
  try {
    // 先获取设备信息，从中得到模板ID
    const deviceResponse = await getDeviceInfo(equipmentId);
    if (deviceResponse.code === 0 && deviceResponse.data?.equipmentTemplateId) {
      // 这里需要根据实际API调整，假设有获取模板信号的接口
      // 暂时返回空数组，等待实际API确认
      return [];
    }
    return [];
  } catch (error) {
    console.error('获取信号列表失败:', error);
    return [];
  }
}

// 获取事件列表（从模板获取，用于显示可配置事件）
export async function getEventList(equipmentId: string | number): Promise<EventInfo[]> {
  try {
    // 先获取设备信息，从中得到模板ID
    const deviceResponse = await getDeviceInfo(equipmentId);
    if (deviceResponse.code === 0 && deviceResponse.data?.equipmentTemplateId) {
      // 这里需要根据实际API调整，假设有获取模板事件的接口
      // 暂时返回空数组，等待实际API确认
      return [];
    }
    return [];
  } catch (error) {
    console.error('获取事件列表失败:', error);
    return [];
  }
}

// 获取控制列表（从模板获取，用于显示可配置控制）
export async function getControlList(equipmentId: string | number): Promise<ControlInfo[]> {
  try {
    // 先获取设备信息，从中得到模板ID
    const deviceResponse = await getDeviceInfo(equipmentId);
    if (deviceResponse.code === 0 && deviceResponse.data?.equipmentTemplateId) {
      // 这里需要根据实际API调整，假设有获取模板控制的接口
      // 暂时返回空数组，等待实际API确认
      return [];
    }
    return [];
  } catch (error) {
    console.error('获取控制列表失败:', error);
    return [];
  }
}