package com.siteweb.tcs.south.omc.config;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

@Configuration
public class FlywayConfig {
    @Bean(name = "omcFlyway")
    @DependsOn("omcDataSource")
    public Flyway flyway(@Qualifier("omcDataSource") DataSource dataSource) {
        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/south-omc-siteweb/migration")
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("omc_schema_history")
            .load();
        flyway.migrate();
        return flyway;
    }
} 