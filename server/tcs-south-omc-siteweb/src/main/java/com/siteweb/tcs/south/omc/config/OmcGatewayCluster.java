package com.siteweb.tcs.south.omc.config;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.common.system.DefaultShardingMessageExtractor;
import com.siteweb.tcs.south.omc.connector.process.OmcFSUProxy;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class OmcGatewayCluster {
    @Value("${omc.gateway.sharding.name:OmcGatewayProxy}")
    private String gatewayShardingName;

    @Bean("omc-gateway-sharding")
    public ActorRef createGatewaySharding() throws IOException {
        var extractor = new DefaultShardingMessageExtractor();
        return ClusterContext.createSharding(gatewayShardingName, Props.create(OmcFSUProxy.class), extractor);
    }
} 