package com.siteweb.tcs.south.omc;

import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.runtime.SouthPlugin;
import com.siteweb.tcs.south.omc.connector.ConnectorDataHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;

@Slf4j
public class OmcSitewebPlugin extends SouthPlugin {

    @Autowired
    private ConnectorDataHolder dataHolder;

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Autowired
    private MessageSource messageSource;

    public OmcSitewebPlugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
            log.info("正在启动 OMC智慧运维插件");
            dataHolder.setPluginId(this.getPluginId());
            log.info("OMC智慧运维插件启动成功");
        } catch (Exception e) {
            log.error("OMC智慧运维插件启动失败", e);
        }
    }

    @Override
    public void onStop() {
        log.info("正在停止 OMC智慧运维插件");
    }
} 