package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.south.omc.dal.dto.OmcDeviceDTO;
import com.siteweb.tcs.south.omc.dal.entity.OmcDevice;
import com.siteweb.tcs.south.omc.web.service.IOmcDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/api/south/omc-siteweb/device")
public class OmcDeviceController {

    @Autowired
    private IOmcDeviceService omcDeviceService;

    @GetMapping(value = "/list")
    public ResponseEntity<List<OmcDeviceDTO>> listDevices() {
        LocalDateTime minTime = LocalDateTime.now().minusMonths(1);
        List<OmcDeviceDTO> devices = omcDeviceService.listActiveDevices(minTime);
        return ResponseEntity.ok(devices);
    }

    @GetMapping(value = "/get/{deviceName}")
    public ResponseEntity<OmcDeviceDTO> getDevice(@PathVariable String deviceName) {
        OmcDeviceDTO device = omcDeviceService.getDeviceByName(deviceName);
        if (device == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(device);
    }

    @PostMapping(value = "/add")
    public ResponseEntity<Map<String, Object>> addDevice(@RequestBody OmcDevice device) {
        boolean success = omcDeviceService.saveDevice(device);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "设备添加成功" : "设备添加失败");
        return ResponseEntity.ok(result);
    }

    @PutMapping(value = "/update")
    public ResponseEntity<Map<String, Object>> updateDevice(@RequestBody OmcDevice device) {
        boolean success = omcDeviceService.updateDevice(device);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "设备更新成功" : "设备更新失败");
        return ResponseEntity.ok(result);
    }

    @DeleteMapping(value = "/delete/{deviceId}")
    public ResponseEntity<Map<String, Object>> deleteDevice(@PathVariable Long deviceId) {
        boolean success = omcDeviceService.deleteDevice(deviceId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "设备删除成功" : "设备删除失败");
        return ResponseEntity.ok(result);
    }
} 