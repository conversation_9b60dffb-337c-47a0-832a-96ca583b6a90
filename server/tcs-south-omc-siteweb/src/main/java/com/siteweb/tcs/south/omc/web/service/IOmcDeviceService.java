package com.siteweb.tcs.south.omc.web.service;

import com.siteweb.tcs.south.omc.dal.dto.OmcDeviceDTO;
import com.siteweb.tcs.south.omc.dal.entity.OmcDevice;

import java.time.LocalDateTime;
import java.util.List;

public interface IOmcDeviceService {
    OmcDeviceDTO getDeviceByName(String deviceName);
    List<OmcDeviceDTO> listActiveDevices(LocalDateTime minCreateTime);
    boolean saveDevice(OmcDevice device);
    boolean updateDevice(OmcDevice device);
    boolean deleteDevice(Long deviceId);
} 