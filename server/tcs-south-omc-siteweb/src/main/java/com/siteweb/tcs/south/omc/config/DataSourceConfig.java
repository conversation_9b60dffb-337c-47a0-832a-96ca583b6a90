package com.siteweb.tcs.south.omc.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.south.omc.dal.mapper"}, sqlSessionFactoryRef = "omcSqlSessionFactory")
public class DataSourceConfig {
    
    @Bean(name = "omcDataSourceProperties")
    @ConfigurationProperties(prefix = "plugin.datasource.omc")
    public DataSourceProperties omcDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "omcDataSource")
    public DataSource omcDataSource(@Qualifier("omcDataSourceProperties") DataSourceProperties dataSourceProperties) {
        return dataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "omcTransactionManager")
    public DataSourceTransactionManager omcTransactionManager(@Qualifier("omcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "omcSqlSessionFactory")
    public SqlSessionFactory omcSqlSessionFactory(@Qualifier("omcDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/south-omc-siteweb/*.xml"));
        return bean.getObject();
    }
} 