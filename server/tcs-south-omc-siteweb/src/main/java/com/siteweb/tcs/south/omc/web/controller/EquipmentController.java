package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.dto.SwitchTemplateDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备控制器
 * 负责设备的增删改查和相关业务操作
 */
@Slf4j
@RestController
@RequestMapping("/equipment")
public class EquipmentController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 根据ID获取设备配置
     *
     * @param id 设备ID
     * @return 设备配置信息
     */
    @GetMapping("/config/{id}")
    public ResponseEntity<ResponseResult> getConfig(@PathVariable("id") Integer id) {
        try {
            Equipment equipment = sitewebPersistentService.getConfigAPI().findByIdForEquipment(id);
            if (equipment != null) {
                return ResponseHelper.successful(equipment);
            } else {
                return ResponseHelper.failed("设备不存在");
            }
        } catch (Exception e) {
            log.error("获取设备配置失败, ID: {}", id, e);
            return ResponseHelper.failed("获取设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建设备配置（按照原配置工具的 createEquipmentV3 逻辑，去掉v3字样）
     *
     * @param createEquipmentDto 创建设备DTO
     * @return 创建结果
     */
    @PostMapping("/config")
    public ResponseEntity<ResponseResult> createConfig(@RequestBody CreateEquipmentDto createEquipmentDto) {
        try {
            // 按照原配置工具的逻辑进行重复名称检查
            boolean repeatName = (Boolean) sitewebPersistentService.getConfigAPI().checkEquipmentNameExistsForEquipment(
                null, createEquipmentDto.getMonitorUnitId(), createEquipmentDto.getEquipmentName());
            if (repeatName) {
                return ResponseHelper.failed("设备名称重复");
            }

            // 验证必要字段（去掉局站概念，设置默认值）
            if (createEquipmentDto.getMonitorUnitId() == null) {
                return ResponseHelper.failed("监控单元ID不能为空");
            }

            // 设置默认值（去掉局站和局房概念）
            createEquipmentDto.setStationId(0);
            createEquipmentDto.setHouseId(0);

            // 转换DTO为Entity并调用service
            Equipment eq = createEquipmentDto.toEntrty();
            Equipment equipment = sitewebPersistentService.getConfigAPI().createForEquipment(eq);

            if (equipment != null) {
                return ResponseHelper.successful(equipment);
            } else {
                return ResponseHelper.failed("设备创建失败");
            }
        } catch (Exception e) {
            log.error("创建设备配置失败", e);
            return ResponseHelper.failed("创建设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新设备配置
     *
     * @param equipmentDetailDTO 设备详情DTO
     * @return 更新结果
     */
    @PutMapping("/config")
    public ResponseEntity<ResponseResult> updateConfig(@RequestBody EquipmentDetailDTO equipmentDetailDTO) {
        try {
            // 确保stationId为0（去掉局站概念）
            if (equipmentDetailDTO.getStationId() == null) {
                equipmentDetailDTO.setStationId(0);
            }
            Equipment equipment = (Equipment) sitewebPersistentService.getConfigAPI().updateForEquipment(equipmentDetailDTO);
            if (equipment != null) {
                return ResponseHelper.successful(equipment);
            } else {
                return ResponseHelper.failed("设备更新失败");
            }
        } catch (Exception e) {
            log.error("更新设备配置失败", e);
            return ResponseHelper.failed("更新设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除设备配置
     *
     * @param id 设备ID
     * @return 删除结果
     */
    @DeleteMapping("/config/{id}")
    public ResponseEntity<ResponseResult> deleteConfig(@PathVariable("id") Integer id) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().deleteForEquipment(id);
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("设备删除失败");
            }
        } catch (Exception e) {
            log.error("删除设备配置失败, ID: {}", id, e);
            return ResponseHelper.failed("删除设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据监控单元ID获取设备列表
     *
     * @param monitorUnitId 监控单元ID
     * @return 设备列表
     */
    @GetMapping("/monitor-unit/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getByMonitorUnitId(@PathVariable("monitorUnitId") Integer monitorUnitId) {
        try {
            List<Equipment> equipments = sitewebPersistentService.getConfigAPI().findByMonitorUnitIdForEquipment(monitorUnitId);
            return ResponseHelper.successful(equipments);
        } catch (Exception e) {
            log.error("根据监控单元ID获取设备失败, monitorUnitId: {}", monitorUnitId, e);
            return ResponseHelper.failed("获取设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 切换设备模板
     *
     * @param switchTemplateDTO 切换模板DTO
     * @return 切换结果
     */
    @PostMapping("/switchtemplate")
    public ResponseEntity<ResponseResult> switchTemplate(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().switchTemplateForEquipment(switchTemplateDTO);
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("设备模板切换失败");
            }
        } catch (Exception e) {
            log.error("切换设备模板失败", e);
            return ResponseHelper.failed("切换设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 设备实例化
     *
     * @param equipmentId 设备ID
     * @return 实例化结果
     */
    @PostMapping("/instance/{equipmentId}")
    public ResponseEntity<ResponseResult> equipmentInstance(@PathVariable("equipmentId") Integer equipmentId) {
        try {
            Integer templateId = sitewebPersistentService.getConfigAPI().equipmentInstanceForEquipment(equipmentId);
            if (templateId != null) {
                return ResponseHelper.successful(templateId);
            } else {
                return ResponseHelper.failed("设备实例化失败");
            }
        } catch (Exception e) {
            log.error("设备实例化失败, equipmentId: {}", equipmentId, e);
            return ResponseHelper.failed("设备实例化失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备模板详情
     * 注意：这个方法调用的是设备模板相关的API，不是设备API
     *
     * @param templateId 模板ID
     * @return 设备模板详情
     */
    @GetMapping("/template/{templateId}")
    public ResponseEntity<ResponseResult> getEquipmentTemplate(@PathVariable("templateId") Integer templateId) {
        try {
            // 这里应该调用设备模板相关的API
            EquipmentTemplate template = sitewebPersistentService.getConfigAPI().findByIdForEquipmentTemplate(templateId);
            if (template != null) {
                return ResponseHelper.successful(template);
            } else {
                return ResponseHelper.failed("设备模板不存在");
            }
        } catch (Exception e) {
            log.error("获取设备模板详情失败, templateId: {}", templateId, e);
            return ResponseHelper.failed("获取设备模板详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索设备列表
     *
     * @param monitorUnitId 监控单元ID (可选)
     * @param equipmentName 设备名称 (可选，支持模糊查询)
     * @return 设备列表
     */
    @GetMapping("/search")
    public ResponseEntity<ResponseResult> searchEquipment(
            @RequestParam(value = "monitorUnitId", required = false) Integer monitorUnitId,
            @RequestParam(value = "equipmentName", required = false) String equipmentName) {
        try {
            List<Equipment> equipmentList = sitewebPersistentService.getConfigAPI()
                    .searchEquipmentList(monitorUnitId, equipmentName);
            return ResponseHelper.successful(equipmentList);
        } catch (Exception e) {
            log.error("搜索设备列表失败, monitorUnitId: {}, equipmentName: {}", monitorUnitId, equipmentName, e);
            return ResponseHelper.failed("搜索设备列表失败: " + e.getMessage());
        }
    }
}
