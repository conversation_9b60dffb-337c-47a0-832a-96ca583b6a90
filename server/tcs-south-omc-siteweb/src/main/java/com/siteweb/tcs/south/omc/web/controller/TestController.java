package com.siteweb.tcs.south.omc.web.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/api/south/omc-siteweb/test")
public class TestController {
    @GetMapping(value = "/helloworld")
    public ResponseEntity<String> helloWorld() {
        log.info("测试接口被访问");
        return ResponseEntity.ok("OMC智慧运维插件测试接口运行正常");
    }
} 