package com.siteweb.stream.common.stream;

import com.siteweb.stream.common.util.Parameters;
import lombok.Data;

/**
 * @ClassName: StreamFlowOption
 * @descriptions: 流配置
 * @author: xsx
 * @date: 2/15/2025 5:41 PM
 **/
@Data
public class StreamFlowOption {
    private Long streamGraphId;
    private Long streamFlowId;
    //实体流参数
    private Parameters streamFlowParameters;
//    private List<StreamShapeOption> shapeNodeOptionList;
}
