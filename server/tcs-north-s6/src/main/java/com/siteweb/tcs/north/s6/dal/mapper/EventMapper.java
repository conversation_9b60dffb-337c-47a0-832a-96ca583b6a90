package com.siteweb.tcs.north.s6.dal.mapper;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.StatementType;

import java.util.Map;

@Mapper
public interface EventMapper {
    @Select(value = " CALL PNL_SaveEventResponse(\n" +
            "        #{stationId}, #{equipmentId}, #{eventId}, #{eventConditionId},\n" +
            "        #{sequenceId}, #{startTime}, #{endTime}, #{overturn},\n" +
            "        #{meanings}, #{eventValue}, #{baseTypeId},\n" +
            "        #{ret, mode=OUT, jdbcType=INTEGER})")
    @Options(statementType = StatementType.CALLABLE)
    Integer saveEventResponse(Map<String, Object> params);
}
