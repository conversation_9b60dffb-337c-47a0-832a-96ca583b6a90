package com.siteweb.tcs.north.s6.connector.protocol;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR> (2024-08-09)
 **/
public enum MessageType {
    ControlRequest(50, "控制请求"),
    ControlResponse(51, "控制响应"),
    ControlCancelRequest(52, "控制取消请求"),
    ControlCancelResponse(53, "控制取消响应"),
    ControlNotify(54, "控制通知");

    MessageType(int value, String desc) {
        this.value = value;
    }

    private final int value;

    @JsonValue
    public int getValue() {
        return this.value;
    }


    @JsonCreator
    public static MessageType fromValue(int value) {
        for (MessageType day : MessageType.values()) {
            if (day.getValue() == value) {
                return day;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }


}