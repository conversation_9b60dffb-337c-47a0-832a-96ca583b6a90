package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.hub.domain.letter.*;
import com.siteweb.tcs.north.s6.connector.letter.*;
import com.siteweb.tcs.north.s6.dal.entity.BatteryDischargeRecordCTCC;
import com.siteweb.tcs.north.s6.dal.entity.Signal;
import com.siteweb.tcs.north.s6.dal.provider.HistoryDataProvider;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

public class EquipmentHistoryDataWatcher extends AbstractActor {

    private final HistoryDataProvider historyDataProvider;

    private final ActorProbe probe = createProbe(this);

    private final ActorRef influxDBSink;

    private final String HISTORY_DATA = "history_data";

    /**
     * 模板ID和信号ID的缓存
     */
    private Map<Integer, Map<Integer, Signal>> equipmentTemplateIdAndSignalCache = new ConcurrentHashMap<>();

    public EquipmentHistoryDataWatcher(ActorRef influxDBSink) {
        this.historyDataProvider = PluginScope.getBean(HistoryDataProvider.class);
        this.probe.addWindowLog(HISTORY_DATA);
        this.influxDBSink = influxDBSink;
    }

    public static Props props(ActorRef influxDBSink) {
        return Props.create(EquipmentHistoryDataWatcher.class, () -> new EquipmentHistoryDataWatcher(influxDBSink));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentHisData.class, this::saveEquipmentHisData)
                .match(EquipmentHisBatCurveData.class, this::saveEquipmentHisBatCurveData)
                .match(EquipmentHistoryData.class, this::saveEquipmentHistoryData)
                .build();
    }

    /**
     * 保存历史数据
     */
    private void saveEquipmentHisData(EquipmentHisData message) {
        this.probe.enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO, "[HistoryData] Saving EquipmentHisData data" + message.toString()));

        // 从缓存中获取信号信息
        Map<Integer, Signal> signalMap = getSignalsByTemplateId(message.getEquipmentTemplateId());
        StoreInfluxDBlAction action = new StoreInfluxDBlAction();
        for (EquipmentSignalHisData signal : message.getSignalLists()) {
            Signal cachedSignal = signalMap.get(signal.getSignalId());
            if (cachedSignal != null) {
                InfluxDBStoreItem signalItem = new InfluxDBStoreItem();
                signalItem.setSignalId(String.valueOf(signal.getSignalId()));
                signalItem.setDeviceId(String.valueOf(signal.getEquipmentId()));
                signalItem.setStationId(String.valueOf(message.getStationId()));
                signalItem.setSignalType("5");
                // baseTypeId非空判断
                Long baseTypeId = cachedSignal.getBaseTypeId();
                if (baseTypeId != null) {
                    signalItem.setBaseTypeId(String.valueOf(baseTypeId));
                }
                signalItem.setValue(signal.getTriggerVal());
                signalItem.setRecordTime(signal.getRecordTime());
                action.getItems().add(signalItem);
            }
        }
        influxDBSink.tell(action, this.getSelf());
    }

    /**
     * 根据模板ID获取信号列表，并缓存
     */
    private Map<Integer, Signal> getSignalsByTemplateId(Integer equipmentTemplateId) {
        // 从缓存中获取信号Map
        if (equipmentTemplateIdAndSignalCache.containsKey(equipmentTemplateId)) {
            return equipmentTemplateIdAndSignalCache.get(equipmentTemplateId);
        }

        // 从数据库中获取信号列表
        List<Signal> signals = historyDataProvider.getSignalListByTemplateId(equipmentTemplateId);

        // 将信号列表转换为Map<SignalId, Signal>
        Map<Integer, Signal> signalMap = new ConcurrentHashMap<>();
        for (Signal signal : signals) {
            signalMap.put(signal.getSignalId(), signal);
        }

        // 将模板ID和信号Map存入缓存
        equipmentTemplateIdAndSignalCache.put(equipmentTemplateId, signalMap);
        return signalMap;
    }

    /**
     * 保存电池历史曲线数据
     */
    private void saveEquipmentHisBatCurveData(EquipmentHisBatCurveData message) {
        if (message.getSignalLists() == null || message.getSignalLists().isEmpty()) {
            return;
        }
        this.probe.enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO, "[HistoryData] Saving EquipmentHisBatCurveData data" + message.toString()));
        HistoryBatCurveAction action = new HistoryBatCurveAction();
        List<HistoryBatCurveItem> list = convertToHistoryBatCurveItems(message);
        action.setItems(list);
        BatteryDischargeRecordCTCC record = new BatteryDischargeRecordCTCC();
        record.setStartTime(message.getStartTime());
        record.setEndTime(list.get(list.size() - 1).getRecordTime());
        record.setEquipmentId(Integer.valueOf(message.getEquipmentId()));
        record.setStationId(message.getStationId());
        historyDataProvider.insertEquipmentHisBatRecord(record);
        influxDBSink.tell(action, this.getSelf());
    }


    public List<HistoryBatCurveItem> convertToHistoryBatCurveItems(EquipmentHisBatCurveData curveData) {
        List<HistoryBatCurveItem> curveItems = new ArrayList<>();

        // 获取信号ID列表，拆分 signalIds 字符串
        String[] signalIdArray = curveData.getSignalIds().split(",");

        // 初始时间是曲线的开始时间
        LocalDateTime currentTime = curveData.getStartTime();

        for (EquipmentHisBatRecord record : curveData.getSignalLists()) {
            // 每条记录的时间是与前一条记录的时间进行累加
            currentTime = currentTime.plusSeconds(record.getOffsetSeconds());
            HistoryBatCurveItem curveItem = new HistoryBatCurveItem();
            curveItem.setStationId(String.valueOf(curveData.getStationId()));
            curveItem.setDeviceId(curveData.getEquipmentId());
            curveItem.setVoltageSignalId(signalIdArray[0].trim());
            curveItem.setCurrentSignalId(signalIdArray[1].trim());
            curveItem.setCurrentValue(record.getCurrentVal());
            curveItem.setVoltageValue(record.getVoltageVal());
            curveItem.setRecordTime(currentTime);
            curveItem.setVoltageType(record.getVoltageType());
            // 将这个 item 添加到列表中
            curveItems.add(curveItem);

        }

        return curveItems;
    }

    /**
     * 保存历史统计数据
     */
    private void saveEquipmentHistoryData(EquipmentHistoryData message) {
        this.probe.enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO, "[HistoryData] Saving EquipmentHistoryData data" + message.toString()));
        HistoryStatisticsAction action = new HistoryStatisticsAction();
        List<HistoryStatisticsItem> list = action.getItems();
        for (EquipmentSignalHistoryData signal : message.getSignalLists()) {
            HistoryStatisticsItem item = new HistoryStatisticsItem();
            item.setSignalId(signal.getSignalId())
                    .setEquipmentId(String.valueOf(message.getEquipmentId()))
                    .setStationId(message.getStationId())
                    .setMinVal(signal.getMinVal())
                    .setMinValTime(signal.getMinValTime())
                    .setMaxVal(signal.getMaxVal())
                    .setMaxValTime(signal.getMaxValTime())
                    .setPeriodStart(signal.getPeriodStart())
                    .setPeriodEnd(signal.getPeriodEnd());
            list.add(item);
        }
        influxDBSink.tell(action, this.getSelf());
    }


    /**
     * 每天凌晨12点清空缓存
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void clearCache() {
        equipmentTemplateIdAndSignalCache.clear();
        this.probe.enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO, "[Cache] Cleared equipment template signal cache at midnight"));
    }


    @Override
    public void postStop() {
        removeProbe(this.probe);
    }
}
