package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.north.s6.connector.letter.HistoryBatCurveAction;
import com.siteweb.tcs.north.s6.connector.letter.HistoryStatisticsAction;
import com.siteweb.tcs.north.s6.connector.letter.StoreInfluxDBlAction;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * <AUTHOR> (2024-08-28)
 **/
public class InfluxDBSink extends AbstractActor {
    private final ActorProbe probe = createProbe(this);
    private final HistoryDataBatchProcessor batchProcessor = new HistoryDataBatchProcessor();

    public static Props props() {
        return Props.create(InfluxDBSink.class, InfluxDBSink::new);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(StoreInfluxDBlAction.class, this::onStoreToInfluxDB)
                .match(HistoryStatisticsAction.class, this::onHistoryStatistics)
                .match(HistoryBatCurveAction.class, this::onHistoryBatCurve)
                .build();
    }

    private void onHistoryBatCurve(HistoryBatCurveAction action) {
        batchProcessor.write(action);
    }

    private void onHistoryStatistics(HistoryStatisticsAction action) {
        batchProcessor.write(action);
    }

    private void onStoreToInfluxDB(StoreInfluxDBlAction action) {
        batchProcessor.write(action);
    }

    @Override
    public void preStart() {

    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }


}

