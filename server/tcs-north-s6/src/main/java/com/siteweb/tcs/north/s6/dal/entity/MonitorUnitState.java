package com.siteweb.tcs.north.s6.dal.entity;

import com.siteweb.tcs.hub.domain.letter.MonitorUnitChange;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


/**
 * 监控单元状态
 */
@Data
public class MonitorUnitState {
    private Integer monitorUnitId;

    private Integer state;

    private String heartBeatTime;

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static MonitorUnitState fromMonitorUnitChange(MonitorUnitChange monitorUnitChange){
        MonitorUnitState res = new MonitorUnitState();
        res.setState(monitorUnitChange.getConnectState());
        res.setMonitorUnitId(monitorUnitChange.getMonitorUnitId());
        String dateStr = LocalDateTime.now().format(dateTimeFormatter);
        res.setHeartBeatTime(dateStr);
        return res;
    }

    public static String formatDateTime(LocalDateTime localDateTime){
        return localDateTime.format(dateTimeFormatter);
    }
}
