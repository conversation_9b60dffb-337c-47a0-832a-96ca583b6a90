package com.siteweb.tcs.north.s6.config;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 自定义插件数据库数据源
 * 插件使用时应修改BeanName保持系统唯一
 **/
@Slf4j
@Configuration
@EnableConfigurationProperties
public class DataSourceConfig {
    @Bean(name = "myPluginDataSourceProperties")
    // TODO ConfigurationProperties 修改插件读取配置文件的数据源路径
    @ConfigurationProperties(prefix = "plugin.s6.datasource" /* 从Yaml文件的哪个地方读取数据库连接配置 */)
    public DataSourceProperties myPluginDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "myPluginDataSource")
    public DataSource myPluginDataSource(@Qualifier("myPluginDataSourceProperties") DataSourceProperties sitewebDataSourceProperties) {
        return sitewebDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "myPluginTransactionManager")
    public DataSourceTransactionManager myPluginTransactionManager(@Qualifier("myPluginDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
