package com.siteweb.tcs.north.s6.dal.provider;

import com.siteweb.tcs.north.s6.dal.entity.BatteryDischargeRecordCTCC;
import com.siteweb.tcs.north.s6.dal.entity.Signal;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HistoryDataProvider {

    /**
     * 根据设备模板ID获取信号列表
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 信号列表
     */
    List<Signal> getSignalListByTemplateId(Integer equipmentTemplateId);

    /**
     * 插入电池历史记录
     *
     * @param record 电池历史记录
     * @return 插入结果
     */
    int insertEquipmentHisBatRecord(BatteryDischargeRecordCTCC record);


}
