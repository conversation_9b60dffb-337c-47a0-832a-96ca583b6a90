package com.siteweb.tcs.middleware.common.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.middleware.common.config.SitewebMyBatisConfig;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.siteweb.dto.AccountDTO;
import com.siteweb.tcs.siteweb.dto.CenterDTO;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.entity.Door;
import com.siteweb.tcs.siteweb.mapper.HouseMapper;
import com.siteweb.tcs.siteweb.mapper.SamplerMapper;
import com.siteweb.tcs.siteweb.mapper.DoorMapper;
import com.siteweb.tcs.siteweb.service.IHouseService;
import com.siteweb.tcs.siteweb.service.ISamplerService;
import com.siteweb.tcs.siteweb.service.IDoorService;
import com.siteweb.tcs.siteweb.service.IAccountService;
import com.siteweb.tcs.siteweb.service.IBaseDicService;
import com.siteweb.tcs.siteweb.service.ICenterService;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.ResolvableType;

import javax.sql.DataSource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Siteweb持久化服务
 * 用于封装tcs-siteweb模块中的各种service，提供统一的访问接口
 * 此服务关联关系型数据库Resource，使用Resource的DataSource创建独立的MyBatis配置
 */
public class SitewebPersistentService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(SitewebPersistentService.class);

    /**
     * Siteweb包路径标识，用于筛选相关的IService实现
     */
    private static final String SITEWEB_PACKAGE_IDENTIFIER = "tcs.siteweb";

    private ApplicationContext applicationContext;

    /**
     * 独立的SqlSessionFactory，使用关联Resource的DataSource
     */
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 独立的SqlSessionTemplate
     */
    private SqlSessionTemplate sqlSessionTemplate;

    /**
     * 缓存实体类型到对应IService的映射
     * Key: 实体类的Class对象
     * Value: 对应的IService实例
     */
    private final Map<Class<?>, IService<?>> serviceCache = new ConcurrentHashMap<>();

    /**
     * 缓存服务名称到IService的映射
     * Key: 服务名称（如"houseServiceImpl"）
     * Value: 对应的IService实例
     */
    private final Map<String, Object> namedServiceCache = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param resource 关联的数据库Resource
     */
    public SitewebPersistentService(String id, String name, String description, Resource resource) {
        super(id, ServiceType.SITEWEB_PERSISTENT.getCode(), name, description, resource);
    }

    /**
     * 设置Spring应用上下文
     *
     * @param applicationContext Spring应用上下文
     */
    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    // 直接注入具体的服务Bean，提高性能和类型安全性
    @Autowired(required = false)
    private IHouseService houseService;

    @Autowired(required = false)
    private ISamplerService samplerService;

    @Autowired(required = false)
    private IDoorService doorService;

    @Autowired(required = false)
    private IAccountService accountService;

    @Autowired(required = false)
    private IBaseDicService baseDicService;

    @Autowired(required = false)
    private ICenterService centerService;

    /**
     * 获取tcs-siteweb中的服务实例（内部使用）
     *
     * @param serviceClass 服务类型
     * @param <T> 服务类型泛型
     * @return 服务实例
     * @throws MiddlewareTechnicalException 获取服务失败时抛出异常
     */
    private <T> T getSitewebService(Class<T> serviceClass) throws MiddlewareTechnicalException {
        try {
            if (applicationContext == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "ApplicationContext not initialized"
                );
            }
            return applicationContext.getBean(serviceClass);
        } catch (Exception e) {
            logger.error("Failed to get siteweb service: {}", serviceClass.getName(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get siteweb service: " + serviceClass.getName(),
                e
            );
        }
    }

    /**
     * 根据服务名称获取tcs-siteweb中的服务实例（内部使用）
     *
     * @param serviceName 服务名称
     * @param serviceClass 服务类型
     * @param <T> 服务类型泛型
     * @return 服务实例
     * @throws MiddlewareTechnicalException 获取服务失败时抛出异常
     */
    private <T> T getSitewebService(String serviceName, Class<T> serviceClass) throws MiddlewareTechnicalException {
        try {
            if (applicationContext == null) {
                throw new MiddlewareTechnicalException(
                    com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "ApplicationContext not initialized"
                );
            }
            return applicationContext.getBean(serviceName, serviceClass);
        } catch (Exception e) {
            logger.error("Failed to get siteweb service by name: {} ({})", serviceName, serviceClass.getName(), e);
            throw new MiddlewareTechnicalException(
                com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get siteweb service by name: " + serviceName,
                e
            );
        }
    }

    /**
     * 根据实体类型获取对应的IService实例
     *
     * @param entityClass 实体类型
     * @param <T> 实体类型泛型
     * @param <S> IService类型泛型
     * @return 对应的IService实例
     * @throws MiddlewareTechnicalException 获取失败时抛出异常
     */
    @SuppressWarnings("unchecked")
    private <T, S extends IService<T>> S getServiceByEntityType(Class<T> entityClass) throws MiddlewareTechnicalException {
        // 尝试从缓存获取，避免重复查找
        IService<?> service = serviceCache.get(entityClass);
        if (service != null) {
            return (S) service;
        }

        try {
            if (applicationContext == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "ApplicationContext not initialized"
                );
            }

            // 遍历 Spring 容器中所有 IService 类型的 Bean
            Map<String, IService> allServices = applicationContext.getBeansOfType(IService.class);
            logger.debug("Found {} IService beans in Spring context, looking for entity type: {}",
                allServices.size(), entityClass.getSimpleName());

            for (IService<?> iService : allServices.values()) {
                // 筛选包路径：只处理tcs.siteweb包下的IService实现
                String serviceClassName = iService.getClass().getName();
                if (!serviceClassName.contains(SITEWEB_PACKAGE_IDENTIFIER)) {
                    logger.trace("Skipping non-siteweb service: {}", serviceClassName);
                    continue;
                }

                // 使用 ResolvableType 获取 IService 的实际泛型参数
                ResolvableType resolvableType = ResolvableType.forClass(iService.getClass()).as(IService.class);

                // 检查是否存在泛型参数，并获取第一个泛型参数的类型
                if (resolvableType.hasGenerics()) {
                    Class<?> genericType = resolvableType.getGeneric(0).resolve();

                    // 如果泛型参数类型与传入的 entityClass 匹配，则找到了对应的 Service
                    if (genericType != null && genericType.equals(entityClass)) {
                        // 缓存找到的 Service 实例，提高后续访问性能
                        serviceCache.put(entityClass, iService);
                        logger.debug("Found and cached siteweb service for entity {}: {} ({})",
                            entityClass.getSimpleName(), iService.getClass().getSimpleName(), genericType.getSimpleName());
                        return (S) iService;
                    }
                } else {
                    logger.trace("Service {} has no generics, skipping", serviceClassName);
                }
            }

            // 如果遍历完所有 IService 仍未找到匹配的，则抛出异常
            // 收集所有siteweb服务的信息用于调试
            StringBuilder sitewebServices = new StringBuilder();
            for (IService<?> iService : allServices.values()) {
                String serviceClassName = iService.getClass().getName();
                if (serviceClassName.contains(SITEWEB_PACKAGE_IDENTIFIER)) {
                    ResolvableType resolvableType = ResolvableType.forClass(iService.getClass()).as(IService.class);
                    if (resolvableType.hasGenerics()) {
                        Class<?> genericType = resolvableType.getGeneric(0).resolve();
                        sitewebServices.append(String.format("%s<%s>, ",
                            iService.getClass().getSimpleName(),
                            genericType != null ? genericType.getSimpleName() : "?"));
                    }
                }
            }

            String errorMessage = String.format(
                "No IService found for entity type: %s. Available siteweb services: [%s]",
                entityClass.getName(),
                sitewebServices.length() > 0 ? sitewebServices.toString() : "none"
            );

            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                errorMessage
            );

        } catch (Exception e) {
            logger.error("Failed to get service for entity type: {}", entityClass.getName(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get service for entity type: " + entityClass.getName(),
                e
            );
        }
    }



    // =====================================================
    // 通用IService方法
    // =====================================================

    /**
     * 根据ID查询实体
     *
     * @param entityClass 实体类型
     * @param id 实体ID
     * @param <T> 实体类型泛型
     * @return 实体对象，如果不存在则返回null
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public <T> T getById(Class<T> entityClass, Serializable id) throws MiddlewareTechnicalException {
        try {
            if (entityClass == null || id == null) {
                logger.warn("Entity class or ID cannot be null");
                return null;
            }

            // 检查SqlSessionTemplate是否可用
            if (sqlSessionTemplate == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "SqlSessionTemplate not initialized for service: " + getId()
                );
            }

            // 获取对应的Mapper并使用MyBatis-Plus的selectById方法
            BaseMapper<T> mapper = getMapperForEntity(entityClass);
            T result = mapper.selectById(id);

            logger.debug("Found entity by ID {} using independent SqlSession: {}", id, result != null ? "found" : "not found");
            return result;

        } catch (Exception e) {
            logger.error("Failed to get {} by ID: {}", entityClass.getSimpleName(), id, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get " + entityClass.getSimpleName() + " by ID: " + id,
                e
            );
        }
    }

    /**
     * 查询所有实体
     *
     * @param entityClass 实体类型
     * @param <T> 实体类型泛型
     * @return 实体列表
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public <T> List<T> list(Class<T> entityClass) throws MiddlewareTechnicalException {
        try {
            if (entityClass == null) {
                logger.warn("Entity class cannot be null");
                return List.of();
            }

            // 检查SqlSessionTemplate是否可用
            if (sqlSessionTemplate == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "SqlSessionTemplate not initialized for service: " + getId()
                );
            }

            // 获取对应的Mapper并使用MyBatis-Plus的selectList方法
            BaseMapper<T> mapper = getMapperForEntity(entityClass);
            List<T> result = mapper.selectList(null); // null表示查询所有

            logger.debug("Found {} entities of type {} using independent SqlSession", result != null ? result.size() : 0, entityClass.getSimpleName());
            return result != null ? result : List.of();

        } catch (Exception e) {
            logger.error("Failed to list entities of type: {}", entityClass.getSimpleName(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to list entities of type: " + entityClass.getSimpleName(),
                e
            );
        }
    }

    /**
     * 保存实体
     *
     * @param entity 实体对象
     * @param <T> 实体类型泛型
     * @return 是否保存成功
     * @throws MiddlewareTechnicalException 保存失败时抛出异常
     */
    public <T> boolean save(T entity) throws MiddlewareTechnicalException {
        try {
            if (entity == null) {
                logger.warn("Entity cannot be null");
                return false;
            }

            @SuppressWarnings("unchecked")
            Class<T> entityClass = (Class<T>) entity.getClass();
            IService<T> service = getServiceByEntityType(entityClass);

            // 直接调用save方法，无需反射
            boolean result = service.save(entity);

            logger.debug("Save entity result: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("Failed to save entity: {}", entity.getClass().getSimpleName(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to save entity: " + entity.getClass().getSimpleName(),
                e
            );
        }
    }

    /**
     * 根据ID删除实体
     *
     * @param entityClass 实体类型
     * @param id 实体ID
     * @param <T> 实体类型泛型
     * @return 是否删除成功
     * @throws MiddlewareTechnicalException 删除失败时抛出异常
     */
    public <T> boolean removeById(Class<T> entityClass, Serializable id) throws MiddlewareTechnicalException {
        try {
            if (entityClass == null || id == null) {
                logger.warn("Entity class or ID cannot be null");
                return false;
            }

            IService<T> service = getServiceByEntityType(entityClass);

            // 直接调用removeById方法，无需反射
            boolean result = service.removeById(id);

            logger.debug("Remove entity by ID {} result: {}", id, result);
            return result;

        } catch (Exception e) {
            logger.error("Failed to remove {} by ID: {}", entityClass.getSimpleName(), id, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to remove " + entityClass.getSimpleName() + " by ID: " + id,
                e
            );
        }
    }

    // =====================================================
    // 特殊方法封装（带服务名称标识）
    // =====================================================

    /**
     * [HouseService] 根据ID查询局房
     *
     * @param houseId 局房ID
     * @return 局房信息，如果不存在则返回null
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public House findHouseById(Integer houseId) throws MiddlewareTechnicalException {
        try {
            if (houseId == null) {
                logger.warn("House ID cannot be null");
                return null;
            }

            // 直接通过applicationContext获取IHouseService，避免反射
            IHouseService houseService = applicationContext.getBean(IHouseService.class);

            // 直接调用方法，类型安全且高效
            House result = houseService.findByHouseId(houseId);

            logger.debug("Found house by ID {}: {}", houseId, result != null ? "found" : "not found");
            return result;

        } catch (Exception e) {
            logger.error("Failed to get house by ID: {}", houseId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get house by ID: " + houseId,
                e
            );
        }
    }

    /**
     * [HouseService] 查询站点的默认局房
     *
     * @param stationId 站点ID
     * @return 默认局房信息，如果不存在则返回null
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public House findStationDefaultHouse(Integer stationId) throws MiddlewareTechnicalException {
        try {
            if (stationId == null) {
                logger.warn("Station ID cannot be null");
                return null;
            }

            // 直接通过applicationContext获取IHouseService，避免反射
            IHouseService houseService = applicationContext.getBean(IHouseService.class);

            // 直接调用方法，类型安全且高效
            House result = houseService.findStationDefaultHouse(stationId);

            logger.debug("Found default house for station {}: {}", stationId, result != null ? "found" : "not found");
            return result;

        } catch (Exception e) {
            logger.error("Failed to get default house for station: {}", stationId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get default house for station: " + stationId,
                e
            );
        }
    }

    /**
     * [SamplerService] 根据协议码查询采集器
     *
     * @param protocolCode 协议码
     * @return 采集器信息，如果不存在则返回null
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public Sampler findSamplerByProtocolCode(String protocolCode) throws MiddlewareTechnicalException {
        try {
            if (protocolCode == null || protocolCode.trim().isEmpty()) {
                logger.warn("Protocol code cannot be null or empty");
                return null;
            }

            // 直接通过applicationContext获取ISamplerService，避免反射
            ISamplerService samplerService = applicationContext.getBean(ISamplerService.class);

            // 直接调用方法，类型安全且高效
            Sampler result = samplerService.getSamplerByProtocolCode(protocolCode);

            logger.debug("Found sampler by protocol code {}: {}", protocolCode, result != null ? "found" : "not found");
            return result;

        } catch (Exception e) {
            logger.error("Failed to get sampler by protocol code: {}", protocolCode, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get sampler by protocol code: " + protocolCode,
                e
            );
        }
    }

    // =====================================================
    // Sampler相关业务方法
    // =====================================================

    /**
     * 根据协议码查询采集器
     *
     * @param protocolCode 协议码
     * @return 采集器信息，如果不存在则返回null
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public Sampler getSamplerByProtocolCode(String protocolCode) throws MiddlewareTechnicalException {
        try {
            if (protocolCode == null || protocolCode.trim().isEmpty()) {
                logger.warn("Protocol code cannot be null or empty");
                return null;
            }

            // 直接通过applicationContext获取ISamplerService，避免反射
            ISamplerService samplerService = applicationContext.getBean(ISamplerService.class);

            // 直接调用方法，类型安全且高效
            Sampler result = samplerService.getSamplerByProtocolCode(protocolCode);

            logger.debug("Found sampler by protocol code {}: {}", protocolCode, result != null ? "found" : "not found");
            return result;

        } catch (Exception e) {
            logger.error("Failed to get sampler by protocol code: {}", protocolCode, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get sampler by protocol code: " + protocolCode,
                e
            );
        }
    }

    /**
     * 查询所有采集器
     *
     * @return 采集器列表
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public List<Sampler> getAllSamplers() throws MiddlewareTechnicalException {
        try {
            // 直接通过applicationContext获取ISamplerService，避免反射
            ISamplerService samplerService = applicationContext.getBean(ISamplerService.class);

            // 直接调用方法，类型安全且高效
            List<Sampler> result = samplerService.list();

            logger.debug("Found {} samplers", result != null ? result.size() : 0);
            return result != null ? result : List.of();

        } catch (Exception e) {
            logger.error("Failed to get all samplers", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get all samplers",
                e
            );
        }
    }

    // =====================================================
    // Door相关业务方法
    // =====================================================

    /**
     * 根据设备ID查询门设备
     *
     * @param equipmentId 设备ID
     * @return 门设备列表
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public List<Door> getDoorsByEquipmentId(Integer equipmentId) throws MiddlewareTechnicalException {
        try {
            if (equipmentId == null) {
                logger.warn("Equipment ID cannot be null");
                return List.of();
            }

            // 直接通过applicationContext获取IDoorService，避免反射
            IDoorService doorService = applicationContext.getBean(IDoorService.class);

            // 直接调用方法，类型安全且高效
            List<Door> result = doorService.getDoorByEquipmentId(equipmentId);

            logger.debug("Found {} doors for equipment {}", result != null ? result.size() : 0, equipmentId);
            return result != null ? result : List.of();

        } catch (Exception e) {
            logger.error("Failed to get doors by equipment ID: {}", equipmentId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get doors by equipment ID: " + equipmentId,
                e
            );
        }
    }

    // =====================================================
    // Account相关业务方法
    // =====================================================

    /**
     * [AccountService] 根据登录ID查找账户
     *
     * @param logonId 登录ID
     * @return 账户DTO列表
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public List<AccountDTO> findAccountsByLogonId(String logonId) throws MiddlewareTechnicalException {
        try {
            if (logonId == null || logonId.trim().isEmpty()) {
                logger.warn("Logon ID cannot be null or empty");
                return List.of();
            }

            // 直接通过applicationContext获取IAccountService，避免反射
            IAccountService accountService = applicationContext.getBean(IAccountService.class);

            // 直接调用方法，类型安全且高效
            List<AccountDTO> result = accountService.findByLogonId(logonId);

            logger.debug("Found {} accounts by logon ID {}", result != null ? result.size() : 0, logonId);
            return result != null ? result : List.of();

        } catch (Exception e) {
            logger.error("Failed to find accounts by logon ID: {}", logonId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to find accounts by logon ID: " + logonId,
                e
            );
        }
    }

    /**
     * [AccountService] 根据手机号查找账户
     *
     * @param mobile 手机号
     * @return 账户DTO列表
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public List<AccountDTO> findAccountsByMobile(String mobile) throws MiddlewareTechnicalException {
        try {
            if (mobile == null || mobile.trim().isEmpty()) {
                logger.warn("Mobile cannot be null or empty");
                return List.of();
            }

            // 直接通过applicationContext获取IAccountService，避免反射
            IAccountService accountService = applicationContext.getBean(IAccountService.class);

            // 直接调用方法，类型安全且高效
            List<AccountDTO> result = accountService.findByMobile(mobile);

            logger.debug("Found {} accounts by mobile {}", result != null ? result.size() : 0, mobile);
            return result != null ? result : List.of();

        } catch (Exception e) {
            logger.error("Failed to find accounts by mobile: {}", mobile, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to find accounts by mobile: " + mobile,
                e
            );
        }
    }

    /**
     * [AccountService] 根据用户ID查找账户
     *
     * @param userId 用户ID
     * @return 账户DTO
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public AccountDTO findAccountByUserId(Integer userId) throws MiddlewareTechnicalException {
        try {
            if (userId == null) {
                logger.warn("User ID cannot be null");
                return null;
            }

            // 直接通过applicationContext获取IAccountService，避免反射
            IAccountService accountService = applicationContext.getBean(IAccountService.class);

            // 直接调用方法，类型安全且高效
            AccountDTO result = accountService.findByUserId(userId);

            logger.debug("Found account by user ID {}: {}", userId, result != null ? "found" : "not found");
            return result;

        } catch (Exception e) {
            logger.error("Failed to find account by user ID: {}", userId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to find account by user ID: " + userId,
                e
            );
        }
    }

    /**
     * [AccountService] 查找所有账户
     *
     * @return 所有账户DTO列表
     * @throws MiddlewareTechnicalException 查询失败时抛出异常
     */
    public List<AccountDTO> findAllAccounts() throws MiddlewareTechnicalException {
        try {
            // 直接通过applicationContext获取IAccountService，避免反射
            IAccountService accountService = applicationContext.getBean(IAccountService.class);

            // 直接调用方法，类型安全且高效
            List<AccountDTO> result = accountService.findAll();

            logger.debug("Found {} accounts", result != null ? result.size() : 0);
            return result != null ? result : List.of();

        } catch (Exception e) {
            logger.error("Failed to find all accounts", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to find all accounts",
                e
            );
        }
    }

    /**
     * [AccountService] 为负用户ID更新中心ID
     *
     * @param centerId 中心ID
     * @throws MiddlewareTechnicalException 更新失败时抛出异常
     */
    public void updateCenterIdForNegativeUserId(int centerId) throws MiddlewareTechnicalException {
        try {
            // 直接通过applicationContext获取IAccountService，避免反射
            IAccountService accountService = applicationContext.getBean(IAccountService.class);

            // 直接调用方法，类型安全且高效
            accountService.updateCenterIdForNegativeUserId(centerId);

            logger.debug("Updated center ID {} for negative user IDs", centerId);

        } catch (Exception e) {
            logger.error("Failed to update center ID for negative user IDs: {}", centerId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to update center ID for negative user IDs: " + centerId,
                e
            );
        }
    }

    // =====================================================
    // BaseDic相关业务方法
    // =====================================================

    /**
     * [BaseDicService] 更新模板中的基类信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @throws MiddlewareTechnicalException 更新失败时抛出异常
     */
    public void updateBaseClassStandardDictionary(Integer equipmentTemplateId) throws MiddlewareTechnicalException {
        try {
            if (equipmentTemplateId == null) {
                logger.warn("Equipment template ID cannot be null");
                return;
            }

            // 直接通过applicationContext获取IBaseDicService，避免反射
            IBaseDicService baseDicService = applicationContext.getBean(IBaseDicService.class);

            // 直接调用方法，类型安全且高效
            baseDicService.updateBaseClassStandardDictionary(equipmentTemplateId);

            logger.debug("Updated base class standard dictionary for equipment template ID: {}", equipmentTemplateId);

        } catch (Exception e) {
            logger.error("Failed to update base class standard dictionary for equipment template ID: {}", equipmentTemplateId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to update base class standard dictionary for equipment template ID: " + equipmentTemplateId,
                e
            );
        }
    }

    /**
     * [BaseDicService] 检查指定基类ID是否存在
     *
     * @param baseTypeId 基类ID
     * @return 是否存在
     * @throws MiddlewareTechnicalException 检查失败时抛出异常
     */
    public boolean existsByBaseTypeId(Long baseTypeId) throws MiddlewareTechnicalException {
        try {
            if (baseTypeId == null) {
                logger.warn("Base type ID cannot be null");
                return false;
            }

            // 直接通过applicationContext获取IBaseDicService，避免反射
            IBaseDicService baseDicService = applicationContext.getBean(IBaseDicService.class);

            // 直接调用方法，类型安全且高效
            boolean result = baseDicService.existsByBaseTypeId(baseTypeId);

            logger.debug("Base type ID {} exists: {}", baseTypeId, result);
            return result;

        } catch (Exception e) {
            logger.error("Failed to check if base type ID exists: {}", baseTypeId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to check if base type ID exists: " + baseTypeId,
                e
            );
        }
    }

    /**
     * [BaseDicService] 生成基类字典
     *
     * @param baseTypeId 目标基类ID
     * @param sourceId 源基类ID
     * @throws MiddlewareTechnicalException 生成失败时抛出异常
     */
    public void generateBaseDic(Long baseTypeId, Long sourceId) throws MiddlewareTechnicalException {
        try {
            if (baseTypeId == null || sourceId == null) {
                logger.warn("Base type ID and source ID cannot be null");
                return;
            }

            // 直接通过applicationContext获取IBaseDicService，避免反射
            IBaseDicService baseDicService = applicationContext.getBean(IBaseDicService.class);

            // 直接调用方法，类型安全且高效
            baseDicService.generateBaseDic(baseTypeId, sourceId);

            logger.debug("Generated base dic for base type ID {} from source ID {}", baseTypeId, sourceId);

        } catch (Exception e) {
            logger.error("Failed to generate base dic for base type ID {} from source ID {}", baseTypeId, sourceId, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to generate base dic for base type ID " + baseTypeId + " from source ID " + sourceId,
                e
            );
        }
    }

    // =====================================================
    // Center相关业务方法
    // =====================================================

    /**
     * [CenterService] 创建监控中心
     *
     * @param centerDTO 监控中心DTO
     * @throws MiddlewareTechnicalException 创建失败时抛出异常
     */
    public void createCenter(CenterDTO centerDTO) throws MiddlewareTechnicalException {
        try {
            if (centerDTO == null) {
                logger.warn("Center DTO cannot be null");
                return;
            }

            // 直接通过applicationContext获取ICenterService，避免反射
            ICenterService centerService = applicationContext.getBean(ICenterService.class);

            // 直接调用方法，类型安全且高效
            centerService.create(centerDTO);

            logger.debug("Created center: {}", centerDTO);

        } catch (Exception e) {
            logger.error("Failed to create center: {}", centerDTO, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to create center: " + centerDTO,
                e
            );
        }
    }

    /**
     * [CenterService] 验证监控中心DTO
     *
     * @param centerDTO 监控中心DTO
     * @throws MiddlewareTechnicalException 验证失败时抛出异常
     */
    public void validateCenterDTO(CenterDTO centerDTO) throws MiddlewareTechnicalException {
        try {
            if (centerDTO == null) {
                logger.warn("Center DTO cannot be null");
                return;
            }

            // 直接通过applicationContext获取ICenterService，避免反射
            ICenterService centerService = applicationContext.getBean(ICenterService.class);

            // 直接调用方法，类型安全且高效
            centerService.validateCenterDTO(centerDTO);

            logger.debug("Validated center DTO: {}", centerDTO);

        } catch (Exception e) {
            logger.error("Failed to validate center DTO: {}", centerDTO, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to validate center DTO: " + centerDTO,
                e
            );
        }
    }

    /**
     * 判断资源类型是否支持
     *
     * @param resourceType 资源类型
     * @return 是否支持
     */
    private boolean isSupportedResourceType(String resourceType) {
        // 支持的关系型数据库资源类型
        return "MYSQL".equalsIgnoreCase(resourceType) ||
               "H2".equalsIgnoreCase(resourceType) ||
               "POSTGRESQL".equalsIgnoreCase(resourceType);
    }

    /**
     * 获取实体对应的Mapper
     *
     * @param entityClass 实体类
     * @param <T> 实体类型
     * @return BaseMapper实例
     * @throws MiddlewareTechnicalException 如果找不到对应的Mapper
     */
    @SuppressWarnings("unchecked")
    private <T> BaseMapper<T> getMapperForEntity(Class<T> entityClass) throws MiddlewareTechnicalException {
        try {
            // 根据实体类型获取对应的Mapper
            if (House.class.equals(entityClass)) {
                return (BaseMapper<T>) sqlSessionTemplate.getMapper(HouseMapper.class);
            } else if (Sampler.class.equals(entityClass)) {
                return (BaseMapper<T>) sqlSessionTemplate.getMapper(SamplerMapper.class);
            } else if (Door.class.equals(entityClass)) {
                return (BaseMapper<T>) sqlSessionTemplate.getMapper(DoorMapper.class);
            } else {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Unsupported entity type: " + entityClass.getName()
                );
            }
        } catch (Exception e) {
            logger.error("Failed to get mapper for entity: {}", entityClass.getName(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to get mapper for entity: " + entityClass.getName(),
                e
            );
        }
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.info("Initializing SitewebPersistentService: {}", getId());

        // 验证关联的Resource
        Resource resource = getResource();
        if (resource == null) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "SitewebPersistentService requires a database resource: " + getId()
            );
        }

        // 验证Resource类型
        String resourceType = resource.getType();
        if (!isSupportedResourceType(resourceType)) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Unsupported resource type for SitewebPersistentService: " + resourceType + ", service: " + getId()
            );
        }

        logger.info("SitewebPersistentService initialized with resource: {} ({})", resource.getId(), resourceType);
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.info("Starting SitewebPersistentService: {}", getId());

        try {
            // 获取关联的Resource
            Resource resource = getResource();

            // 获取DataSource
            DataSource dataSource = resource.getNativeResource();

            // 验证DataSource
            SitewebMyBatisConfig.validateDataSource(dataSource, getId());

            // 创建独立的SqlSessionFactory
            this.sqlSessionFactory = SitewebMyBatisConfig.createSqlSessionFactory(dataSource, getId());

            // 创建SqlSessionTemplate
            this.sqlSessionTemplate = SitewebMyBatisConfig.createSqlSessionTemplate(sqlSessionFactory, getId());

            logger.info("SitewebPersistentService started successfully: {}", getId());

        } catch (Exception e) {
            logger.error("Failed to start SitewebPersistentService: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_START_FAILED,
                "Failed to start SitewebPersistentService: " + getId() + ", error: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.info("Stopping SitewebPersistentService: {}", getId());
        // MyBatis资源会随着DataSource的关闭而自动清理
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.info("Destroying SitewebPersistentService: {}", getId());

        // 清理MyBatis资源
        this.sqlSessionTemplate = null;
        this.sqlSessionFactory = null;

        // 清理缓存
        this.serviceCache.clear();
        this.namedServiceCache.clear();

        // 清理Spring上下文引用
        this.applicationContext = null;

        logger.info("SitewebPersistentService destroyed: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            // 检查ApplicationContext是否可用
            if (applicationContext == null) {
                return HealthStatus.down("ApplicationContext not available");
            }

            // 检查关联的Resource
            Resource resource = getResource();
            if (resource == null) {
                return HealthStatus.down("Database resource not available");
            }

            // 检查Resource健康状态
            HealthStatus resourceHealth = resource.checkHealth();
            if (!resourceHealth.isUp()) {
                return HealthStatus.down("Database resource unhealthy: " + resourceHealth.getMessage());
            }

            // 检查SqlSessionFactory
            if (sqlSessionFactory == null) {
                return HealthStatus.down("SqlSessionFactory not initialized");
            }

            // 检查SqlSessionTemplate
            if (sqlSessionTemplate == null) {
                return HealthStatus.down("SqlSessionTemplate not initialized");
            }

            return HealthStatus.up("SitewebPersistentService is healthy");
        } catch (Exception e) {
            logger.error("Health check failed for SitewebPersistentService: {}", getId(), e);
            return HealthStatus.down("Health check failed: " + e.getMessage());
        }
    }
}
