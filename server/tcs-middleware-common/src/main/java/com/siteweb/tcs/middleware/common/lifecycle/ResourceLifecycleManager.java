package com.siteweb.tcs.middleware.common.lifecycle;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.resource.Resource;

/**
 * 资源生命周期管理器接口
 */
public interface ResourceLifecycleManager {

    /**
     * 创建资源实例
     *
     * @param resourceId 资源ID，对应数据库中的资源配置
     * @return 资源实例
     * @throws MiddlewareTechnicalException 创建资源实例失败时抛出异常
     */
    Resource createResource(String resourceId) throws MiddlewareTechnicalException;

    /**
     * 检查资源健康状态
     *
     * @param resource 资源实例
     * @return 健康状态
     */
    HealthStatus checkResourceHealth(Resource resource);

    /**
     * 销毁资源实例
     *
     * @param resource 资源实例
     * @throws MiddlewareTechnicalException 销毁资源实例失败时抛出异常
     */
    void destroyResource(Resource resource) throws MiddlewareTechnicalException;

    /**
     * 执行资源健康检查
     * 定期检查所有资源的健康状态
     */
    void performHealthCheck();
}
