package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import org.apache.pekko.actor.ActorSystem;
import org.apache.pekko.http.javadsl.Http;
import org.apache.pekko.http.javadsl.ServerBinding;
import org.apache.pekko.http.javadsl.model.HttpRequest;
import org.apache.pekko.http.javadsl.model.HttpResponse;
import org.apache.pekko.http.javadsl.server.Route;
import org.apache.pekko.stream.Materializer;
import org.apache.pekko.stream.javadsl.Flow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

/**
 * HTTP服务器资源
 * 基于Pekko Akka HTTP实现的HTTP服务器资源
 */
public class HttpServerResource extends BaseResource {

    private static final Logger logger = LoggerFactory.getLogger(HttpServerResource.class);

    // HTTP服务器绑定
    private ServerBinding serverBinding;

    // HTTP服务器配置
    private final String host;
    private final int port;
    private final Duration idleTimeout;
    private final int backlog;

    // Pekko Akka相关组件
    private final ActorSystem actorSystem;
    private final Materializer materializer;
    private final Http http;

    /**
     * 构造函数
     *
     * @param id 资源ID
     * @param type 资源类型
     * @param name 资源名称
     * @param description 资源描述
     * @param host 主机地址
     * @param port 端口
     * @param idleTimeout 空闲超时时间（秒）
     * @param backlog 连接队列大小
     */
    public HttpServerResource(String id, String type, String name, String description,
                             String host, int port, int idleTimeout, int backlog) {
        super(id, type, name, description);
        this.host = host;
        this.port = port;
        this.idleTimeout = Duration.ofSeconds(idleTimeout);
        this.backlog = backlog;

        // 从ClusterContext获取ActorSystem和相关组件
        this.actorSystem = ClusterContext.system();
        this.materializer = ClusterContext.getMaterializer();
        this.http = ClusterContext.getHttp();

        if (this.actorSystem == null) {
            throw new IllegalStateException("ActorSystem未初始化，请确保ClusterContext已正确配置");
        }
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化HTTP服务器资源: {}", getId());
        // 初始化阶段不需要特殊操作
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动HTTP服务器资源: {}", getId());
        try {
            // 创建默认路由（可以被后续的bindRoute方法覆盖）
            Route defaultRoute = createDefaultRoute();

            // 启动HTTP服务器
            CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
                    .bind(defaultRoute);

            // 等待服务器绑定完成
            serverBinding = bindingFuture.toCompletableFuture().get();

            InetSocketAddress address = serverBinding.localAddress();
            logger.info("HTTP服务器启动成功: {}:{} ({})", address.getHostString(), address.getPort(), getId());
        } catch (Exception e) {
            logger.error("启动HTTP服务器资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动HTTP服务器资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止HTTP服务器资源: {}", getId());
        try {
            if (serverBinding != null) {
                // 优雅地停止HTTP服务器
                serverBinding.terminate(Duration.ofSeconds(10))
                        .toCompletableFuture().get();
                serverBinding = null;
                logger.info("HTTP服务器停止成功: {}", getId());
            }
        } catch (Exception e) {
            logger.error("停止HTTP服务器资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止HTTP服务器资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁HTTP服务器资源: {}", getId());
        try {
            // 确保服务器已停止
            if (serverBinding != null) {
                serverBinding.terminate(Duration.ofSeconds(5))
                        .toCompletableFuture().get();
                serverBinding = null;
            }
            logger.info("HTTP服务器资源销毁成功: {}", getId());
        } catch (Exception e) {
            logger.error("销毁HTTP服务器资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                "销毁HTTP服务器资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        if (serverBinding == null) {
            return HealthStatus.down("HTTP服务器未启动");
        }

        try {
            InetSocketAddress address = serverBinding.localAddress();
            Map<String, Object> details = new HashMap<>();
            details.put("host", address.getHostString());
            details.put("port", address.getPort());
            details.put("idleTimeout", idleTimeout.getSeconds() + "s");
            details.put("backlog", backlog);

            return HealthStatus.up("HTTP服务器运行正常", details);
        } catch (Exception e) {
            logger.error("检查HTTP服务器健康状态失败: {}", getId(), e);
            return HealthStatus.down("检查健康状态失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeResource() {
        return (T) serverBinding;
    }

    /**
     * 绑定自定义路由
     *
     * @param route 自定义路由
     * @return 绑定完成的Future
     * @throws MiddlewareTechnicalException 如果绑定失败
     */
    public CompletableFuture<ServerBinding> bindRoute(Route route) throws MiddlewareTechnicalException {
        if (getStatus() != ResourceStatus.STARTED) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "HTTP服务器未启动，无法绑定路由"
            );
        }

        try {
            // 停止当前绑定
            if (serverBinding != null) {
                serverBinding.terminate(Duration.ofSeconds(5))
                        .toCompletableFuture().get();
            }

            // 创建新的绑定
            CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
                    .bind(route);

            // 更新服务器绑定
            serverBinding = bindingFuture.toCompletableFuture().get();

            InetSocketAddress address = serverBinding.localAddress();
            logger.info("HTTP服务器路由更新成功: {}:{} ({})", address.getHostString(), address.getPort(), getId());

            return CompletableFuture.completedFuture(serverBinding);
        } catch (Exception e) {
            logger.error("绑定HTTP服务器路由失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "绑定HTTP服务器路由失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 绑定自定义处理流
     *
     * @param handler 自定义处理流
     * @return 绑定完成的Future
     * @throws MiddlewareTechnicalException 如果绑定失败
     */
    public CompletableFuture<ServerBinding> bindHandler(Flow<HttpRequest, HttpResponse, ?> handler) throws MiddlewareTechnicalException {
        if (getStatus() != ResourceStatus.STARTED) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "HTTP服务器未启动，无法绑定处理器"
            );
        }

        try {
            // 停止当前绑定
            if (serverBinding != null) {
                serverBinding.terminate(Duration.ofSeconds(5))
                        .toCompletableFuture().get();
            }

            // 创建新的绑定
            CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
                    .bindFlow(handler);

            // 更新服务器绑定
            serverBinding = bindingFuture.toCompletableFuture().get();

            InetSocketAddress address = serverBinding.localAddress();
            logger.info("HTTP服务器处理器更新成功: {}:{} ({})", address.getHostString(), address.getPort(), getId());

            return CompletableFuture.completedFuture(serverBinding);
        } catch (Exception e) {
            logger.error("绑定HTTP服务器处理器失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "绑定HTTP服务器处理器失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 创建默认路由
     *
     * @return 默认路由
     */
    private Route createDefaultRoute() {
        return org.apache.pekko.http.javadsl.server.Directives.get(() ->
                org.apache.pekko.http.javadsl.server.Directives.path("", () ->
                        org.apache.pekko.http.javadsl.server.Directives.complete("HTTP Server is running")
                )
        );
    }

    /**
     * 获取服务器绑定
     *
     * @return 服务器绑定
     */
    public ServerBinding getServerBinding() {
        return serverBinding;
    }

    /**
     * 获取主机地址
     *
     * @return 主机地址
     */
    public String getHost() {
        return host;
    }

    /**
     * 获取端口
     *
     * @return 端口
     */
    public int getPort() {
        return port;
    }

    /**
     * 获取空闲超时时间
     *
     * @return 空闲超时时间
     */
    public Duration getIdleTimeout() {
        return idleTimeout;
    }

    /**
     * 获取连接队列大小
     *
     * @return 连接队列大小
     */
    public int getBacklog() {
        return backlog;
    }
}
