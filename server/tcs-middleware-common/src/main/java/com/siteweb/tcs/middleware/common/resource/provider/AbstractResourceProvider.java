package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceProvider;
import com.siteweb.tcs.middleware.common.util.ConfigConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 资源提供者抽象基类
 * 提供统一的配置转换逻辑和通用方法
 *
 * @param <T> 资源类型
 * @param <C> 配置类型
 */
public abstract class AbstractResourceProvider<T extends Resource, C> implements ResourceProvider<T> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 获取配置类的Class对象
     * 子类必须实现此方法
     *
     * @return 配置类的Class对象
     */
    protected abstract Class<C> getConfigClass();

    /**
     * 使用配置对象创建资源实例
     * 子类必须实现此方法
     *
     * @param id 资源ID
     * @param name 资源名称
     * @param description 资源描述
     * @param config 配置对象
     * @return 资源实例
     * @throws MiddlewareTechnicalException 创建失败时抛出
     */
    protected abstract T doCreateResource(String id, String name, String description, C config) 
            throws MiddlewareTechnicalException;

    /**
     * 验证配置对象
     * 子类可以重写此方法来实现特定的验证逻辑
     *
     * @param config 配置对象
     * @throws MiddlewareTechnicalException 验证失败时抛出
     */
    protected void validateConfigObject(C config) throws MiddlewareTechnicalException {
        if (config == null) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                String.format("配置对象不能为null: %s", getConfigClass().getSimpleName())
            );
        }
        logger.debug("配置对象验证通过: {}", getConfigClass().getSimpleName());
    }

    /**
     * 将Map转换为配置对象
     * 使用统一的ConfigConverter进行转换
     *
     * @param configMap 配置Map
     * @return 配置对象
     * @throws MiddlewareTechnicalException 转换失败时抛出
     */
    protected final C convertMapToConfig(Map<String, Object> configMap) throws MiddlewareTechnicalException {
        try {
            logger.debug("开始转换配置Map到{}: {}", getConfigClass().getSimpleName(), configMap);
            
            C config = ConfigConverter.convertMapToConfig(configMap, getConfigClass());
            
            // 验证转换后的配置对象
            validateConfigObject(config);
            
            logger.debug("配置转换和验证成功: {}", config);
            return config;
            
        } catch (MiddlewareTechnicalException e) {
            throw e;
        } catch (Exception e) {
            logger.error("配置转换失败: Map={}, TargetClass={}", configMap, getConfigClass().getSimpleName(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                String.format("配置转换失败: %s", e.getMessage()),
                e
            );
        }
    }

    /**
     * 创建资源实例的最终实现
     * 统一处理配置转换和资源创建流程
     */
    @Override
    public final T createResource(String id, String name, String description, Map<String, Object> config) 
            throws MiddlewareTechnicalException {
        
        logger.info("开始创建{}资源: id={}, name={}", getType(), id, name);
        
        try {
            // 1. 转换配置
            C configObject = convertMapToConfig(config);
            
            // 2. 创建资源
            T resource = doCreateResource(id, name, description, configObject);
            
            logger.info("{}资源创建成功: id={}, name={}", getType(), id, name);
            return resource;
            
        } catch (MiddlewareTechnicalException e) {
            logger.error("创建{}资源失败: id={}, name={}, 错误: {}", getType(), id, name, e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("创建{}资源失败: id={}, name={}", getType(), id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                String.format("创建%s资源失败: %s", getType(), e.getMessage()),
                e
            );
        }
    }

    /**
     * 销毁资源的默认实现
     * 子类可以重写此方法来实现特定的销毁逻辑
     */
    @Override
    public void destroyResource(Resource resource) throws MiddlewareTechnicalException {
        if (resource != null) {
            try {
                logger.info("开始销毁{}资源: {}", getType(), resource.getId());
                // 默认实现：调用资源的destroy方法
                resource.destroy();
                logger.info("{}资源销毁成功: {}", getType(), resource.getId());
            } catch (Exception e) {
                logger.error("销毁{}资源失败: {}", getType(), resource.getId(), e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTRUCTION_FAILED,
                    String.format("销毁%s资源失败: %s", getType(), e.getMessage()),
                    e
                );
            }
        }
    }

    /**
     * 获取配置对象的字符串表示
     * 用于日志记录，子类可以重写以隐藏敏感信息
     *
     * @param config 配置对象
     * @return 配置的字符串表示
     */
    protected String getConfigString(C config) {
        if (config == null) {
            return "null";
        }
        // 默认使用toString，子类可以重写来隐藏敏感信息
        return config.toString();
    }

    /**
     * 记录配置信息（隐藏敏感信息）
     *
     * @param config 配置对象
     */
    protected final void logConfig(C config) {
        logger.debug("使用配置: {}", getConfigString(config));
    }
}
