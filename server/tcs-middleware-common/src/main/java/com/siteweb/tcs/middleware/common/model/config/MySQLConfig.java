package com.siteweb.tcs.middleware.common.model.config;

import lombok.Data;

/**
 * MySQL数据库配置类
 */
@Data
public class MySQLConfig {
    /**
     * 主机地址
     */
    private String host = "localhost";

    /**
     * 端口
     */
    private int port = 3306;

    /**
     * 数据库名
     */
    private String database;

    /**
     * 用户名
     */
    private String username = "root";

    /**
     * 密码
     */
    private String password = "";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 30000;

    /**
     * 最小空闲连接数
     */
    private int minIdle = 5;

    /**
     * 最大连接池大小
     */
    private int maxPoolSize = 20;

    /**
     * 空闲超时时间（毫秒）
     */
    private long idleTimeout = 180000;

    /**
     * 连接最大生存时间（毫秒）
     */
    private long maxLifetime = 1800000;

    /**
     * 是否自动提交
     */
    private boolean autoCommit = true;

    /**
     * 连接测试查询
     */
    private String connectionTestQuery = "SELECT 1";

    /**
     * 是否使用SSL连接
     */
    private boolean useSSL = false;

    /**
     * 是否允许公钥检索
     */
    private boolean allowPublicKeyRetrieval = true;

    /**
     * 字符集
     */
    private String characterEncoding = "UTF-8";

    /**
     * 是否使用Unicode
     */
    private boolean useUnicode = true;

    /**
     * 是否允许多条查询
     */
    private boolean allowMultiQueries = false;

    /**
     * 服务器时区
     */
    private String serverTimezone = "Asia/Shanghai";
}
