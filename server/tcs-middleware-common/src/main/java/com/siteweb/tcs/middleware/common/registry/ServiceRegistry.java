package com.siteweb.tcs.middleware.common.registry;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.lifecycle.IServiceInitializer;
import com.siteweb.tcs.middleware.common.service.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 服务注册表
 * 用于管理服务实例
 */
@Component
public class ServiceRegistry {

    private static final Logger logger = LoggerFactory.getLogger(ServiceRegistry.class);

    private final ConcurrentMap<String, Service> services = new ConcurrentHashMap<>();

    /**
     * 服务引用计数详情
     * key: serviceId, value: Map<referenceId, 引用次数>
     */
    private final ConcurrentMap<String, ConcurrentMap<String, AtomicInteger>> serviceReferenceDetails = new ConcurrentHashMap<>();

    /**
     * 服务总引用计数（用于快速查询）
     * key: serviceId, value: 总引用计数
     */
    private final ConcurrentMap<String, AtomicInteger> serviceReferenceCounts = new ConcurrentHashMap<>();

    /**
     * 服务引用者集合（用于快速查询）
     * key: serviceId, value: 引用者ID集合
     */
    private final ConcurrentMap<String, Set<String>> serviceReferences = new ConcurrentHashMap<>();

    private IServiceInitializer serviceInitializer;
    private ApplicationContext applicationContext;
    private ResourceRegistry resourceRegistry;

    @Autowired
    public void setServiceInitializer(IServiceInitializer serviceInitializer) {
        this.serviceInitializer = serviceInitializer;
    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Autowired
    public void setResourceRegistry(ResourceRegistry resourceRegistry) {
        this.resourceRegistry = resourceRegistry;
    }

    /**
     * 保存服务到注册表
     *
     * @param service 服务实例
     * @throws MiddlewareBusinessException 如果保存失败
     */
    public void save(Service service) throws MiddlewareBusinessException {
        if (service == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Service cannot be null"
            );
        }
        services.put(service.getId(), service);
        logger.debug("Service saved to registry: {}", service.getId());
    }

    /**
     * 根据ID获取服务（不增加引用计数）
     * 如果服务不存在，可能会尝试创建
     *
     * ⚠️ 注意：此方法不会增加引用计数，仅供前端查看服务运行状况、是否实例化等场景使用
     * 在后端代码中使用中间件服务时，必须使用 get(serviceId, referenceId) 方法
     *
     * @param serviceId 服务ID
     * @return 服务实例，如果不存在则返回null
     */
    public Service get(String serviceId) {
        return get(serviceId, null);
    }

    /**
     * 根据ID获取服务（带引用者ID）
     * 如果服务不存在，可能会尝试创建
     *
     * @param serviceId 服务ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 服务实例，如果不存在则返回null
     */
    public Service get(String serviceId, String referenceId) {
        Service service = services.get(serviceId);

        // 如果服务不存在，尝试从服务初始化器获取
        if (service == null && serviceInitializer != null) {
            try {
                logger.debug("Service not found in registry, trying to initialize: {}", serviceId);
                service = serviceInitializer.initializeServiceById(serviceId);
            } catch (Exception e) {
                logger.warn("Failed to initialize service: {}", serviceId, e);
            }
        }

        // 添加引用计数
        if (service != null && referenceId != null) {
            addServiceReference(serviceId, referenceId);
            logger.debug("Added reference for service {}: {}", serviceId, referenceId);
        }

        return service;
    }

    /**
     * 获取所有服务
     *
     * @return 所有服务的集合
     */
    public Collection<Service> getAll() {
        return Collections.unmodifiableCollection(services.values());
    }

    /**
     * 根据类型获取服务
     *
     * @param type 服务类型
     * @return 指定类型的服务集合
     */
    public Collection<Service> getByType(String type) {
        return services.values().stream()
                .filter(service -> service.getType().equals(type))
                .collect(Collectors.toList());
    }

    /**
     * 从注册表中移除服务
     *
     * @param serviceId 服务ID
     * @return 被移除的服务，如果不存在则返回null
     */
    public Service remove(String serviceId) {
        logger.debug("Removing service from registry: {}", serviceId);
        return services.remove(serviceId);
    }

    /**
     * 检查服务是否存在于注册表中
     * 与get方法不同，此方法不会尝试创建不存在的服务
     *
     * @param serviceId 服务ID
     * @return 如果服务存在则返回true，否则返回false
     */
    public boolean contains(String serviceId) {
        return services.containsKey(serviceId);
    }

    /**
     * 添加服务引用
     * 当插件开始使用服务时调用
     *
     * @param serviceId 服务ID
     * @param referenceId 引用者ID（通常是插件ID）
     */
    public void addServiceReference(String serviceId, String referenceId) {
        // 更新详细引用计数
        int newReferenceCount = serviceReferenceDetails
            .computeIfAbsent(serviceId, k -> new ConcurrentHashMap<>())
            .computeIfAbsent(referenceId, k -> new AtomicInteger(0))
            .incrementAndGet();

        // 更新总引用计数
        serviceReferenceCounts.computeIfAbsent(serviceId, k -> new AtomicInteger(0)).incrementAndGet();

        // 更新引用者集合
        serviceReferences.computeIfAbsent(serviceId, k -> ConcurrentHashMap.newKeySet()).add(referenceId);

        logger.debug("Added reference for service {}: {} (reference count: {}, total references: {})",
                    serviceId, referenceId, newReferenceCount, serviceReferenceCounts.get(serviceId).get());
    }

    /**
     * 根据引用者ID获取所有引用的服务
     *
     * @param referenceId 引用者ID
     * @return 服务ID集合
     */
    public Set<String> getServicesByReferenceId(String referenceId) {
        Set<String> result = new HashSet<>();
        for (Map.Entry<String, Set<String>> entry : serviceReferences.entrySet()) {
            String serviceId = entry.getKey();
            Set<String> refIds = entry.getValue();
            if (refIds.contains(referenceId)) {
                result.add(serviceId);
            }
        }
        return result;
    }

    /**
     * 批量移除服务引用（移除该引用者的所有引用）
     * 当插件停止时，可以一次性移除该插件对所有服务的引用
     *
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 移除的服务数量
     */
    public int batchRemoveServiceReferences(String referenceId) {
        Set<String> serviceIds = getServicesByReferenceId(referenceId);
        int removedServiceCount = 0;

        for (String serviceId : serviceIds) {
            try {
                // 移除该引用者对该服务的所有引用
                boolean canDestroy = removeServiceReference(serviceId, referenceId, true);
                removedServiceCount++;
                if (canDestroy) {
                    logger.info("Service {} has no more references and was destroyed", serviceId);
                } else {
                    logger.debug("Service {} still has other references", serviceId);
                }
            } catch (Exception e) {
                logger.error("Failed to remove reference for service {}: {}", serviceId, e.getMessage(), e);
            }
        }

        logger.info("Batch removed references for {} services from reference: {}", removedServiceCount, referenceId);
        return removedServiceCount;
    }

    /**
     * 移除服务引用（单次）
     * 当插件停止使用服务时调用
     *
     * @param serviceId 服务ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 如果引用计数为0，返回true表示可以安全销毁服务
     */
    public boolean removeServiceReference(String serviceId, String referenceId) {
        return removeServiceReference(serviceId, referenceId, false);
    }

    /**
     * 移除服务引用
     *
     * @param serviceId 服务ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @param removeAll 是否移除该引用者的所有引用
     * @return 如果引用计数为0，返回true表示可以安全销毁服务
     */
    public boolean removeServiceReference(String serviceId, String referenceId, boolean removeAll) {
        ConcurrentMap<String, AtomicInteger> referenceDetails = serviceReferenceDetails.get(serviceId);
        if (referenceDetails == null) {
            logger.debug("No reference details found for service: {}", serviceId);
            return false;
        }

        AtomicInteger referenceCount = referenceDetails.get(referenceId);
        if (referenceCount == null) {
            logger.debug("No reference found for service {} from reference {}", serviceId, referenceId);
            return false;
        }

        int removedCount;
        if (removeAll) {
            // 移除该引用者的所有引用
            removedCount = referenceCount.getAndSet(0);
            referenceDetails.remove(referenceId);
        } else {
            // 只移除一次引用
            removedCount = 1;
            int newReferenceCount = referenceCount.decrementAndGet();
            if (newReferenceCount <= 0) {
                referenceDetails.remove(referenceId);
            }
        }

        // 更新总引用计数
        AtomicInteger totalCount = serviceReferenceCounts.get(serviceId);
        if (totalCount != null) {
            int newTotalCount = totalCount.addAndGet(-removedCount);
            logger.debug("Removed {} references for service {}: {} (remaining total references: {})",
                        removedCount, serviceId, referenceId, newTotalCount);

            if (newTotalCount <= 0) {
                // 清理所有引用数据
                serviceReferenceCounts.remove(serviceId);
                serviceReferenceDetails.remove(serviceId);
                serviceReferences.remove(serviceId);

                logger.info("Service {} has no more references, destroying service", serviceId);

                // 自动销毁没有引用的服务
                try {
                    Service service = services.get(serviceId);
                    if (service != null) {
                        service.stop();
                        logger.info("Destroying service with no references: {}", serviceId);
                        service.destroy();
                        services.remove(serviceId);

                        // 移除服务对资源的引用
                        if (resourceRegistry != null) {
                            resourceRegistry.batchRemoveResourceReferences(serviceId);
                        }

                        logger.info("Service destroyed successfully: {}", serviceId);
                    }
                } catch (Exception e) {
                    logger.error("Failed to destroy service {}: {}", serviceId, e.getMessage(), e);
                }

                return true;
            } else {
                // 如果该引用者没有更多引用，从引用者集合中移除
                if (!referenceDetails.containsKey(referenceId)) {
                    Set<String> references = serviceReferences.get(serviceId);
                    if (references != null) {
                        references.remove(referenceId);
                    }
                }
            }
        }
        return false;
    }

    /**
     * 插件生命周期管理：清理插件的所有引用
     * 先清理服务引用，再清理资源引用
     *
     * @param pluginId 插件ID
     */
    public void cleanupPluginReferences(String pluginId) {
        logger.info("Starting cleanup for plugin: {}", pluginId);

        // 1. 先清理对服务的所有引用
        int removedServices = batchRemoveServiceReferences(pluginId);
        logger.info("Cleaned up {} service references for plugin: {}", removedServices, pluginId);

        // 2. 再清理对资源的所有引用
        if (resourceRegistry != null) {
            int removedResources = resourceRegistry.batchRemoveResourceReferences(pluginId);
            logger.info("Cleaned up {} resource references for plugin: {}", removedResources, pluginId);
        }

        logger.info("Completed cleanup for plugin: {}", pluginId);
    }

    /**
     * 获取服务引用计数
     *
     * @param serviceId 服务ID
     * @return 引用计数
     */
    public int getServiceReferenceCount(String serviceId) {
        AtomicInteger count = serviceReferenceCounts.get(serviceId);
        return count != null ? count.get() : 0;
    }

    /**
     * 获取服务引用者列表
     *
     * @param serviceId 服务ID
     * @return 引用者ID集合
     */
    public Set<String> getServiceReferences(String serviceId) {
        Set<String> references = serviceReferences.get(serviceId);
        return references != null ? Collections.unmodifiableSet(references) : Collections.emptySet();
    }

}
