package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * MySQL数据库资源
 */
public class MySQLResource extends BaseResource {

    private static final Logger logger = LoggerFactory.getLogger(MySQLResource.class);

    private DataSource dataSource;

    /**
     * 构造函数
     *
     * @param id           资源ID
     * @param type         资源类型
     * @param name         资源名称
     * @param description  资源描述
     * @param dataSource   数据源
     */
    public MySQLResource(String id, String type, String name, String description, DataSource dataSource) {
        super(id, type, name, description);
        this.dataSource = dataSource;
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化MySQL资源: {}", getId());
        // 初始化阶段不创建数据源，只在启动时创建
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动MySQL资源: {}", getId());
        try {
            // 测试连接
            try (Connection connection = dataSource.getConnection()) {
                if (!connection.isValid(5)) {
                    throw new SQLException("连接无效");
                }
            }

            logger.info("MySQL资源启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动MySQL资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动MySQL资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止MySQL资源: {}", getId());
        try {
            // 对于HikariDataSource，需要关闭连接池
            if (dataSource instanceof AutoCloseable) {
                ((AutoCloseable) dataSource).close();
            }
            logger.info("MySQL资源停止成功: {}", getId());
        } catch (Exception e) {
            logger.error("停止MySQL资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止MySQL资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁MySQL资源: {}", getId());
        try {
            // 对于HikariDataSource，需要关闭连接池
            if (dataSource instanceof AutoCloseable) {
                ((AutoCloseable) dataSource).close();
            }
            dataSource = null;
            logger.info("MySQL资源销毁成功: {}", getId());
        } catch (Exception e) {
            logger.error("销毁MySQL资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                "销毁MySQL资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        if (dataSource == null) {
            return HealthStatus.down("数据源未初始化");
        }

        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                Map<String, Object> details = new HashMap<>();
                // 尝试获取连接池信息
                try {
                    // 反射获取HikariDataSource的连接池信息
                    Object poolMXBean = dataSource.getClass().getMethod("getHikariPoolMXBean").invoke(dataSource);
                    if (poolMXBean != null) {
                        details.put("activeConnections", poolMXBean.getClass().getMethod("getActiveConnections").invoke(poolMXBean));
                        details.put("idleConnections", poolMXBean.getClass().getMethod("getIdleConnections").invoke(poolMXBean));
                        details.put("totalConnections", poolMXBean.getClass().getMethod("getTotalConnections").invoke(poolMXBean));
                        details.put("threadsAwaitingConnection", poolMXBean.getClass().getMethod("getThreadsAwaitingConnection").invoke(poolMXBean));
                    }
                } catch (Exception e) {
                    // 忽略反射异常，这只是尝试获取额外信息
                    logger.debug("获取连接池信息失败", e);
                }

                return HealthStatus.up("连接正常", details);
            } else {
                return HealthStatus.down("连接无效");
            }
        } catch (SQLException e) {
            logger.error("检查MySQL资源健康状态失败: {}", getId(), e);
            return HealthStatus.down("连接失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeResource() {
        if (dataSource == null) {
            throw new IllegalStateException("MySQL资源未启动");
        }
        return (T) dataSource;
    }

    /**
     * 获取数据源
     *
     * @return 数据源
     */
    public DataSource getDataSource() {
        if (dataSource == null) {
            throw new IllegalStateException("MySQL资源未启动");
        }
        return dataSource;
    }
}