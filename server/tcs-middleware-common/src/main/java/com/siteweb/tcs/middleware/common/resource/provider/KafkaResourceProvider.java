package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.KafkaConfig;
import com.siteweb.tcs.middleware.common.resource.KafkaResource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Kafka资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class KafkaResourceProvider extends AbstractResourceProvider<KafkaResource, KafkaConfig> {

    @Override
    public String getType() {
        return ResourceType.KAFKA.getCode();
    }

    @Override
    protected Class<KafkaConfig> getConfigClass() {
        return KafkaConfig.class;
    }

    @Override
    protected void validateConfigObject(KafkaConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证引导服务器地址
        if (!StringUtils.hasText(config.getBootstrapServers())) {
            errors.add("引导服务器地址不能为空");
        }

        // 验证客户端ID
        if (!StringUtils.hasText(config.getClientId())) {
            errors.add("客户端ID不能为空");
        }

        // 验证序列化器
        if (!StringUtils.hasText(config.getKeySerializer())) {
            errors.add("键序列化器不能为空");
        }

        if (!StringUtils.hasText(config.getValueSerializer())) {
            errors.add("值序列化器不能为空");
        }

        // 验证反序列化器
        if (!StringUtils.hasText(config.getKeyDeserializer())) {
            errors.add("键反序列化器不能为空");
        }

        if (!StringUtils.hasText(config.getValueDeserializer())) {
            errors.add("值反序列化器不能为空");
        }

        // 验证数值参数
        if (config.getRetries() != null && config.getRetries() < 0) {
            errors.add("重试次数不能小于0");
        }

        if (config.getBatchSize() != null && config.getBatchSize() <= 0) {
            errors.add("批处理大小必须大于0");
        }

        if (config.getLingerMs() != null && config.getLingerMs() < 0) {
            errors.add("延迟时间不能小于0");
        }

        if (config.getBufferMemory() != null && config.getBufferMemory() <= 0) {
            errors.add("缓冲区大小必须大于0");
        }

        if (config.getSessionTimeoutMs() != null && config.getSessionTimeoutMs() <= 0) {
            errors.add("会话超时时间必须大于0");
        }

        if (config.getMaxPollRecords() != null && config.getMaxPollRecords() <= 0) {
            errors.add("最大拉取记录数必须大于0");
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_CONFIG_INVALID,
                "Kafka配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            KafkaConfig kafkaConfig = convertMapToConfig(config);
            validateConfigObject(kafkaConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证Kafka配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证Kafka配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            KafkaConfig kafkaConfig = convertMapToConfig(config);

            // 创建临时客户端进行连接测试
            Properties props = createKafkaProperties(kafkaConfig);

            try (AdminClient adminClient = AdminClient.create(props)) {
                // 获取主题列表，测试连接
                ListTopicsResult topicsResult = adminClient.listTopics();
                int topicCount = topicsResult.names().get(10, TimeUnit.SECONDS).size();

                Map<String, Object> details = new HashMap<>();
                details.put("bootstrapServers", kafkaConfig.getBootstrapServers());
                details.put("topicCount", topicCount);

                return ConnectionTestResult.success("连接成功", details);
            }
        } catch (Exception e) {
            logger.error("测试Kafka连接失败", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        }
    }

    @Override
    protected KafkaResource doCreateResource(String id, String name, String description, KafkaConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建Kafka资源: id={}, name={}, bootstrapServers={}", 
                id, name, config.getBootstrapServers());

            // 创建Kafka生产者工厂和模板
            Properties producerProperties = createKafkaProperties(config);
            ProducerFactory<String, String> producerFactory = createProducerFactory(config);
            KafkaTemplate<String, String> kafkaTemplate = createKafkaTemplate(producerFactory);

            // 创建Kafka资源实例
            KafkaResource resource = new KafkaResource(id, getType(), name, description, kafkaTemplate, producerFactory, producerProperties);
            
            logger.info("Kafka资源创建成功: id={}, name={}", id, name);
            return resource;
            
        } catch (Exception e) {
            logger.error("创建Kafka资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建Kafka资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(KafkaConfig config) {
        // 隐藏敏感信息
        return String.format("KafkaConfig{bootstrapServers='%s', clientId='%s', groupId='%s', saslPassword='***'}",
            config.getBootstrapServers(), config.getClientId(), config.getGroupId());
    }

    /**
     * 创建Kafka属性配置
     *
     * @param config Kafka配置
     * @return Kafka属性
     */
    protected Properties createKafkaProperties(KafkaConfig config) {
        Properties props = new Properties();
        
        // 基本配置
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getBootstrapServers());
        props.put(ProducerConfig.CLIENT_ID_CONFIG, config.getClientId());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, config.getKeySerializer());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, config.getValueSerializer());
        
        // 可选配置
        if (config.getAcks() != null) {
            props.put(ProducerConfig.ACKS_CONFIG, config.getAcks());
        }
        if (config.getRetries() != null) {
            props.put(ProducerConfig.RETRIES_CONFIG, config.getRetries());
        }
        if (config.getBatchSize() != null) {
            props.put(ProducerConfig.BATCH_SIZE_CONFIG, config.getBatchSize());
        }
        if (config.getLingerMs() != null) {
            props.put(ProducerConfig.LINGER_MS_CONFIG, config.getLingerMs());
        }
        if (config.getBufferMemory() != null) {
            props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, config.getBufferMemory());
        }

        // 安全配置
        if (StringUtils.hasText(config.getSecurityProtocol())) {
            props.put("security.protocol", config.getSecurityProtocol());
        }
        if (StringUtils.hasText(config.getSaslMechanism())) {
            props.put("sasl.mechanism", config.getSaslMechanism());
        }
        if (StringUtils.hasText(config.getSaslUsername()) && StringUtils.hasText(config.getSaslPassword())) {
            props.put("sasl.jaas.config", String.format(
                "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                config.getSaslUsername(), config.getSaslPassword()));
        }

        // 额外的生产者配置
        if (config.getAdditionalProducerConfig() != null) {
            props.putAll(config.getAdditionalProducerConfig());
        }

        return props;
    }

    /**
     * 创建生产者工厂
     *
     * @param config Kafka配置
     * @return 生产者工厂
     */
    protected ProducerFactory<String, String> createProducerFactory(KafkaConfig config) {
        Map<String, Object> configProps = new HashMap<>();
        Properties props = createKafkaProperties(config);
        props.forEach((key, value) -> configProps.put(key.toString(), value));
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * 创建KafkaTemplate
     *
     * @param producerFactory 生产者工厂
     * @return KafkaTemplate
     */
    protected KafkaTemplate<String, String> createKafkaTemplate(ProducerFactory<String, String> producerFactory) {
        return new KafkaTemplate<>(producerFactory);
    }
}
