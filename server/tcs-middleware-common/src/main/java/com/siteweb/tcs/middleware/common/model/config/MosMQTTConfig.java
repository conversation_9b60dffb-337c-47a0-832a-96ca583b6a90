package com.siteweb.tcs.middleware.common.model.config;

import lombok.Data;

/**
 * Mosquitto MQTT配置类
 */
@Data
public class MosMQTTConfig {
    /**
     * 服务器地址
     */
    private String serverUri = "tcp://localhost:1883";

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否清除会话
     */
    private boolean cleanSession = true;

    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 保持连接时间间隔（秒）
     */
    private int keepAliveInterval = 60;

    /**
     * 自动重连
     */
    private boolean automaticReconnect = true;

    /**
     * 最大重连延迟（毫秒）
     */
    private long maxReconnectDelay = 128000;

    /**
     * 最大消息缓存数量
     */
    private int maxInflight = 10;

    /**
     * 默认QoS级别
     */
    private int defaultQos = 1;

    /**
     * 默认是否保留消息
     */
    private boolean defaultRetained = false;

    /**
     * 遗嘱主题
     */
    private String willTopic;

    /**
     * 遗嘱消息
     */
    private String willMessage;

    /**
     * 遗嘱QoS级别
     */
    private int willQos = 1;

    /**
     * 遗嘱是否保留
     */
    private boolean willRetained = false;

    /**
     * SSL/TLS配置
     */
    private boolean useSsl = false;

    /**
     * 密钥库路径
     */
    private String keyStore;

    /**
     * 密钥库密码
     */
    private String keyStorePassword;

    /**
     * 信任库路径
     */
    private String trustStore;

    /**
     * 信任库密码
     */
    private String trustStorePassword;
}
