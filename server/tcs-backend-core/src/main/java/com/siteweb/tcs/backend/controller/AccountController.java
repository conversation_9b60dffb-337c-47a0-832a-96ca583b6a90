package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.dal.dto.AccountDTO;
import com.siteweb.tcs.hub.dal.dto.ResetPasswordDTO;
import com.siteweb.tcs.hub.dal.dto.UpdatePasswordDTO;
import com.siteweb.tcs.hub.service.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/account")
public class AccountController {

    @Autowired
    private AccountService accountService;

    /**
     * 创建新用户账号
     *
     * @param accountDTO 账号信息（包含用户名、登录ID、密码等）
     * @return 创建成功返回账号信息，失败返回错误信息
     * @throws RuntimeException 当登录ID重复或创建失败时抛出异常
     */
    @PostMapping(value = "/createaccount", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createAccount(@RequestBody AccountDTO accountDTO) {
        try {
            return ResponseHelper.successful(accountService.createAccount(accountDTO));
        } catch (IllegalArgumentException e) {
            log.error("Create account failed: Invalid parameters", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (IllegalStateException e) {
            log.error("Create account failed: Login ID repeat", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_REPEAT_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("Create account failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_CREATE_ERROR, e.getMessage());
        }
    }

    /**
     * 更新用户账号信息
     *
     * @param accountDTO 需要更新的账号信息
     * @return 更新成功返回更新后的账号信息，失败返回错误信息
     * @throws RuntimeException 当登录ID重复或更新失败时抛出异常
     */
    @PutMapping(value = "/updateaccount", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateAccount(@RequestBody AccountDTO accountDTO) {
        try {
            AccountDTO updatedAccount = accountService.updateAccount(accountDTO);
            if (updatedAccount == null) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "User not found with ID: " + accountDTO.getUserId());
            }
            return ResponseHelper.successful(updatedAccount);
        } catch (IllegalArgumentException e) {
            log.error("Update account failed: Invalid parameters", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (IllegalStateException e) {
            log.error("Update account failed: Login ID modification attempted", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("Update account failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }

    /**
     * 修改账号密码
     * 需要提供原密码进行验证
     *
     * @param updatePasswordDTO 密码更新信息（包含用户ID、原密码、新密码）
     * @return 更新成功返回true，失败返回错误信息
     */
    @PostMapping(value = "/editpassword", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> editPassword(@RequestBody UpdatePasswordDTO updatePasswordDTO) {
        try {
            boolean result = accountService.updatePassword(updatePasswordDTO);
            if (!result) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "User not found with ID: " + updatePasswordDTO.getUserId());
            }
            return ResponseHelper.successful(true);
        } catch (Exception e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }

    /**
     * 重置账号密码
     * 管理员操作，无需验证原密码
     *
     * @param resetPasswordDTO 密码重置信息（包含用户ID、新密码）
     * @return 重置成功返回true，失败返回错误信息
     */
    @PostMapping(value = "/resetpassword", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> resetPassword(@RequestBody ResetPasswordDTO resetPasswordDTO) {
        try {
            boolean result = accountService.resetPassword(resetPasswordDTO);
            if (!result) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "User not found with ID: " + resetPasswordDTO.getUserId());
            }
            return ResponseHelper.successful(true);
        } catch (IllegalArgumentException e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (Exception e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }

    /**
     * 删除用户账号
     *
     * @param userId 要删除的用户ID
     * @return 删除成功返回true，失败返回错误信息
     */
    @DeleteMapping(value = "/deleteaccount/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteAccount(@PathVariable Integer userId) {
        try {
            boolean result = accountService.removeById(userId);
            if (!result) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "User not found with ID: " + userId);
            }
            return ResponseHelper.successful(true);
        } catch (Exception e) {
            log.error("Delete account failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_DELETE_ERROR, e.getMessage());
        }
    }

    /**
     * 获取账号信息
     *
     * @param userId 用户ID
     * @return 返回指定用户的账号信息，用户不存在时返回错误信息
     */
    @GetMapping(value = "/accountinfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAccountInfo(@RequestParam Integer userId) {
        try {
            AccountDTO accountDTO = accountService.getById(userId);
            if (accountDTO == null) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "User not found with ID: " + userId);
            }
            return ResponseHelper.successful(accountDTO);
        } catch (IllegalArgumentException e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (Exception e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }

    /**
     * 获取所有用户账号列表
     *
     * @return 返回系统中所有用户的账号信息列表
     */
    @GetMapping(value = "/allaccounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAccounts() {
        try {
            return ResponseHelper.successful(accountService.getAllAccounts());
        } catch (Exception e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }

    /**
     * 启用或停用用户账号
     *
     * @param userId 用户ID
     * @param enable true表示启用，false表示停用
     * @return 操作成功返回true，失败返回错误信息
     */
    @PutMapping(value = "/eidtenable", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> editEnable(@RequestParam Integer userId, @RequestParam Boolean enable) {
        try {
            boolean result = accountService.updateEnable(userId, enable);
            if (!result) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "User not found with ID: " + userId);
            }
            return ResponseHelper.successful(true);
        } catch (IllegalArgumentException e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        } catch (Exception e) {
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_UPDATE_ERROR, e.getMessage());
        }
    }
}