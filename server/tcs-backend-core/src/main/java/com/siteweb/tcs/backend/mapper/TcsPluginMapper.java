package com.siteweb.tcs.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.backend.entity.TcsPlugin;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR> (2024-06-18)
 **/
public interface TcsPluginMapper extends BaseMapper<TcsPlugin> {
    @Update("UPDATE tcs_plugins SET enabled = 1 WHERE pluginId = #{pluginId}")
    int enablePlugin(@Param("pluginId") String pluginId);

    @Update("UPDATE tcs_plugins SET enabled = 0 WHERE pluginId = #{pluginId}")
    int disablePlugin(@Param("pluginId") String pluginId);

    @Select("SELECT enabled FROM tcs_plugins WHERE pluginId = #{pluginId}")
    Boolean isEnable(@Param("pluginId") String pluginId);
}
