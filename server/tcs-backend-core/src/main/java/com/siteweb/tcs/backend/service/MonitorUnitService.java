package com.siteweb.tcs.backend.service;

import com.siteweb.tcs.backend.dto.ForeignDeviceInfoDTO;
import com.siteweb.tcs.backend.dto.MonitorUnitInfoDTO;
import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.dto.ForeignDeviceDTO;
import com.siteweb.tcs.hub.dal.dto.ForeignGatewayDTO;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.HubConnectorDataHolder;
import com.siteweb.tcs.hub.domain.letter.*;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.process.LocalMonitorUnitStateCache;
import lombok.SneakyThrows;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletionStage;

import static com.siteweb.tcs.common.runtime.ActorUtil.askActorAsync;
import static com.siteweb.tcs.hub.domain.process.LocalEquipmentStateStore.queryEquipmentState;

@Component
public class MonitorUnitService {
    private final static String adapterName = "gatewayChangeAdapter";

    private final static String stateStoreName = "monitorUnitStateStore";


    @Autowired
    EquipmentRealSignalService equipmentRealSignalService;

    @Autowired
    EquipmentHistoryDataService equipmentHistoryDataService;

    @Autowired
    LocalMonitorUnitStateCache localMonitorUnitStateCache;

    @Autowired
    private HubConnectorDataHolder hubConnectorDataHolder;


    public MonitorUnitService() {

    }

    @SneakyThrows
    public List<MonitorUnitInfoDTO> getMonitorUnitList() {
        List<MonitorUnitInfoDTO> res = new ArrayList<>();
        //获取采集器状态
        List<LiveMonitorUnitState> liveMonitorUnitStates = localMonitorUnitStateCache.queryAllMonitorUnitState();
        //获取采集器和网关映射
        ActorRef gatewayLifeCycleManager = hubConnectorDataHolder.getHubEntry();
        CompletionStage<Object> responseFuture = askActorAsync(gatewayLifeCycleManager, "getAllConfig");
        Map<Integer, ForeignGatewayDTO> foreignGatewayMap = (Map<Integer, ForeignGatewayDTO>) responseFuture.toCompletableFuture().get();
        //根据映射合并一下
        for (LiveMonitorUnitState liveMonitorUnitState : liveMonitorUnitStates) {

            ForeignGatewayDTO foreignGateway = foreignGatewayMap.get(liveMonitorUnitState.getMonitorUnitID());
            if (foreignGateway != null) {
                var item = new MonitorUnitInfoDTO();
                item.setMonitorUnitID(liveMonitorUnitState.getMonitorUnitID());
                item.setForeignGatewayID(foreignGateway.getForeignGatewayID());
                item.setPluginId(foreignGateway.getPluginId());
                item.setTimeStamp(liveMonitorUnitState.getTimeStamp());
                item.setConnectState(liveMonitorUnitState.getConnectState());
                String adapterPath = ActorPathBuilder.create().withHubRoot()
                        .append("gatewayChangeGuard")
                        .append(adapterName)
                        .appendIds(foreignGateway.getForeignGatewayID())
                        .build();

                String stateStorePath = ActorPathBuilder.create()
                        .withHubRoot()
                        .append("gatewayChangeGuard")
                        .append(stateStoreName)
                        .appendIds(foreignGateway.getMonitorUnitID())
                        .build();

                String spoutPath = ActorPathBuilder.create()
                        .withHubRoot()
                        .append("gatewayChangeGuard")
                        .append("gatewayChangeSpout")
                        .build();
                item.setAdpaterPath(adapterPath);
                item.setStateStorePath(stateStorePath);
                item.setSpoutPath(spoutPath);
                List<? extends ForeignDevice> foreignDeviceList = foreignGateway.getForeignDeviceList();
                if (foreignDeviceList == null) {
                    foreignDeviceList = new ArrayList<>();
                }
                List<ForeignDeviceInfoDTO> foreignDeviceInfoDTOS = new ArrayList<>();
                for (ForeignDevice foreignDevice : foreignDeviceList) {
                    ForeignDeviceInfoDTO foreignDeviceInfoDTO = new ForeignDeviceInfoDTO(foreignDevice);
                    foreignDeviceInfoDTO.setState(null);
                    foreignDeviceInfoDTOS.add(foreignDeviceInfoDTO);
                }
                item.setForeignDeviceList(foreignDeviceInfoDTOS);
                res.add(item);
            }
        }

        return res;
    }

    @SneakyThrows
    public MonitorUnitInfoDTO getMonitorUnitByID(Integer monitorUnitID) {
        //获取采集器和网关映射
        ActorRef gatewayLifeCycleManager = hubConnectorDataHolder.getHubEntry();
        MonitorUnitQueryRequest monitorUnitQueryAction = new MonitorUnitQueryRequest();
        monitorUnitQueryAction.setMonitorUnitId(monitorUnitID);
        CompletionStage<Object> responseFuture = askActorAsync(gatewayLifeCycleManager, monitorUnitQueryAction);
        ForeignGatewayDTO foreignGateway = (ForeignGatewayDTO) responseFuture.toCompletableFuture().get();
        List<LiveMonitorUnitState> liveMonitorUnitStates = localMonitorUnitStateCache.queryAllMonitorUnitState();
        LiveMonitorUnitState monitorUnitIDFound = liveMonitorUnitStates.stream().filter(x -> x.getMonitorUnitID() == monitorUnitID).findFirst().orElseThrow(() -> new Exception("monitorUnitID not found"));
        //根据monitorUnitID找到对应的网关
        if (foreignGateway != null) {

            var item = new MonitorUnitInfoDTO();
            item.setMonitorUnitID(foreignGateway.getMonitorUnitID());
            item.setForeignGatewayID(foreignGateway.getForeignGatewayID());
            item.setPluginId(foreignGateway.getPluginId());
            //如果是start和restart状态，就是连接状态,stop就是断开状态
            LifeCycleEventType lifeCycleEventType = foreignGateway.getLifeCycleEventType();
            switch (lifeCycleEventType) {
                case START:
                case RESTART:
                    item.setConnectState(1);
                    break;
                case STOP:
                default:
                    item.setConnectState(0);
            }
            String adapterPath = ActorPathBuilder.create()
                    .withHubRoot()
                    .append("gatewayChangeGuard")
                    .append(adapterName)
                    .appendIds(foreignGateway.getForeignGatewayID())
                    .build();

            String stateStorePath = ActorPathBuilder.create()
                    .withHubRoot()
                    .append("gatewayChangeGuard")
                    .append(stateStoreName)
                    .appendIds(foreignGateway.getMonitorUnitID())
                    .build();

            String spoutPath = ActorPathBuilder.create()
                    .withHubRoot()
                    .append("gatewayChangeGuard")
                    .append("gatewayChangeSpout")
                    .build();
            item.setAdpaterPath(adapterPath);
            item.setStateStorePath(stateStorePath);
            item.setSpoutPath(spoutPath);
            item.setForeignDeviceList(setEquipmentListState((List<ForeignDeviceDTO>) foreignGateway.getForeignDeviceList()));
            return item;
        }
        return null;
    }

    @SneakyThrows
    public ForeignDeviceInfoDTO getForeignDeviceByID(Integer monitorUnitId, String equipmentId) {
        ActorRef gatewayLifeCycleManager = hubConnectorDataHolder.getHubEntry();
        MonitorUnitQueryRequest monitorUnitQueryAction = new MonitorUnitQueryRequest();
        monitorUnitQueryAction.setMonitorUnitId(monitorUnitId);
        CompletionStage<Object> responseFuture = askActorAsync(gatewayLifeCycleManager, monitorUnitQueryAction);
        ForeignGatewayDTO foreignGateway = (ForeignGatewayDTO) responseFuture.toCompletableFuture().get();
        if (foreignGateway != null) {
            List<ForeignDeviceDTO> foreignDeviceList = (List<ForeignDeviceDTO>) foreignGateway.getForeignDeviceList();
            ForeignDeviceDTO foreignDeviceDTO = foreignDeviceList.stream().filter(x -> x.getEquipmentId() == Integer.parseInt(equipmentId)).findFirst().orElseThrow(() -> new Exception("equipmentId not found"));
            return setEquipmentState(foreignDeviceDTO);
        }
        return null;
    }

    private List<ForeignDeviceInfoDTO> setEquipmentListState(List<ForeignDeviceDTO> equipments) {
        GetEquipmentStateAction getEquipmentStateAction = new GetEquipmentStateAction();
        getEquipmentStateAction.setGetAllData(false);
        getEquipmentStateAction.setEquipmentIds(equipments.stream().map(ForeignDeviceDTO::getEquipmentId).toList());
        List<LiveEquipmentState> liveEquipmentStates = queryEquipmentState(getEquipmentStateAction);
        if (equipments == null) return new ArrayList<>();
        var res = new ArrayList<ForeignDeviceInfoDTO>();
        equipments.forEach(equipment -> {
            if (equipment != null) {
                ForeignDeviceInfoDTO temp = new ForeignDeviceInfoDTO(equipment);
                LiveEquipmentState liveEquipmentState = liveEquipmentStates.stream().filter(x -> x.getEquipmentId() == equipment.getEquipmentId()).findFirst().orElse(null);
                if (liveEquipmentState != null) {
                    temp.setState(liveEquipmentState.getEquipmentState());
                }
                res.add(temp);
            }
        });
        return res;
    }

    private ForeignDeviceInfoDTO setEquipmentState(ForeignDeviceDTO equipments) {
        LiveEquipmentState liveEquipmentState = queryEquipmentState(equipments.getEquipmentId());
        ForeignDeviceInfoDTO temp = new ForeignDeviceInfoDTO(equipments);
        List<EquipmentSignalHisData> equipmentHistoryDataById = equipmentHistoryDataService.getEquipmentHistoryDataById(equipments.getEquipmentId());
        if (equipmentHistoryDataById != null && !equipmentHistoryDataById.isEmpty()){
            temp.setForeignHisDataList(equipmentHistoryDataById);
        }
        List<EquipmentRealSignal> realSignals = equipmentRealSignalService.getRealSignals(List.of(equipments.getEquipmentId()));
        List<RealSignal> finalRealSignals = Optional.ofNullable(realSignals)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0).getRealSignalList())
                .orElse(Collections.emptyList());
        temp.setRealSignalList(finalRealSignals);
        temp.setState(liveEquipmentState.getEquipmentState());
        return temp;
    }

    public Integer getMonitorUnitCount() {
        List<LiveMonitorUnitState> liveMonitorUnitStates = localMonitorUnitStateCache.queryAllMonitorUnitState();
        return liveMonitorUnitStates.size();
    }

    public void control(Integer monitorUnitId, LifeCycleEventType eventType) {
//        if (ObjectUtil.isEmpty(monitorUnitId)) return;
//        HubLifeCycleEvent lifeCycleEvent = new HubLifeCycleEvent();
//        lifeCycleEvent.setEventType(eventType);
//        lifeCycleEvent.setThingType(ThingType.GATEWAY);
//        lifeCycleEvent.setMonitorUnitId(monitorUnitId);
//        hubConnectorDataHolder.getGatewayLifeCycleManager().tell(lifeCycleEvent, ActorRef.noSender());
    }



}

