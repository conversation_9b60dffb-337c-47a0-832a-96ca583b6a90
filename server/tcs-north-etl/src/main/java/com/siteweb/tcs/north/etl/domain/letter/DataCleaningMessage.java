package com.siteweb.tcs.north.etl.domain.letter;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据处理消息
 * 用于在Actor之间传递需要处理的数据
 */
@Data
@NoArgsConstructor
@ToString
public class DataCleaningMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据源ID
     */
    private Integer sourceId;
    
    /**
     * 策略ID
     */
    private Integer strategyId;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 信号ID
     */
    private String signalId;
    
    /**
     * 时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 原始数据
     */
    private Map<String, Object> rawData = new HashMap<>();
    
    /**
     * 处理后的数据
     */
    private Map<String, Object> cleanedData = new HashMap<>();
    
    /**
     * 数据质量标记
     */
    private Map<String, String> qualityFlags = new HashMap<>();
    
    /**
     * 获取窗口日志字符串
     */
    public String getWindowLogString() {
        return String.format("DataCleaningMessage[sourceId=%d, strategyId=%d, dataType=%s, deviceId=%s, signalId=%s]",
                sourceId, strategyId, dataType, deviceId, signalId);
    }
}
