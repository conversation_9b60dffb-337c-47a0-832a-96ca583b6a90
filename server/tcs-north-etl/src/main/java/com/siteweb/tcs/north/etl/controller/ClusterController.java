package com.siteweb.tcs.north.etl.controller;

import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.north.etl.service.ClusterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 集群控制器
 */
@Slf4j
@RestController
@RequestMapping("/etl/cluster")
@Api(tags = "集群管理")
public class ClusterController {
    
    @Autowired
    private ClusterService clusterService;
    
    /**
     * 获取集群状态
     */
    @ApiOperation("获取集群状态")
    @GetMapping(value = "/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getClusterStatus() {
        Map<String, Object> status = clusterService.getClusterStatus();
        return ResponseEntity.ok(ResponseResult.success(status));
    }
}
