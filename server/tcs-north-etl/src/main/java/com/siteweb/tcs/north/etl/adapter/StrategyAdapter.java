package com.siteweb.tcs.north.etl.adapter;

import com.siteweb.tcs.north.etl.model.Reader;
import com.siteweb.tcs.north.etl.model.Strategy;
import com.siteweb.tcs.north.etl.model.Writer;

/**
 * 策略适配器接口
 * 负责将策略转换为NiFi流程
 */
public interface StrategyAdapter {
    
    /**
     * 检查是否支持该策略
     * 
     * @param reader 读取器
     * @param writer 存储器
     * @return 是否支持
     */
    boolean supports(Reader reader, Writer writer);
    
    /**
     * 创建NiFi流程
     * 
     * @param strategy 策略
     * @param reader 读取器
     * @param writer 存储器
     * @return NiFi流程组ID
     */
    String createNifiFlow(Strategy strategy, Reader reader, Writer writer);
    
    /**
     * 更新NiFi流程
     * 
     * @param processGroupId NiFi流程组ID
     * @param strategy 策略
     * @param reader 读取器
     * @param writer 存储器
     * @return 更新后的NiFi流程组ID
     */
    String updateNifiFlow(String processGroupId, Strategy strategy, Reader reader, Writer writer);
    
    /**
     * 删除NiFi流程
     * 
     * @param processGroupId NiFi流程组ID
     */
    void deleteNifiFlow(String processGroupId);
}
