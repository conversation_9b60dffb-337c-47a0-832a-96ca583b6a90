package com.siteweb.tcs.north.etl.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.north.etl.model.Strategy;
import com.siteweb.tcs.north.etl.model.Task;
import com.siteweb.tcs.north.etl.model.TaskExecution;
import com.siteweb.tcs.north.etl.nifi.service.NiFiService;
import com.siteweb.tcs.north.etl.repository.TaskExecutionRepository;
import com.siteweb.tcs.north.etl.repository.TaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务服务
 */
@Slf4j
@Service
public class TaskService {
    
    @Autowired
    private TaskRepository taskRepository;
    
    @Autowired
    private TaskExecutionRepository taskExecutionRepository;
    
    @Autowired
    private StrategyService strategyService;
    
    @Autowired
    private NiFiService nifiService;
    
    /**
     * 创建任务
     */
    @Transactional
    public Task createTask(Task task) {
        log.info("Creating task: {}", task.getName());
        
        // 验证策略
        Strategy strategy = strategyService.getStrategyById(task.getStrategyId());
        if (strategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + task.getStrategyId());
        }
        
        // 设置初始状态
        task.setStatus(Task.TaskStatus.STOPPED.name());
        task.setNifiTargetState(Task.NifiState.STOPPED.name());
        
        // 设置时间
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        
        // 保存任务
        taskRepository.insert(task);
        
        log.info("Created task with ID: {}", task.getId());
        
        return task;
    }
    
    /**
     * 更新任务
     */
    @Transactional
    public Task updateTask(Task task) {
        log.info("Updating task: {}", task.getId());
        
        // 验证策略
        Strategy strategy = strategyService.getStrategyById(task.getStrategyId());
        if (strategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + task.getStrategyId());
        }
        
        // 获取原任务
        Task oldTask = taskRepository.selectById(task.getId());
        if (oldTask == null) {
            throw new IllegalArgumentException("Task not found: " + task.getId());
        }
        
        // 设置时间
        task.setCreatedAt(oldTask.getCreatedAt());
        task.setUpdatedAt(LocalDateTime.now());
        
        // 更新任务
        taskRepository.updateById(task);
        
        log.info("Updated task: {}", task.getId());
        
        return task;
    }
    
    /**
     * 删除任务
     */
    @Transactional
    public void deleteTask(Integer id) {
        log.info("Deleting task: {}", id);
        
        // 获取任务
        Task task = taskRepository.selectById(id);
        if (task == null) {
            log.warn("Task not found: {}", id);
            return;
        }
        
        // 如果任务正在运行，先停止
        if (Task.TaskStatus.RUNNING.name().equals(task.getStatus())) {
            stopTask(id);
        }
        
        // 删除任务
        taskRepository.deleteById(id);
        
        log.info("Deleted task: {}", id);
    }
    
    /**
     * 启动任务
     */
    @Transactional
    public void startTask(Integer id) {
        log.info("Starting task: {}", id);
        
        // 获取任务
        Task task = taskRepository.selectById(id);
        if (task == null) {
            throw new IllegalArgumentException("Task not found: " + id);
        }
        
        // 获取策略
        Strategy strategy = strategyService.getStrategyById(task.getStrategyId());
        if (strategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + task.getStrategyId());
        }
        
        // 检查NiFi流程组ID
        String processGroupId = strategy.getNifiProcessGroupId();
        if (processGroupId == null || processGroupId.isEmpty()) {
            throw new IllegalStateException("NiFi process group ID not found for strategy: " + strategy.getId());
        }
        
        // 创建执行记录
        TaskExecution execution = new TaskExecution();
        execution.setTaskId(task.getId());
        execution.setStartTime(LocalDateTime.now());
        execution.setStatus(TaskExecution.ExecutionStatus.RUNNING.name());
        execution.setCreatedAt(LocalDateTime.now());
        taskExecutionRepository.insert(execution);
        
        try {
            // 启动NiFi流程组
            nifiService.startProcessGroup(processGroupId);
            
            // 更新任务状态
            task.setStatus(Task.TaskStatus.RUNNING.name());
            task.setNifiTargetState(Task.NifiState.RUNNING.name());
            task.setLastRunTime(LocalDateTime.now());
            task.setUpdatedAt(LocalDateTime.now());
            taskRepository.updateById(task);
            
            log.info("Started task: {}", id);
        } catch (Exception e) {
            log.error("Error starting task: {}", id, e);
            
            // 更新执行记录
            execution.setEndTime(LocalDateTime.now());
            execution.setStatus(TaskExecution.ExecutionStatus.FAILURE.name());
            execution.setLogMessage("Error starting task: " + e.getMessage());
            taskExecutionRepository.updateById(execution);
            
            // 更新任务状态
            task.setStatus(Task.TaskStatus.ERROR.name());
            task.setUpdatedAt(LocalDateTime.now());
            taskRepository.updateById(task);
            
            throw new RuntimeException("Failed to start task", e);
        }
    }
    
    /**
     * 停止任务
     */
    @Transactional
    public void stopTask(Integer id) {
        log.info("Stopping task: {}", id);
        
        // 获取任务
        Task task = taskRepository.selectById(id);
        if (task == null) {
            throw new IllegalArgumentException("Task not found: " + id);
        }
        
        // 获取策略
        Strategy strategy = strategyService.getStrategyById(task.getStrategyId());
        if (strategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + task.getStrategyId());
        }
        
        // 检查NiFi流程组ID
        String processGroupId = strategy.getNifiProcessGroupId();
        if (processGroupId == null || processGroupId.isEmpty()) {
            throw new IllegalStateException("NiFi process group ID not found for strategy: " + strategy.getId());
        }
        
        // 获取正在运行的执行记录
        List<TaskExecution> runningExecutions = taskExecutionRepository.findRunningExecutions();
        TaskExecution execution = null;
        for (TaskExecution e : runningExecutions) {
            if (e.getTaskId().equals(task.getId())) {
                execution = e;
                break;
            }
        }
        
        try {
            // 停止NiFi流程组
            nifiService.stopProcessGroup(processGroupId);
            
            // 更新任务状态
            task.setStatus(Task.TaskStatus.STOPPED.name());
            task.setNifiTargetState(Task.NifiState.STOPPED.name());
            task.setUpdatedAt(LocalDateTime.now());
            taskRepository.updateById(task);
            
            // 更新执行记录
            if (execution != null) {
                execution.setEndTime(LocalDateTime.now());
                execution.setStatus(TaskExecution.ExecutionStatus.CANCELLED.name());
                taskExecutionRepository.updateById(execution);
            }
            
            log.info("Stopped task: {}", id);
        } catch (Exception e) {
            log.error("Error stopping task: {}", id, e);
            
            // 更新任务状态
            task.setStatus(Task.TaskStatus.ERROR.name());
            task.setUpdatedAt(LocalDateTime.now());
            taskRepository.updateById(task);
            
            throw new RuntimeException("Failed to stop task", e);
        }
    }
    
    /**
     * 获取任务状态
     */
    public Map<String, Object> getTaskStatus(Integer id) {
        log.info("Getting status for task: {}", id);
        
        // 获取任务
        Task task = taskRepository.selectById(id);
        if (task == null) {
            throw new IllegalArgumentException("Task not found: " + id);
        }
        
        // 获取策略
        Strategy strategy = strategyService.getStrategyById(task.getStrategyId());
        if (strategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + task.getStrategyId());
        }
        
        // 检查NiFi流程组ID
        String processGroupId = strategy.getNifiProcessGroupId();
        if (processGroupId == null || processGroupId.isEmpty()) {
            throw new IllegalStateException("NiFi process group ID not found for strategy: " + strategy.getId());
        }
        
        try {
            // 获取NiFi流程组状态
            Map<String, Object> status = nifiService.getProcessGroupStatus(processGroupId);
            
            // 添加任务信息
            status.put("taskId", task.getId());
            status.put("taskName", task.getName());
            status.put("taskStatus", task.getStatus());
            status.put("lastRunTime", task.getLastRunTime());
            status.put("nextRunTime", task.getNextRunTime());
            
            return status;
        } catch (Exception e) {
            log.error("Error getting status for task: {}", id, e);
            throw new RuntimeException("Failed to get task status", e);
        }
    }
    
    /**
     * 获取任务日志
     */
    public List<Map<String, Object>> getTaskLogs(Integer id) {
        log.info("Getting logs for task: {}", id);
        
        // 获取任务
        Task task = taskRepository.selectById(id);
        if (task == null) {
            throw new IllegalArgumentException("Task not found: " + id);
        }
        
        // 获取策略
        Strategy strategy = strategyService.getStrategyById(task.getStrategyId());
        if (strategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + task.getStrategyId());
        }
        
        // 检查NiFi流程组ID
        String processGroupId = strategy.getNifiProcessGroupId();
        if (processGroupId == null || processGroupId.isEmpty()) {
            throw new IllegalStateException("NiFi process group ID not found for strategy: " + strategy.getId());
        }
        
        try {
            // 获取NiFi公告
            return nifiService.getBulletins(processGroupId);
        } catch (Exception e) {
            log.error("Error getting logs for task: {}", id, e);
            throw new RuntimeException("Failed to get task logs", e);
        }
    }
    
    /**
     * 根据ID查询任务
     */
    public Task getTaskById(Integer id) {
        return taskRepository.selectById(id);
    }
    
    /**
     * 查询所有任务
     */
    public List<Task> getAllTasks() {
        return taskRepository.selectList(null);
    }
    
    /**
     * 分页查询任务
     */
    public Page<Task> getTasksByPage(int pageNum, int pageSize) {
        Page<Task> page = new Page<>(pageNum, pageSize);
        return taskRepository.selectPage(page, null);
    }
    
    /**
     * 根据策略ID查询任务
     */
    public List<Task> getTasksByStrategyId(Integer strategyId) {
        return taskRepository.findByStrategyId(strategyId);
    }
    
    /**
     * 根据状态查询任务
     */
    public List<Task> getTasksByStatus(String status) {
        return taskRepository.findByStatus(status);
    }
    
    /**
     * 查询任务执行记录
     */
    public List<TaskExecution> getTaskExecutions(Integer taskId) {
        return taskExecutionRepository.findByTaskId(taskId);
    }
    
    /**
     * 定时调度任务
     */
    @Scheduled(fixedDelay = 60000) // 每分钟执行一次
    public void scheduleTasks() {
        log.debug("Scheduling tasks...");
        
        try {
            // 获取所有需要调度的任务
            List<Task> tasks = taskRepository.findScheduledTasks();
            
            for (Task task : tasks) {
                // 检查是否需要执行
                if (task.getNextRunTime() != null && task.getNextRunTime().isBefore(LocalDateTime.now())) {
                    // 如果任务已经停止，启动任务
                    if (Task.TaskStatus.STOPPED.name().equals(task.getStatus())) {
                        try {
                            startTask(task.getId());
                        } catch (Exception e) {
                            log.error("Error starting scheduled task: {}", task.getId(), e);
                        }
                    }
                    
                    // 计算下次执行时间
                    // TODO: 实现Cron表达式解析
                    // 这里简单地设置为1小时后
                    task.setNextRunTime(LocalDateTime.now().plusHours(1));
                    task.setUpdatedAt(LocalDateTime.now());
                    taskRepository.updateById(task);
                }
            }
        } catch (Exception e) {
            log.error("Error scheduling tasks", e);
        }
    }
}
