package com.siteweb.tcs.north.etl.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.north.etl.adapter.StrategyAdapter;
import com.siteweb.tcs.north.etl.model.Reader;
import com.siteweb.tcs.north.etl.model.Strategy;
import com.siteweb.tcs.north.etl.model.Writer;
import com.siteweb.tcs.north.etl.repository.StrategyRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 策略服务
 */
@Slf4j
@Service
public class StrategyService {
    
    @Autowired
    private StrategyRepository strategyRepository;
    
    @Autowired
    private ReaderService readerService;
    
    @Autowired
    private WriterService writerService;
    
    @Autowired
    private List<StrategyAdapter> strategyAdapters;
    
    /**
     * 创建策略
     */
    @Transactional
    public Strategy createStrategy(Strategy strategy) {
        log.info("Creating strategy: {}", strategy.getName());
        
        // 验证读取器和存储器
        Reader reader = readerService.getReaderById(strategy.getReaderId());
        if (reader == null) {
            throw new IllegalArgumentException("Reader not found: " + strategy.getReaderId());
        }
        
        Writer writer = writerService.getWriterById(strategy.getWriterId());
        if (writer == null) {
            throw new IllegalArgumentException("Writer not found: " + strategy.getWriterId());
        }
        
        // 查找适配器
        StrategyAdapter adapter = findAdapter(reader, writer);
        if (adapter == null) {
            throw new IllegalArgumentException("No adapter found for reader type: " + reader.getType() + 
                    " and writer type: " + writer.getType());
        }
        
        // 设置时间
        strategy.setCreatedAt(LocalDateTime.now());
        strategy.setUpdatedAt(LocalDateTime.now());
        
        // 保存策略
        strategyRepository.insert(strategy);
        
        // 创建NiFi流程
        String processGroupId = adapter.createNifiFlow(strategy, reader, writer);
        strategy.setNifiProcessGroupId(processGroupId);
        
        // 更新策略
        strategyRepository.updateById(strategy);
        
        log.info("Created strategy with ID: {} and NiFi process group ID: {}", 
                strategy.getId(), strategy.getNifiProcessGroupId());
        
        return strategy;
    }
    
    /**
     * 更新策略
     */
    @Transactional
    public Strategy updateStrategy(Strategy strategy) {
        log.info("Updating strategy: {}", strategy.getId());
        
        // 验证读取器和存储器
        Reader reader = readerService.getReaderById(strategy.getReaderId());
        if (reader == null) {
            throw new IllegalArgumentException("Reader not found: " + strategy.getReaderId());
        }
        
        Writer writer = writerService.getWriterById(strategy.getWriterId());
        if (writer == null) {
            throw new IllegalArgumentException("Writer not found: " + strategy.getWriterId());
        }
        
        // 查找适配器
        StrategyAdapter adapter = findAdapter(reader, writer);
        if (adapter == null) {
            throw new IllegalArgumentException("No adapter found for reader type: " + reader.getType() + 
                    " and writer type: " + writer.getType());
        }
        
        // 获取原策略
        Strategy oldStrategy = strategyRepository.selectById(strategy.getId());
        if (oldStrategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + strategy.getId());
        }
        
        // 设置时间
        strategy.setCreatedAt(oldStrategy.getCreatedAt());
        strategy.setUpdatedAt(LocalDateTime.now());
        
        // 更新NiFi流程
        String processGroupId = oldStrategy.getNifiProcessGroupId();
        if (processGroupId != null) {
            processGroupId = adapter.updateNifiFlow(processGroupId, strategy, reader, writer);
        } else {
            processGroupId = adapter.createNifiFlow(strategy, reader, writer);
        }
        strategy.setNifiProcessGroupId(processGroupId);
        
        // 更新策略
        strategyRepository.updateById(strategy);
        
        log.info("Updated strategy: {} with NiFi process group ID: {}", 
                strategy.getId(), strategy.getNifiProcessGroupId());
        
        return strategy;
    }
    
    /**
     * 删除策略
     */
    @Transactional
    public void deleteStrategy(Integer id) {
        log.info("Deleting strategy: {}", id);
        
        // 获取策略
        Strategy strategy = strategyRepository.selectById(id);
        if (strategy == null) {
            log.warn("Strategy not found: {}", id);
            return;
        }
        
        // 删除NiFi流程
        String processGroupId = strategy.getNifiProcessGroupId();
        if (processGroupId != null) {
            try {
                // 查找适配器
                Reader reader = readerService.getReaderById(strategy.getReaderId());
                Writer writer = writerService.getWriterById(strategy.getWriterId());
                
                if (reader != null && writer != null) {
                    StrategyAdapter adapter = findAdapter(reader, writer);
                    if (adapter != null) {
                        adapter.deleteNifiFlow(processGroupId);
                    }
                }
            } catch (Exception e) {
                log.error("Error deleting NiFi flow for strategy: {}", id, e);
            }
        }
        
        // 删除策略
        strategyRepository.deleteById(id);
        
        log.info("Deleted strategy: {}", id);
    }
    
    /**
     * 根据ID查询策略
     */
    public Strategy getStrategyById(Integer id) {
        return strategyRepository.selectById(id);
    }
    
    /**
     * 查询所有策略
     */
    public List<Strategy> getAllStrategies() {
        return strategyRepository.selectList(null);
    }
    
    /**
     * 分页查询策略
     */
    public Page<Strategy> getStrategiesByPage(int pageNum, int pageSize) {
        Page<Strategy> page = new Page<>(pageNum, pageSize);
        return strategyRepository.selectPage(page, null);
    }
    
    /**
     * 根据读取器ID查询策略
     */
    public List<Strategy> getStrategiesByReaderId(Integer readerId) {
        return strategyRepository.findByReaderId(readerId);
    }
    
    /**
     * 根据存储器ID查询策略
     */
    public List<Strategy> getStrategiesByWriterId(Integer writerId) {
        return strategyRepository.findByWriterId(writerId);
    }
    
    /**
     * 查找适配器
     */
    private StrategyAdapter findAdapter(Reader reader, Writer writer) {
        for (StrategyAdapter adapter : strategyAdapters) {
            if (adapter.supports(reader, writer)) {
                return adapter;
            }
        }
        return null;
    }
}
