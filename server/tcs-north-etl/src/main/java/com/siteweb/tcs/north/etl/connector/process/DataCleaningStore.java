package com.siteweb.tcs.north.etl.connector.process;

import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.north.etl.domain.letter.DataCleaningMessage;
import lombok.extern.slf4j.Slf4j;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 数据处理存储
 * 负责存储和管理处理后的数据
 */
@Slf4j
public class DataCleaningStore extends AbstractActor {

    private final ActorProbe probe;
    private final ActorRef dataCleaningSpout;
    
    /**
     * 构造函数
     */
    public DataCleaningStore(ActorRef dataCleaningSpout) {
        this.dataCleaningSpout = dataCleaningSpout;
        this.probe = createProbe(this);
        probe.addCounter("StoredDataCounter");
    }
    
    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DataCleaningMessage.class, this::handleDataCleaning)
                .matchAny(this::unhandled)
                .build();
    }
    
    /**
     * 处理数据处理消息
     */
    private void handleDataCleaning(DataCleaningMessage message) {
        probe.info("Storing data: " + message.getWindowLogString());
        
        try {
            // 存储数据
            storeData(message);
            
            // 转发到数据发送器
            dataCleaningSpout.tell(message, getSelf());
            
            // 更新计数器
            probe.incrementCounterAmount("StoredDataCounter", 1);
        } catch (Exception e) {
            probe.error("Error storing data: " + e.getMessage());
        }
    }
    
    /**
     * 存储数据
     */
    private void storeData(DataCleaningMessage message) {
        // 实现数据存储逻辑
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
    
    /**
     * 创建Props
     */
    public static Props props(ActorRef dataCleaningSpout) {
        return Props.create(DataCleaningStore.class, dataCleaningSpout);
    }
}
