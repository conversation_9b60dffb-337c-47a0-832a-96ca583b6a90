package org.apache.nifi.web.api.dto;

import java.util.Collection;
import java.util.Date;
import java.util.Map;

/**
 * Stub implementation of ProcessGroupStatusDTO for compilation purposes.
 * This is a simplified version of the original class from Apache NiFi.
 */
public class ProcessGroupStatusDTO {
    private String id;
    private String name;
    private Date statsLastRefreshed;
    private String inputCount;
    private String inputBytes;
    private String outputCount;
    private String outputBytes;
    private String queuedCount;
    private String queuedBytes;
    private String bytesRead;
    private String bytesWritten;
    private String flowFilesReceived;
    private String bytesReceived;
    private String flowFilesSent;
    private String bytesSent;
    private String flowFilesTransferred;
    private String bytesTransferred;
    private String activeThreadCount;
    private String terminatedThreadCount;
    private Collection<ProcessorStatusDTO> processorStatus;
    private Collection<ConnectionStatusDTO> connectionStatus;
    private Collection<ProcessGroupStatusDTO> processGroupStatus;
    private Collection<RemoteProcessGroupStatusDTO> remoteProcessGroupStatus;
    private Collection<PortStatusDTO> inputPortStatus;
    private Collection<PortStatusDTO> outputPortStatus;
    private Map<String, String> versionedFlowStatus;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getStatsLastRefreshed() {
        return statsLastRefreshed;
    }

    public void setStatsLastRefreshed(Date statsLastRefreshed) {
        this.statsLastRefreshed = statsLastRefreshed;
    }

    public String getInputCount() {
        return inputCount;
    }

    public void setInputCount(String inputCount) {
        this.inputCount = inputCount;
    }

    public String getInputBytes() {
        return inputBytes;
    }

    public void setInputBytes(String inputBytes) {
        this.inputBytes = inputBytes;
    }

    public String getOutputCount() {
        return outputCount;
    }

    public void setOutputCount(String outputCount) {
        this.outputCount = outputCount;
    }

    public String getOutputBytes() {
        return outputBytes;
    }

    public void setOutputBytes(String outputBytes) {
        this.outputBytes = outputBytes;
    }

    public String getQueuedCount() {
        return queuedCount;
    }

    public void setQueuedCount(String queuedCount) {
        this.queuedCount = queuedCount;
    }

    public String getQueuedBytes() {
        return queuedBytes;
    }

    public void setQueuedBytes(String queuedBytes) {
        this.queuedBytes = queuedBytes;
    }

    public String getBytesRead() {
        return bytesRead;
    }

    public void setBytesRead(String bytesRead) {
        this.bytesRead = bytesRead;
    }

    public String getBytesWritten() {
        return bytesWritten;
    }

    public void setBytesWritten(String bytesWritten) {
        this.bytesWritten = bytesWritten;
    }

    public String getFlowFilesReceived() {
        return flowFilesReceived;
    }

    public void setFlowFilesReceived(String flowFilesReceived) {
        this.flowFilesReceived = flowFilesReceived;
    }

    public String getBytesReceived() {
        return bytesReceived;
    }

    public void setBytesReceived(String bytesReceived) {
        this.bytesReceived = bytesReceived;
    }

    public String getFlowFilesSent() {
        return flowFilesSent;
    }

    public void setFlowFilesSent(String flowFilesSent) {
        this.flowFilesSent = flowFilesSent;
    }

    public String getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(String bytesSent) {
        this.bytesSent = bytesSent;
    }

    public String getFlowFilesTransferred() {
        return flowFilesTransferred;
    }

    public void setFlowFilesTransferred(String flowFilesTransferred) {
        this.flowFilesTransferred = flowFilesTransferred;
    }

    public String getBytesTransferred() {
        return bytesTransferred;
    }

    public void setBytesTransferred(String bytesTransferred) {
        this.bytesTransferred = bytesTransferred;
    }

    public String getActiveThreadCount() {
        return activeThreadCount;
    }

    public void setActiveThreadCount(String activeThreadCount) {
        this.activeThreadCount = activeThreadCount;
    }

    public String getTerminatedThreadCount() {
        return terminatedThreadCount;
    }

    public void setTerminatedThreadCount(String terminatedThreadCount) {
        this.terminatedThreadCount = terminatedThreadCount;
    }

    public Collection<ProcessorStatusDTO> getProcessorStatus() {
        return processorStatus;
    }

    public void setProcessorStatus(Collection<ProcessorStatusDTO> processorStatus) {
        this.processorStatus = processorStatus;
    }

    public Collection<ConnectionStatusDTO> getConnectionStatus() {
        return connectionStatus;
    }

    public void setConnectionStatus(Collection<ConnectionStatusDTO> connectionStatus) {
        this.connectionStatus = connectionStatus;
    }

    public Collection<ProcessGroupStatusDTO> getProcessGroupStatus() {
        return processGroupStatus;
    }

    public void setProcessGroupStatus(Collection<ProcessGroupStatusDTO> processGroupStatus) {
        this.processGroupStatus = processGroupStatus;
    }

    public Collection<RemoteProcessGroupStatusDTO> getRemoteProcessGroupStatus() {
        return remoteProcessGroupStatus;
    }

    public void setRemoteProcessGroupStatus(Collection<RemoteProcessGroupStatusDTO> remoteProcessGroupStatus) {
        this.remoteProcessGroupStatus = remoteProcessGroupStatus;
    }

    public Collection<PortStatusDTO> getInputPortStatus() {
        return inputPortStatus;
    }

    public void setInputPortStatus(Collection<PortStatusDTO> inputPortStatus) {
        this.inputPortStatus = inputPortStatus;
    }

    public Collection<PortStatusDTO> getOutputPortStatus() {
        return outputPortStatus;
    }

    public void setOutputPortStatus(Collection<PortStatusDTO> outputPortStatus) {
        this.outputPortStatus = outputPortStatus;
    }

    public Map<String, String> getVersionedFlowStatus() {
        return versionedFlowStatus;
    }

    public void setVersionedFlowStatus(Map<String, String> versionedFlowStatus) {
        this.versionedFlowStatus = versionedFlowStatus;
    }
}
