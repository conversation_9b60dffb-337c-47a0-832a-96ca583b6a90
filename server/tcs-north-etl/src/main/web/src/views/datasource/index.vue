<template>
  <div class="datasource-container">
    <div class="page-header">
      <h2>{{ $t('ai-cleaner.datasource.list') }}</h2>
      <el-button type="primary" @click="handleCreateDataSource">
        <el-icon><plus /></el-icon>
        {{ $t('ai-cleaner.datasource.create') }}
      </el-button>
    </div>
    
    <!-- 数据源列表 -->
    <el-card class="datasource-list" shadow="never">
      <el-table :data="dataSources" v-loading="loading" border style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" :label="$t('ai-cleaner.datasource.name')" min-width="150" />
        <el-table-column prop="type" :label="$t('ai-cleaner.datasource.type')" width="120">
          <template #default="scope">
            {{ $t(`ai-cleaner.datasource.types.${scope.row.type}`) }}
          </template>
        </el-table-column>
        <el-table-column prop="dataFormat" :label="$t('ai-cleaner.datasource.dataFormat')" width="120" />
        <el-table-column prop="status" :label="$t('ai-cleaner.datasource.status')" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'ACTIVE' ? 'success' : scope.row.status === 'ERROR' ? 'danger' : 'info'">
              {{ $t(`ai-cleaner.datasource.status.${scope.row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ai-cleaner.common.actions')" width="300">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'INACTIVE'" 
              type="success" 
              size="small" 
              @click="handleActivateDataSource(scope.row)"
            >
              {{ $t('ai-cleaner.datasource.actions.activate') }}
            </el-button>
            <el-button 
              v-if="scope.row.status === 'ACTIVE'" 
              type="warning" 
              size="small" 
              @click="handleDeactivateDataSource(scope.row)"
            >
              {{ $t('ai-cleaner.datasource.actions.deactivate') }}
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              @click="handleTestConnection(scope.row)"
            >
              {{ $t('ai-cleaner.datasource.actions.test') }}
            </el-button>
            <el-button type="primary" size="small" @click="handleEditDataSource(scope.row)">
              {{ $t('ai-cleaner.datasource.actions.edit') }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDeleteDataSource(scope.row)">
              {{ $t('ai-cleaner.datasource.actions.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 数据源表单对话框 -->
    <el-dialog 
      :title="dialogType === 'create' ? $t('ai-cleaner.datasource.create') : $t('ai-cleaner.datasource.edit')" 
      v-model="dialogVisible"
      width="60%"
    >
      <el-form :model="dataSourceForm" label-width="120px" :rules="rules" ref="dataSourceFormRef">
        <el-form-item :label="$t('ai-cleaner.datasource.name')" prop="name">
          <el-input v-model="dataSourceForm.name" :placeholder="$t('ai-cleaner.datasource.name')" />
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.datasource.type')" prop="type">
          <el-select v-model="dataSourceForm.type" :placeholder="$t('ai-cleaner.datasource.type')" style="width: 100%">
            <el-option 
              v-for="(value, key) in $t('ai-cleaner.datasource.types')" 
              :key="key" 
              :label="value" 
              :value="key" 
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.datasource.dataFormat')" prop="dataFormat">
          <el-input v-model="dataSourceForm.dataFormat" :placeholder="$t('ai-cleaner.datasource.dataFormat')" />
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.datasource.samplingInterval')" prop="samplingInterval">
          <el-input-number v-model="dataSourceForm.samplingInterval" :min="1" :placeholder="$t('ai-cleaner.datasource.samplingInterval')" />
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.datasource.description')" prop="description">
          <el-input 
            v-model="dataSourceForm.description" 
            type="textarea" 
            rows="3" 
            :placeholder="$t('ai-cleaner.datasource.description')"
          />
        </el-form-item>
        
        <!-- 连接配置 -->
        <h3>{{ $t('ai-cleaner.datasource.connectionConfig') }}</h3>
        <div v-if="dataSourceForm.type === 'MYSQL'">
          <el-form-item label="Host" prop="connectionConfig.host">
            <el-input v-model="dataSourceForm.connectionConfig.host" placeholder="Host" />
          </el-form-item>
          <el-form-item label="Port" prop="connectionConfig.port">
            <el-input-number v-model="dataSourceForm.connectionConfig.port" :min="1" :max="65535" placeholder="Port" />
          </el-form-item>
          <el-form-item label="Database" prop="connectionConfig.database">
            <el-input v-model="dataSourceForm.connectionConfig.database" placeholder="Database" />
          </el-form-item>
          <el-form-item label="Username" prop="connectionConfig.username">
            <el-input v-model="dataSourceForm.connectionConfig.username" placeholder="Username" />
          </el-form-item>
          <el-form-item label="Password" prop="connectionConfig.password">
            <el-input v-model="dataSourceForm.connectionConfig.password" type="password" placeholder="Password" />
          </el-form-item>
        </div>
        <div v-else-if="dataSourceForm.type === 'INFLUXDB'">
          <el-form-item label="URL" prop="connectionConfig.url">
            <el-input v-model="dataSourceForm.connectionConfig.url" placeholder="URL" />
          </el-form-item>
          <el-form-item label="Token" prop="connectionConfig.token">
            <el-input v-model="dataSourceForm.connectionConfig.token" placeholder="Token" />
          </el-form-item>
          <el-form-item label="Organization" prop="connectionConfig.org">
            <el-input v-model="dataSourceForm.connectionConfig.org" placeholder="Organization" />
          </el-form-item>
          <el-form-item label="Bucket" prop="connectionConfig.bucket">
            <el-input v-model="dataSourceForm.connectionConfig.bucket" placeholder="Bucket" />
          </el-form-item>
        </div>
        <div v-else-if="dataSourceForm.type === 'HTTP_API'">
          <el-form-item label="URL" prop="connectionConfig.url">
            <el-input v-model="dataSourceForm.connectionConfig.url" placeholder="URL" />
          </el-form-item>
          <el-form-item label="Method" prop="connectionConfig.method">
            <el-select v-model="dataSourceForm.connectionConfig.method" placeholder="Method">
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
            </el-select>
          </el-form-item>
          <el-form-item label="Headers" prop="connectionConfig.headers">
            <el-input 
              v-model="dataSourceForm.connectionConfig.headers" 
              type="textarea" 
              rows="3" 
              placeholder="Headers (JSON format)" 
            />
          </el-form-item>
        </div>
        <div v-else-if="dataSourceForm.type === 'FILE'">
          <el-form-item label="Path" prop="connectionConfig.path">
            <el-input v-model="dataSourceForm.connectionConfig.path" placeholder="File path" />
          </el-form-item>
          <el-form-item label="Format" prop="connectionConfig.format">
            <el-select v-model="dataSourceForm.connectionConfig.format" placeholder="File format">
              <el-option label="CSV" value="CSV" />
              <el-option label="JSON" value="JSON" />
              <el-option label="XML" value="XML" />
            </el-select>
          </el-form-item>
        </div>
        <div v-else-if="dataSourceForm.type === 'SPOUT'">
          <el-form-item label="Topic" prop="connectionConfig.topic">
            <el-input v-model="dataSourceForm.connectionConfig.topic" placeholder="Topic" />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('ai-cleaner.common.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm">{{ $t('ai-cleaner.common.save') }}</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      :title="$t('ai-cleaner.common.confirmDelete')"
      v-model="deleteDialogVisible"
      width="30%"
    >
      <p>{{ $t('ai-cleaner.common.confirmDeleteMessage', { name: currentDataSource.name }) }}</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">{{ $t('ai-cleaner.common.cancel') }}</el-button>
          <el-button type="danger" @click="confirmDelete">{{ $t('ai-cleaner.common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import api from '@/utils/api';

// 数据
const dataSources = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const dialogType = ref('create'); // 'create' or 'edit'
const currentDataSource = ref({});
const dataSourceFormRef = ref(null);

// 表单数据
const dataSourceForm = reactive({
  id: null,
  name: '',
  type: 'MYSQL',
  description: '',
  dataFormat: 'JSON',
  samplingInterval: 60,
  status: 'INACTIVE',
  connectionConfig: {
    host: '',
    port: 3306,
    database: '',
    username: '',
    password: '',
    url: '',
    token: '',
    org: '',
    bucket: '',
    method: 'GET',
    headers: '',
    path: '',
    format: 'CSV',
    topic: ''
  }
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: 'Please input data source name', trigger: 'blur' },
    { min: 2, max: 50, message: 'Length should be 2 to 50 characters', trigger: 'blur' }
  ],
  type: [
    { required: true, message: 'Please select data source type', trigger: 'change' }
  ],
  dataFormat: [
    { required: true, message: 'Please input data format', trigger: 'blur' }
  ],
  samplingInterval: [
    { required: true, message: 'Please input sampling interval', trigger: 'blur' }
  ]
};

// 生命周期钩子
onMounted(() => {
  fetchDataSources();
});

// 获取所有数据源
const fetchDataSources = async () => {
  loading.value = true;
  try {
    const response = await api.dataSource.getAllDataSources();
    dataSources.value = response.data || [];
  } catch (error) {
    console.error('Failed to fetch data sources:', error);
    ElMessage.error('Failed to fetch data sources');
  } finally {
    loading.value = false;
  }
};

// 创建数据源
const handleCreateDataSource = () => {
  dialogType.value = 'create';
  resetForm();
  dialogVisible.value = true;
};

// 编辑数据源
const handleEditDataSource = (dataSource) => {
  dialogType.value = 'edit';
  currentDataSource.value = dataSource;
  
  // 复制数据源数据到表单
  dataSourceForm.id = dataSource.id;
  dataSourceForm.name = dataSource.name;
  dataSourceForm.type = dataSource.type;
  dataSourceForm.description = dataSource.description || '';
  dataSourceForm.dataFormat = dataSource.dataFormat;
  dataSourceForm.samplingInterval = dataSource.samplingInterval;
  dataSourceForm.status = dataSource.status;
  
  // 复制连接配置
  if (dataSource.connectionConfig) {
    dataSourceForm.connectionConfig = { ...dataSource.connectionConfig };
  } else {
    resetConnectionConfig();
  }
  
  dialogVisible.value = true;
};

// 激活数据源
const handleActivateDataSource = async (dataSource) => {
  try {
    await api.dataSource.activateDataSource(dataSource.id);
    ElMessage.success('Data source activated successfully');
    fetchDataSources();
  } catch (error) {
    console.error('Failed to activate data source:', error);
    ElMessage.error('Failed to activate data source');
  }
};

// 停用数据源
const handleDeactivateDataSource = async (dataSource) => {
  try {
    await api.dataSource.deactivateDataSource(dataSource.id);
    ElMessage.success('Data source deactivated successfully');
    fetchDataSources();
  } catch (error) {
    console.error('Failed to deactivate data source:', error);
    ElMessage.error('Failed to deactivate data source');
  }
};

// 测试连接
const handleTestConnection = async (dataSource) => {
  try {
    const result = await api.dataSource.testConnection(dataSource.id);
    if (result.data) {
      ElMessage.success('Connection test successful');
    } else {
      ElMessage.error('Connection test failed');
    }
  } catch (error) {
    console.error('Connection test failed:', error);
    ElMessage.error('Connection test failed');
  }
};

// 删除数据源
const handleDeleteDataSource = (dataSource) => {
  currentDataSource.value = dataSource;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  try {
    await api.dataSource.deleteDataSource(currentDataSource.value.id);
    ElMessage.success('Data source deleted successfully');
    deleteDialogVisible.value = false;
    fetchDataSources();
  } catch (error) {
    console.error('Failed to delete data source:', error);
    ElMessage.error('Failed to delete data source');
  }
};

// 提交表单
const submitForm = async () => {
  if (!dataSourceFormRef.value) return;
  
  dataSourceFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 处理连接配置
        if (dataSourceForm.type === 'HTTP_API' && typeof dataSourceForm.connectionConfig.headers === 'string') {
          try {
            dataSourceForm.connectionConfig.headers = JSON.parse(dataSourceForm.connectionConfig.headers);
          } catch (e) {
            ElMessage.error('Headers must be valid JSON');
            return;
          }
        }
        
        if (dialogType.value === 'create') {
          await api.dataSource.createDataSource(dataSourceForm);
          ElMessage.success('Data source created successfully');
        } else {
          await api.dataSource.updateDataSource(dataSourceForm);
          ElMessage.success('Data source updated successfully');
        }
        dialogVisible.value = false;
        fetchDataSources();
      } catch (error) {
        console.error('Failed to save data source:', error);
        ElMessage.error('Failed to save data source');
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  dataSourceForm.id = null;
  dataSourceForm.name = '';
  dataSourceForm.type = 'MYSQL';
  dataSourceForm.description = '';
  dataSourceForm.dataFormat = 'JSON';
  dataSourceForm.samplingInterval = 60;
  dataSourceForm.status = 'INACTIVE';
  
  resetConnectionConfig();
  
  if (dataSourceFormRef.value) {
    dataSourceFormRef.value.resetFields();
  }
};

// 重置连接配置
const resetConnectionConfig = () => {
  dataSourceForm.connectionConfig = {
    host: '',
    port: 3306,
    database: '',
    username: '',
    password: '',
    url: '',
    token: '',
    org: '',
    bucket: '',
    method: 'GET',
    headers: '',
    path: '',
    format: 'CSV',
    topic: ''
  };
};
</script>

<style scoped>
.datasource-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.datasource-list {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
