<template>
  <div class="strategy-container">
    <div class="page-header">
      <h2>{{ $t('ai-cleaner.strategy.list') }}</h2>
      <el-button type="primary" @click="handleCreateStrategy">
        <el-icon><plus /></el-icon>
        {{ $t('ai-cleaner.strategy.create') }}
      </el-button>
    </div>
    
    <!-- 策略列表 -->
    <el-card class="strategy-list" shadow="never">
      <el-table :data="strategies" v-loading="loading" border style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" :label="$t('ai-cleaner.strategy.name')" min-width="150" />
        <el-table-column prop="type" :label="$t('ai-cleaner.strategy.type')" width="120">
          <template #default="scope">
            {{ $t(`ai-cleaner.strategy.types.${scope.row.type}`) }}
          </template>
        </el-table-column>
        <el-table-column prop="dataType" :label="$t('ai-cleaner.strategy.dataType')" width="120" />
        <el-table-column prop="status" :label="$t('ai-cleaner.strategy.status')" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'ACTIVE' ? 'success' : 'info'">
              {{ $t(`ai-cleaner.strategy.status.${scope.row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('ai-cleaner.common.actions')" width="250">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'INACTIVE'" 
              type="success" 
              size="small" 
              @click="handleActivateStrategy(scope.row)"
            >
              {{ $t('ai-cleaner.strategy.actions.activate') }}
            </el-button>
            <el-button 
              v-if="scope.row.status === 'ACTIVE'" 
              type="warning" 
              size="small" 
              @click="handleDeactivateStrategy(scope.row)"
            >
              {{ $t('ai-cleaner.strategy.actions.deactivate') }}
            </el-button>
            <el-button type="primary" size="small" @click="handleEditStrategy(scope.row)">
              {{ $t('ai-cleaner.strategy.actions.edit') }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDeleteStrategy(scope.row)">
              {{ $t('ai-cleaner.strategy.actions.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 策略表单对话框 -->
    <el-dialog 
      :title="dialogType === 'create' ? $t('ai-cleaner.strategy.create') : $t('ai-cleaner.strategy.edit')" 
      v-model="dialogVisible"
      width="60%"
    >
      <el-form :model="strategyForm" label-width="120px" :rules="rules" ref="strategyFormRef">
        <el-form-item :label="$t('ai-cleaner.strategy.name')" prop="name">
          <el-input v-model="strategyForm.name" :placeholder="$t('ai-cleaner.strategy.name')" />
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.strategy.type')" prop="type">
          <el-select v-model="strategyForm.type" :placeholder="$t('ai-cleaner.strategy.type')" style="width: 100%">
            <el-option 
              v-for="(value, key) in $t('ai-cleaner.strategy.types')" 
              :key="key" 
              :label="value" 
              :value="key" 
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.strategy.dataSource')" prop="dataSourceId">
          <el-select v-model="strategyForm.dataSourceId" :placeholder="$t('ai-cleaner.strategy.dataSource')" style="width: 100%">
            <el-option 
              v-for="source in dataSources" 
              :key="source.id" 
              :label="source.name" 
              :value="source.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.strategy.dataType')" prop="dataType">
          <el-input v-model="strategyForm.dataType" :placeholder="$t('ai-cleaner.strategy.dataType')" />
        </el-form-item>
        <el-form-item :label="$t('ai-cleaner.strategy.description')" prop="description">
          <el-input 
            v-model="strategyForm.description" 
            type="textarea" 
            rows="3" 
            :placeholder="$t('ai-cleaner.strategy.description')"
          />
        </el-form-item>
        
        <!-- 清洗规则 -->
        <div class="rules-header">
          <h3>{{ $t('ai-cleaner.strategy.rules') }}</h3>
          <el-button type="primary" size="small" @click="addRule">
            <el-icon><plus /></el-icon>
            {{ $t('ai-cleaner.common.add') }}
          </el-button>
        </div>
        
        <el-table :data="strategyForm.cleaningRules" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column :label="$t('ai-cleaner.strategy.rule.name')" min-width="150">
            <template #default="scope">
              <el-input v-model="scope.row.name" size="small" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('ai-cleaner.strategy.rule.type')" width="180">
            <template #default="scope">
              <el-select v-model="scope.row.type" size="small" style="width: 100%">
                <el-option 
                  v-for="(value, key) in $t('ai-cleaner.strategy.rule.types')" 
                  :key="key" 
                  :label="value" 
                  :value="key" 
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="$t('ai-cleaner.strategy.rule.order')" width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.order" :min="1" size="small" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('ai-cleaner.strategy.rule.enabled')" width="100">
            <template #default="scope">
              <el-switch v-model="scope.row.enabled" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('ai-cleaner.common.actions')" width="100">
            <template #default="scope">
              <el-button type="danger" size="small" @click="removeRule(scope.$index)">
                {{ $t('ai-cleaner.common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('ai-cleaner.common.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm">{{ $t('ai-cleaner.common.save') }}</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      :title="$t('ai-cleaner.common.confirmDelete')"
      v-model="deleteDialogVisible"
      width="30%"
    >
      <p>{{ $t('ai-cleaner.common.confirmDeleteMessage', { name: currentStrategy.name }) }}</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">{{ $t('ai-cleaner.common.cancel') }}</el-button>
          <el-button type="danger" @click="confirmDelete">{{ $t('ai-cleaner.common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import api from '@/utils/api';

// 数据
const strategies = ref([]);
const dataSources = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const dialogType = ref('create'); // 'create' or 'edit'
const currentStrategy = ref({});
const strategyFormRef = ref(null);

// 表单数据
const strategyForm = reactive({
  id: null,
  name: '',
  type: 'REALTIME',
  description: '',
  dataSourceId: null,
  dataType: '',
  status: 'INACTIVE',
  cleaningRules: []
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: 'Please input strategy name', trigger: 'blur' },
    { min: 2, max: 50, message: 'Length should be 2 to 50 characters', trigger: 'blur' }
  ],
  type: [
    { required: true, message: 'Please select strategy type', trigger: 'change' }
  ],
  dataSourceId: [
    { required: true, message: 'Please select data source', trigger: 'change' }
  ],
  dataType: [
    { required: true, message: 'Please input data type', trigger: 'blur' }
  ]
};

// 生命周期钩子
onMounted(() => {
  fetchStrategies();
  fetchDataSources();
});

// 获取所有策略
const fetchStrategies = async () => {
  loading.value = true;
  try {
    const response = await api.strategy.getAllStrategies();
    strategies.value = response.data || [];
  } catch (error) {
    console.error('Failed to fetch strategies:', error);
    ElMessage.error('Failed to fetch strategies');
  } finally {
    loading.value = false;
  }
};

// 获取所有数据源
const fetchDataSources = async () => {
  try {
    const response = await api.dataSource.getAllDataSources();
    dataSources.value = response.data || [];
  } catch (error) {
    console.error('Failed to fetch data sources:', error);
    ElMessage.error('Failed to fetch data sources');
  }
};

// 创建策略
const handleCreateStrategy = () => {
  dialogType.value = 'create';
  resetForm();
  dialogVisible.value = true;
};

// 编辑策略
const handleEditStrategy = (strategy) => {
  dialogType.value = 'edit';
  currentStrategy.value = strategy;
  
  // 复制策略数据到表单
  strategyForm.id = strategy.id;
  strategyForm.name = strategy.name;
  strategyForm.type = strategy.type;
  strategyForm.description = strategy.description || '';
  strategyForm.dataSourceId = strategy.dataSourceId;
  strategyForm.dataType = strategy.dataType;
  strategyForm.status = strategy.status;
  strategyForm.cleaningRules = strategy.cleaningRules ? [...strategy.cleaningRules] : [];
  
  dialogVisible.value = true;
};

// 激活策略
const handleActivateStrategy = async (strategy) => {
  try {
    await api.strategy.activateStrategy(strategy.id);
    ElMessage.success('Strategy activated successfully');
    fetchStrategies();
  } catch (error) {
    console.error('Failed to activate strategy:', error);
    ElMessage.error('Failed to activate strategy');
  }
};

// 停用策略
const handleDeactivateStrategy = async (strategy) => {
  try {
    await api.strategy.deactivateStrategy(strategy.id);
    ElMessage.success('Strategy deactivated successfully');
    fetchStrategies();
  } catch (error) {
    console.error('Failed to deactivate strategy:', error);
    ElMessage.error('Failed to deactivate strategy');
  }
};

// 删除策略
const handleDeleteStrategy = (strategy) => {
  currentStrategy.value = strategy;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  try {
    await api.strategy.deleteStrategy(currentStrategy.value.id);
    ElMessage.success('Strategy deleted successfully');
    deleteDialogVisible.value = false;
    fetchStrategies();
  } catch (error) {
    console.error('Failed to delete strategy:', error);
    ElMessage.error('Failed to delete strategy');
  }
};

// 提交表单
const submitForm = async () => {
  if (!strategyFormRef.value) return;
  
  strategyFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'create') {
          await api.strategy.createStrategy(strategyForm);
          ElMessage.success('Strategy created successfully');
        } else {
          await api.strategy.updateStrategy(strategyForm);
          ElMessage.success('Strategy updated successfully');
        }
        dialogVisible.value = false;
        fetchStrategies();
      } catch (error) {
        console.error('Failed to save strategy:', error);
        ElMessage.error('Failed to save strategy');
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  strategyForm.id = null;
  strategyForm.name = '';
  strategyForm.type = 'REALTIME';
  strategyForm.description = '';
  strategyForm.dataSourceId = null;
  strategyForm.dataType = '';
  strategyForm.status = 'INACTIVE';
  strategyForm.cleaningRules = [];
  
  if (strategyFormRef.value) {
    strategyFormRef.value.resetFields();
  }
};

// 添加规则
const addRule = () => {
  strategyForm.cleaningRules.push({
    id: null,
    name: '',
    type: 'missing_value',
    description: '',
    parameters: {},
    order: strategyForm.cleaningRules.length + 1,
    enabled: true
  });
};

// 删除规则
const removeRule = (index) => {
  strategyForm.cleaningRules.splice(index, 1);
  
  // 重新排序
  strategyForm.cleaningRules.forEach((rule, idx) => {
    rule.order = idx + 1;
  });
};
</script>

<style scoped>
.strategy-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.strategy-list {
  margin-bottom: 20px;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.rules-header h3 {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
