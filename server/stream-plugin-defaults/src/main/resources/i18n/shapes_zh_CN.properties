# ==================================================
# Comment Component
# ==================================================
streams.shapes.comment.name=注释
streams.shapes.comment.alias=文本注释
streams.shapes.comment.tooltip=注释节点，在图上增加注释，支持markdown。
streams.shapes.comment.groups=基础
streams.shapes.comment.tags=注释,注解
# ==================================================
# Switch Component
# ==================================================
streams.shapes.data-switch.name=数据条件分支
streams.shapes.data-switch.alias=条件分支
streams.shapes.data-switch.tooltip=通过条件控制数据流走向
streams.shapes.data-switch.groups=基础
streams.shapes.data-switch.tags=Switch,分支,判断,条件
streams.shapes.data-switch.inlet1.name=In
streams.shapes.data-switch.outlet1.name=Out-{index}
# ==================================================
# Timer Component
# ==================================================
streams.shapes.fixed-timer.name=固定间隔定时器
streams.shapes.fixed-timer.alias=定时器
streams.shapes.fixed-timer.tooltip=固定间隔的定时器组件
streams.shapes.fixed-timer.groups=基础
streams.shapes.fixed-timer.tags=定时器,Time,Timer
streams.shapes.fixed-timer.outlet1.name=滴答事件
streams.shapes.fixed-timer.outlet2.name=启动事件
streams.shapes.fixed-timer.outlet3.name=停止事件


# ==================================================
# Tracer Component
# ==================================================
streams.shapes.data-tracer.name=数据跟踪器
streams.shapes.data-tracer.alias=跟踪器
streams.shapes.data-tracer.tooltip=可以实时查看数据流数据
streams.shapes.data-tracer.groups=基础
streams.shapes.data-tracer.tags=调试,跟踪
streams.shapes.data-tracer.inlet1.name=输入
# ==================================================
# DataMapper Component
# ==================================================
streams.shapes.data-mapper.name=数据映射
streams.shapes.data-mapper.alias=映射
streams.shapes.data-mapper.tooltip=数据对象格式根据设置条件映射转换
streams.shapes.data-mapper.groups=基础
streams.shapes.data-mapper.tags=Mapper,映射,转换
streams.shapes.data-mapper.inlet1.name=输入
streams.shapes.data-mapper.outlet1.name=输出
# ==================================================
# ChangeFilter Component
# ==================================================
streams.shapes.data-filter.name=变更过滤器
streams.shapes.data-filter.alias=变更
streams.shapes.data-filter.tooltip=用于过滤未变更的数据
streams.shapes.data-filter.groups=基础
streams.shapes.data-filter.tags=变更,过滤
streams.shapes.data-filter.inlet1.name=数据输入
streams.shapes.data-filter.outlet1.name=数据输出
# ==================================================
# DataProcess Component
# ==================================================
streams.shapes.data-process.name=数据处理
streams.shapes.data-process.alias=处理
streams.shapes.data-process.tooltip=测试的
streams.shapes.data-process.groups=基础
streams.shapes.data-process.tags=处理,转换
streams.shapes.data-process.inlet1.name=数据输入
streams.shapes.data-process.outlet1.name=数据输出

# ==================================================
# Delay Component
# ==================================================
streams.shapes.delay.name=延迟处理
streams.shapes.delay.alias=延迟
streams.shapes.delay.tooltip=延迟数据流中数据的处理
streams.shapes.delay.groups=基础
streams.shapes.delay.tags=处理,转换
streams.shapes.delay.inlet1.name=数据输入
streams.shapes.delay.outlet1.name=数据输出

# ==================================================
# TCP Server Component
# ==================================================
streams.shapes.tcp-server.name=TCP服务器
streams.shapes.tcp-server.alias=TCP服务器
streams.shapes.tcp-server.tooltip=提供TCP服务器功能，监听指定端口并接收TCP连接
streams.shapes.tcp-server.groups=基础
streams.shapes.tcp-server.tags=网络,TCP,服务器
streams.shapes.tcp-server.outlet1.name=数据输出





# ==================================================
# TCP Server Component
# ==================================================
streams.shapes.property-inject.name=属性注入
streams.shapes.property-inject.alias=注入
streams.shapes.property-inject.tooltip=提供属性字段的动态注入功能
streams.shapes.property-inject.groups=基础
streams.shapes.property-inject.tags=属性,字段,注入
streams.shapes.property-inject.inlet1.name=数据输入
streams.shapes.property-inject.outlet1.name=数据输出
