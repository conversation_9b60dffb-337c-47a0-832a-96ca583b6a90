package com.siteweb.stream.defaults.options;

import com.siteweb.tcs.common.expression.MemberAccessExpression;
import com.siteweb.stream.common.stream.StreamShapeOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-02-18)
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class SwitchShapeOption extends StreamShapeOption {

    /**
     * 属性名
     */
    private MemberAccessExpression property;

    /**
     * 分支配置
     */
    private List<SwitchBranchOption> branches = new ArrayList<>();

    /**
     * 是否匹配全部Branches？ <br/>
     * 如果为是，不管是否匹配成功继续匹配下面branch直至匹配完所有
     * 如果为否，匹配到一个即退出匹配
     */
    private boolean matchAll = false;


}
