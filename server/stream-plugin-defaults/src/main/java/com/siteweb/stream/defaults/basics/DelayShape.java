package com.siteweb.stream.defaults.basics;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.common.types.Any;
import com.siteweb.stream.defaults.options.DelayShapeOption;
import com.siteweb.stream.defaults.options.defaults.TimerDefaultOption;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.Cancellable;
import org.apache.pekko.actor.Scheduler;
import scala.concurrent.duration.Duration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (2025-02-27)
 **/
@Slf4j
@EditorHidden
@Shape(type = "delay")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeDefaultOptions(TimerDefaultOption.class)
@ShapeInlet(id = 0x01, type = Any.class)
@ShapeOutlet(id = 0x1000, type = Any.class)
public class DelayShape extends AbstractShape {
    @Recoverable
    private DelayShapeOption option;

    private Cancellable scheduleTask;

    private final Scheduler scheduler;

    public DelayShape(ShapeRuntimeContext context) {
        super(context);
        scheduler = context().system().getScheduler();
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof DelayShapeOption delayShapeOption) {
            option = delayShapeOption;
        }
    }


    @Override
    protected void processMessage(StreamMessage message) {
        if (DelayShapeOption.DelayStrategy.LAST.equals(option.getStrategy()) && scheduleTask != null && !scheduleTask.isCancelled()) {
            scheduleTask.cancel();
        }
        var outlet = context.getOutLet((short) 0x01);
        scheduleTask = scheduler.scheduleOnce(
                Duration.create(option.getMilliseconds(), TimeUnit.MILLISECONDS),
                () -> outlet.broadcast(message),
                getContext().system().dispatcher()
        );
    }


}
