package com.siteweb.stream.defaults.networks;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.messages.TextMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.defaults.messages.HTTPRequestMessage;
import com.siteweb.stream.defaults.options.HTTPRequestShapeOption;
import com.siteweb.stream.defaults.options.defaults.CommentDefaultOption;
import com.siteweb.tcs.common.system.ClusterContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.http.javadsl.Http;
import org.apache.pekko.http.javadsl.model.HttpEntities;
import org.apache.pekko.http.javadsl.model.HttpRequest;
import org.apache.pekko.http.javadsl.model.HttpResponse;
import org.apache.pekko.http.javadsl.unmarshalling.Unmarshaller;
import org.apache.pekko.stream.Materializer;

/**
 * <AUTHOR> (2025-04-24)
 **/
@Slf4j
@EditorHidden
@Shape(type = "http-request")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeColor(bkColor = "#FFFFFF")
@ShapeDefaultOptions(CommentDefaultOption.class)
@ShapeInlet(id = 0x01, type = StreamMessage.class)
@ShapeOutlet(id = 0x01, type = StreamMessage.class)
public class HTTPRequestShape extends AbstractShape {

    private final Http http;
    private final Materializer materializer;

    @Recoverable
    private HTTPRequestShapeOption options;

    public HTTPRequestShape(ShapeRuntimeContext context) {
        super(context);
        materializer = ClusterContext.getMaterializer();
        http = ClusterContext.getHttp();
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof HTTPRequestShapeOption httpRequestShapeOption) {
            this.options = httpRequestShapeOption;
        }
    }

    @Override
    protected void processMessage(StreamMessage message) {
        if (message instanceof HTTPRequestMessage httpRequestMessage) {
            var entity = HttpEntities.create(httpRequestMessage.getContentType(), httpRequestMessage.getPayload());
            HttpRequest httpRequest = HttpRequest.create(options.getServeUrl()).withMethod(httpRequestMessage.getMethod()).withEntity(entity);
            http.singleRequest(httpRequest).whenComplete((response, throwable) -> {
                if (throwable != null) {
                    processException(httpRequestMessage, throwable);
                } else {
                    processResponse(httpRequestMessage, response);
                }
            });
        }
    }


    private void processException(HTTPRequestMessage request, Throwable throwable) {
        // TODO 临时先打印。
        log.error("Unexpected errors were encountered when calling the Http service API '{}'", options.getServeUrl(), throwable);
    }


    private void processResponse(HTTPRequestMessage request, HttpResponse response) {
        Unmarshaller.entityToString().unmarshal(response.entity(), materializer).whenComplete((body, throwable) -> {
            var message = new TextMessage();
            message.setPayload(body);
            context.getOutLet(0x01).broadcast(message);
        });
    }

}
