---
description: 
globs: 
alwaysApply: true
---
# 前端开发规范指南

本文档提供了项目前端开发的规范指南，包括菜单配置、路由添加、页面创建、API请求及后端响应处理等关键内容。

## 目录结构

项目遵循以下目录结构：

- `src/views/`：页面视图，按业务模块分子目录
- `src/components/`：通用组件
- `src/api/`：接口请求与类型定义
- `src/router/`：路由配置
- `src/utils/`：工具函数
- `src/store/`：状态管理
- `src/layout/`：布局组件
- `src/style/`：全局样式
- `src/assets/`：静态资源
- `locales/`：国际化文件

## 路由与菜单配置

### 添加新路由

路由配置位于 `src/router/modules/` 目录下，每个功能模块创建独立的路由文件。

```typescript
// src/router/modules/system.ts
import { $t } from "@/plugins/i18n";
const Layout = () => import("@/layout/index.vue");

export default {
  path: "/system",
  name: "System",
  component: Layout,
  redirect: "/system/plugin",
  meta: {
    icon: "ep/setting",
    title: "标题",
    rank: 10
  },
  children: [
    {
      path: "/system/plugin",
      name: "PluginManagement",
      component: () => import("@/views/system/plugin/index.vue"),
      meta: {
        title: "标题"
      }
    }
  ]
} satisfies RouteConfigsTable;
```

### 菜单国际化配置

菜单文本需在国际化文件中配置，位于 `locales/` 目录：

```yaml
# locales/zh-CN.yaml
menus:
  systemManagement: 系统管理
  pluginManagement: 插件管理
```

```yaml
# locales/en.yaml
menus:
  systemManagement: System Management
  pluginManagement: Plugin Management
```

## 创建新页面

创建新页面需要遵循以下步骤：

1. 在 `src/views/` 目录下创建对应的页面目录和文件
2. 在 `src/router/modules/` 添加对应的路由配置
3. 在国际化文件中添加对应的菜单文本

### 页面基本结构

```vue
<template>
  <div class="page-container">
    <!-- 页面内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
// 引入组件和API

// 状态定义
const loading = ref(false);
const dataList = ref([]);

// 生命周期钩子
onMounted(() => {
  fetchData();
});

// 数据获取方法
const fetchData = async () => {
  // API调用实现
};
</script>

<style scoped>
/* 样式定义 */
</style>
```

## 样式规范

### Tailwind CSS 使用规范

项目使用 Tailwind CSS 作为主要样式方案，请遵循以下规范：

1. **优先使用 Tailwind 类**：优先使用 Tailwind 提供的工具类，而非自定义样式
   ```html
   <!-- 推荐 -->
   <div class="flex justify-between items-center p-4 bg-gray-50 rounded-md"></div>
   
   <!-- 不推荐 -->
   <div class="header-container"></div>
   ```

2. **响应式设计**：使用 Tailwind 的响应式前缀进行断点设计
   ```html
   <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"></div>
   ```

3. **自定义工具类**：项目定义了一些常用工具类，如 `flex-c`、`flex-bc` 等
   ```html
   <div class="flex-bc"><!-- 使用flex布局，两端对齐且垂直居中 --></div>
   ```

4. **暗黑模式适配**：使用 `dark:` 前缀适配暗黑模式
   ```html
   <div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white"></div>
   ```

### 颜色变量使用规范

项目支持主题切换功能，**严禁使用硬编码颜色值**，必须使用主题变量确保在不同主题下的正确显示：

#### 1. **严禁硬编码颜色 ❌**

以下做法是错误的，会导致主题切换时颜色不匹配：

```html
<!-- ❌ 错误：硬编码颜色值 -->
<div class="bg-blue-500 text-white"></div>
<div style="background-color: #409eff; color: #ffffff;"></div>
<div class="border-[#409eff]"></div>

<!-- ❌ 错误：使用渐变色 -->
<div class="bg-gradient-to-r from-blue-500 to-purple-600"></div>
<button class="bg-gradient-to-br from-blue-500 to-purple-600"></button>
```

```css
/* ❌ 错误：CSS中硬编码颜色 */
.custom-element {
  background-color: #409eff;
  color: #ffffff;
  border-color: rgb(64, 158, 255);
}
```

#### 2. **使用 Element Plus 主题变量 ✅**

优先使用 Element Plus 提供的颜色变量：

```css
/* ✅ 正确：使用 Element Plus 变量 */
.custom-element {
  color: var(--el-color-primary);
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color);
}

/* 常用的 Element Plus 颜色变量 */
var(--el-color-primary)        /* 主色 */
var(--el-color-success)        /* 成功色 */
var(--el-color-warning)        /* 警告色 */
var(--el-color-danger)         /* 危险色 */
var(--el-color-error)          /* 错误色 */
var(--el-color-info)           /* 信息色 */
var(--el-bg-color)             /* 背景色 */
var(--el-text-color-primary)   /* 主要文字色 */
var(--el-text-color-regular)   /* 常规文字色 */
var(--el-text-color-secondary) /* 次要文字色 */
var(--el-border-color)         /* 边框色 */
```

#### 3. **使用项目主题变量 ✅**

使用项目定义的主题变量：

```css
/* ✅ 正确：使用项目主题变量 */
.custom-element {
  color: var(--pure-theme-menu-text);
  background-color: var(--pure-theme-menu-bg);
  border-color: var(--pure-theme-border);
}

/* 项目主题变量示例 */
var(--pure-theme-menu-text)    /* 菜单文字色 */
var(--pure-theme-menu-bg)      /* 菜单背景色 */
var(--pure-theme-border)       /* 主题边框色 */
```

#### 4. **在 Tailwind 中使用变量 ✅**

在 Tailwind CSS 中正确使用变量：

```html
<!-- ✅ 正确：使用 Tailwind 预定义类 -->
<div class="bg-primary text-white border-primary"></div>

<!-- ✅ 正确：使用 CSS 变量 -->
<div class="text-[var(--el-color-primary)] bg-[var(--el-bg-color)]"></div>
<div class="border-[var(--el-border-color)]"></div>

<!-- ✅ 正确：使用透明度变体 -->
<div class="bg-primary/10 text-primary/80"></div>
<div class="bg-[var(--el-color-primary)]/20"></div>
```

#### 5. **Tailwind 预设颜色系统 ✅**

项目已配置的 Tailwind 颜色可以安全使用：

```html
<!-- ✅ 正确：使用系统颜色 -->
<div class="text-gray-700 bg-gray-50 border-gray-200"></div>
<div class="text-gray-700 dark:text-gray-300"></div>
<div class="bg-white dark:bg-gray-800"></div>
```

#### 6. **主题切换适配要求**

所有颜色必须支持明暗主题切换：

```html
<!-- ✅ 正确：同时适配明暗主题 -->
<div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white"></div>
<div class="border-gray-200 dark:border-gray-700"></div>

<!-- ✅ 正确：使用主题变量自动适配 -->
<div class="bg-[var(--el-bg-color)] text-[var(--el-text-color-primary)]"></div>
```

#### 7. **组件库颜色使用**

Element Plus 组件会自动使用主题色：

```html
<!-- ✅ 正确：使用组件内置主题 -->
<el-button type="primary">主要按钮</el-button>
<el-tag type="success">成功标签</el-tag>
<el-alert type="warning">警告提示</el-alert>
```

#### 8. **开发检查清单**

在开发过程中，请确保：

- [ ] 没有使用任何硬编码的颜色值（如 `#409eff`、`rgb(64, 158, 255)`）
- [ ] 没有使用渐变色（如 `bg-gradient-to-r`）
- [ ] 所有自定义颜色都使用 CSS 变量
- [ ] 适配了明暗两种主题模式
- [ ] 在主题切换时测试了页面显示效果

#### 9. **常见错误及修正**

| 错误写法 | 正确写法 |
|---------|---------|
| `bg-blue-500` | `bg-primary` 或 `bg-[var(--el-color-primary)]` |
| `text-blue-600` | `text-primary` 或 `text-[var(--el-color-primary)]` |
| `border-blue-300` | `border-primary/30` 或 `border-[var(--el-color-primary)]/30` |
| `bg-gradient-to-r from-blue-500 to-purple-600` | `bg-primary` |
| `style="color: #409eff"` | `class="text-[var(--el-color-primary)]"` |

遵循这些规范可以确保项目在不同主题下都能正确显示，提升用户体验。

## API请求规范

### API文件创建

API请求相关代码应放在 `src/api/` 目录下，按功能模块创建独立的文件：

```typescript
// src/api/plugin.ts
import { http } from "@/utils/http";

export interface PluginInfo {
  pluginId: string;
  pluginName: string;
  // 其他字段定义
}

export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

/**
 * 获取插件列表
 */
export const getPluginList = () => {
  return http.request<ApiResponse<PluginInfo[]>>(
    "get",
    "/api/thing/plugins/list"
  );
};

/**
 * 上传插件
 * @param file 插件文件
 */
export const uploadPlugin = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);
  return http.request<ApiResponse<any>>("post", "/api/thing/plugins/install", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
```

### 后端响应格式与处理

后端API响应格式统一为：

```typescript
{
    "code": 0,  //这里是0表示正常 非0表示请求错误
    "timestamp": 1749620121400, //时间戳
    "data": {   //返回值
    },
    "msg": null  //错误消息
}
```

在组件中处理API响应：

```typescript
// 示例：请求处理
const fetchPluginList = async () => {
  loading.value = true;
  try {
    const res = await getPluginList();
    if (res.state) {  // 使用 state 而非 success 判断请求是否成功
      plugins.value = res.data;
    } else {
      ElMessage.error(res.err_msg || "获取数据失败");
    }
  } catch (error) {
    ElMessage.error("请求异常");
    console.error(error);
  } finally {
    loading.value = false;
  }
};
```

### 请求错误处理

- 使用 `res.state` 判断业务逻辑是否成功
- 使用 `res.err_msg` 获取错误信息
- 使用 `try/catch` 处理网络异常等情况
- 在 `finally` 中重置加载状态

## Element Plus 组件使用

项目使用 Element Plus 作为组件库，使用方式如下：

```vue
<template>
  <el-card>
    <template #header>
      <div class="card-header flex-bc">
        <span>标题</span>
        <el-button type="primary">操作</el-button>
      </div>
    </template>
    <div>内容</div>
  </el-card>
</template>

<script setup lang="ts">
import { ElMessage, ElCard, ElButton } from "element-plus";
</script>
```

## 状态管理

对于全局状态，使用 Pinia 进行管理：

```typescript
// src/store/modules/plugin.ts
import { defineStore } from "pinia";
import { getPluginList } from "@/api/plugin";

export const usePluginStore = defineStore("plugin", {
  state: () => ({
    plugins: [],
    loading: false,
  }),
  actions: {
    async fetchPlugins() {
      this.loading = true;
      try {
        const res = await getPluginList();
        if (res.state) {
          this.plugins = res.data;
        }
      } finally {
        this.loading = false;
      }
    }
  }
});
```

## 开发约束规则

在开发过程中，请严格遵循以下约束规则：

### 1. **禁止使用国际化 ❌**
在编写代码过程中，**严禁**使用国际化功能，包括但不限于：
- 不得使用 `$t()` 函数进行文本翻译
- 不得引用国际化配置文件（如 `locales/` 目录下的文件）
- 所有文本内容请直接使用中文硬编码
- 菜单标题、按钮文字、提示信息等均使用中文

```vue
<!-- ❌ 错误：使用国际化 -->
<el-button>{{ $t('common.save') }}</el-button>

<!-- ✅ 正确：直接使用中文 -->
<el-button>保存</el-button>
```

### 2. **禁止修复格式样式错误 ❌**
在开发过程中，**严禁**主动修复任何格式或样式错误，包括：
- 不得修复代码格式问题（缩进、换行等）
- 不得修复CSS样式错误
- 不得调整代码结构或优化代码风格
- 仅实现功能需求，不进行额外的代码整理

### 3. **禁止自动运行项目测试 ❌**
完成代码编写后，**严禁**自动运行任何测试，包括：
- 不得自动执行 `npm test` 或类似命令
- 不得进行功能测试验证
- 不得启动开发服务器进行验证
- 除非用户明确要求，否则不执行任何测试操作


## 开发流程总结

1. 创建路由配置，添加菜单项和国际化文本
2. 创建API文件，定义接口和类型
3. 创建页面文件，实现页面布局和交互逻辑
4. 编写组件样式，优先使用 Tailwind CSS 和主题变量
5. 测试功能，确保所有特性正常工作

## 补充说明

对于开发中遇到的常见问题，可参考项目文档和示例代码。如有疑问，请与团队成员讨论以确保开发一致性。 