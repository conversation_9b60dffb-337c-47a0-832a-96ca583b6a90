FROM openjdk:17-jdk-alpine

RUN apk update && \
    apk add --no-cache \
    tzdata \
    libstdc++ \
    libstdc++6 \
    font-adobe-100dpi \
    ttf-dejavu \
    fontconfig

RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

ENV LANG C.UTF-8
VOLUME /tmp
WORKDIR /home
ADD output/tcs-core-0.0.1-SNAPSHOT.jar /home/<USER>
COPY output/tcs/config /home/<USER>
EXPOSE 9700
ENTRYPOINT ["java", "-Xms4096m", "-Xmx16384m","-jar", "-Dspring.profiles.active=mysql", "-Dspring.config.location=/home/<USER>/ ","-Duser.timezone=Asia/Shanghai","/home/<USER>"]